-- Sadə və işləyən satış veriləri silmə skripti
-- 2025.01.01-ə qədər satış veriləri silir

-- =============================================================================
-- 1. SADƏ SİLMƏ PROSEDURU (KIÇIK VERİ ÜÇÜN)
-- =============================================================================

DELIMITER //

CREATE PROCEDURE SimpleSalesDelete(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'XƏTA BAŞ VERDİ - ƏMƏLIYYAT GERİ ALINDI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    SELECT CONCAT('Silmə başladı: ', cutoff_date) as status;
    
    -- Extension table-ları əvvəl (əgər varsa)
    DELETE FROM tpInvoiceHeaderExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpInvoiceLineExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpOrderHeaderExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpShipmentHeaderExtension WHERE CreatedDate < cutoff_date;
    
    SELECT 'Extension table-ları silindi' as progress;
    
    -- Line table-ları (child table-lar)
    DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trPaymentLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Header table-ları (parent table-lar)
    DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT 'SATIŞ VERİLƏRİ SİLMƏ TAMAMLANDI!' as final_status;
    
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 2. BATCH SİLMƏ PROSEDURU (BÖYÜK VERİ ÜÇÜN)
-- =============================================================================

DELIMITER //

CREATE PROCEDURE BatchSalesDelete(
    IN cutoff_date DATE,
    IN batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'BATCH SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    SELECT CONCAT('Batch silmə başladı: ', cutoff_date, ', Batch size: ', batch_size) as status;
    
    -- trInvoiceLine batch silmə
    SET total_deleted = 0;
    batch_loop_invoice_line: LOOP
        START TRANSACTION;
        DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trInvoiceLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_invoice_line;
        END IF;
        
        SELECT SLEEP(0.1); -- 100ms fasilə
    END LOOP;
    
    -- trOrderLine batch silmə
    SET total_deleted = 0;
    batch_loop_order_line: LOOP
        START TRANSACTION;
        DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trOrderLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_order_line;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    -- trShipmentLine batch silmə
    SET total_deleted = 0;
    batch_loop_shipment_line: LOOP
        START TRANSACTION;
        DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trShipmentLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_shipment_line;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    -- Header table-ları (kiçik batch-larla)
    START TRANSACTION;
    DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    COMMIT;
    
    SELECT 'BATCH SATIŞ SİLMƏ TAMAMLANDI!' as final_status;
END //

DELIMITER ;

-- =============================================================================
-- 3. AUDIT VERİLƏRİNİ SİLMƏK
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteSalesAudit(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'AUDIT SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Satış audit table-ları
    DELETE FROM auInvoiceTrace WHERE OperationDate < cutoff_date;
    SELECT CONCAT('auInvoiceTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM auInvoiceReprintTrace WHERE OperationDate < cutoff_date;
    SELECT CONCAT('auInvoiceReprintTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM auShipmentTrace WHERE OperationDate < cutoff_date;
    SELECT CONCAT('auShipmentTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM auShipmentReprintTrace WHERE OperationDate < cutoff_date;
    SELECT CONCAT('auShipmentReprintTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM auPaymentReprintTrace WHERE DocumentDate < cutoff_date;
    SELECT CONCAT('auPaymentReprintTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM auRetailInvoiceLineChangeTrace WHERE OperationDate < cutoff_date;
    SELECT CONCAT('auRetailInvoiceLineChangeTrace silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT 'SATIŞ AUDIT VERİLƏRİ SİLİNDİ!' as final_status;
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 4. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

/*
-- ƏVVƏL BU QUERY-Nİ İCRA EDİN:
SOURCE simple_sales_check.sql;

-- BACKUP YARADIN:
-- mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine trShipmentHeader trShipmentLine > sales_backup.sql

-- 1. Kiçik test (əvvəl bunu sınayın):
CALL SimpleSalesDelete('2024-01-01'); -- Yalnız 2024-dən əvvəlki veriləri test üçün

-- 2. Audit veriləri silmək (təhlükəsiz):
CALL DeleteSalesAudit('2025-01-01');

-- 3. Kiçik veri üçün sadə silmə:
CALL SimpleSalesDelete('2025-01-01');

-- 4. Böyük veri üçün batch silmə:
CALL BatchSalesDelete('2025-01-01', 1000);

-- 5. Çox böyük veri üçün kiçik batch:
CALL BatchSalesDelete('2025-01-01', 500);
*/

-- =============================================================================
-- 5. SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

DELIMITER //

CREATE PROCEDURE CheckAfterDeletion(IN cutoff_date DATE)
BEGIN
    SELECT 'SİLMƏDƏN SONRA YOXLAMA' as info;
    
    -- Qalan veriləri yoxla
    SELECT 
        'trInvoiceHeader' as table_name,
        COUNT(*) as remaining_records,
        COUNT(CASE WHEN CreatedDate < cutoff_date THEN 1 END) as old_records_remaining
    FROM trInvoiceHeader
    UNION ALL
    SELECT 
        'trInvoiceLine',
        COUNT(*),
        COUNT(CASE WHEN CreatedDate < cutoff_date THEN 1 END)
    FROM trInvoiceLine
    UNION ALL
    SELECT 
        'trOrderHeader',
        COUNT(*),
        COUNT(CASE WHEN CreatedDate < cutoff_date THEN 1 END)
    FROM trOrderHeader
    UNION ALL
    SELECT 
        'trOrderLine',
        COUNT(*),
        COUNT(CASE WHEN CreatedDate < cutoff_date THEN 1 END)
    FROM trOrderLine;
    
    -- Orphan record yoxlaması
    SELECT 'ORPHAN YOXLAMASI' as info;
    
    SELECT 
        'trInvoiceLine orphans' as check_type,
        COUNT(*) as orphan_count
    FROM trInvoiceLine il
    LEFT JOIN trInvoiceHeader ih ON il.InvoiceID = ih.InvoiceID
    WHERE ih.InvoiceID IS NULL;
    
    SELECT 
        'trOrderLine orphans' as check_type,
        COUNT(*) as orphan_count
    FROM trOrderLine ol
    LEFT JOIN trOrderHeader oh ON ol.OrderID = oh.OrderID
    WHERE oh.OrderID IS NULL;
    
    SELECT 'YOXLAMA TAMAMLANDI' as final_status;
END //

DELIMITER ;

-- =============================================================================
-- 6. ORPHAN RECORD-LARI TƏMİZLƏMƏK
-- =============================================================================

DELIMITER //

CREATE PROCEDURE CleanOrphanRecords()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'ORPHAN TƏMİZLƏMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Invoice line orphan-larını təmizlə
    DELETE il FROM trInvoiceLine il
    LEFT JOIN trInvoiceHeader ih ON il.InvoiceID = ih.InvoiceID
    WHERE ih.InvoiceID IS NULL;
    SELECT CONCAT('Invoice line orphans silindi: ', ROW_COUNT()) as progress;
    
    -- Order line orphan-larını təmizlə
    DELETE ol FROM trOrderLine ol
    LEFT JOIN trOrderHeader oh ON ol.OrderID = oh.OrderID
    WHERE oh.OrderID IS NULL;
    SELECT CONCAT('Order line orphans silindi: ', ROW_COUNT()) as progress;
    
    -- Shipment line orphan-larını təmizlə
    DELETE sl FROM trShipmentLine sl
    LEFT JOIN trShipmentHeader sh ON sl.ShipmentID = sh.ShipmentID
    WHERE sh.ShipmentID IS NULL;
    SELECT CONCAT('Shipment line orphans silindi: ', ROW_COUNT()) as progress;
    
    SELECT 'ORPHAN RECORD-LAR TƏMİZLƏNDİ!' as final_status;
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 7. HAZIR KOMANDALAR XÜLASƏSI
-- =============================================================================

/*
ADDIM-ADDIM İCRA PLANI:

1. Yoxlama:
   SOURCE simple_sales_check.sql;

2. Backup:
   mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine > backup.sql

3. Test (kiçik tarix):
   CALL SimpleSalesDelete('2024-01-01');

4. Audit silmə:
   CALL DeleteSalesAudit('2025-01-01');

5. Əsas silmə:
   CALL BatchSalesDelete('2025-01-01', 1000);

6. Yoxlama:
   CALL CheckAfterDeletion('2025-01-01');

7. Orphan təmizləmə (əgər lazımsa):
   CALL CleanOrphanRecords();
*/
