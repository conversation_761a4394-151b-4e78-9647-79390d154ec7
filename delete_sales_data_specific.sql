-- Sizin ERP sistemində SATIŞ VERİLƏRİNİ silmək üçün spesifik skript
-- Parent-Child əlaqələrinə uyğun təhlükəsiz silmə

-- =============================================================================
-- 1. SATIŞ VERİLƏRİ ANALİZİ
-- =============================================================================

-- Sizin sisteminizdə əsas satış table-ları:
-- trInvoiceHeader/trInvoiceLine - Faktura (əsas satış sənədi)
-- trOrderHeader/trOrderLine - Sifariş
-- trShipmentHeader/trShipmentLine - Göndərmə
-- trProposalHeader/trProposalLine - Təklif
-- trReportedSaleHeader - Bildirişli satış
-- trSalesPlan/trSalesPlanChannel/trSalesPlanProduct - Satış planı
-- trPaymentHeader/trPaymentLine - Ödəniş
-- trReserveHeader/trReserveLine - Rezerv

-- =============================================================================
-- 2. SATIŞ VERİLƏRİNİ YOXLAMAQ
-- =============================================================================

-- Əvvəl bu query-ni icra edin ki, nə qədər veri silinəcəyini görəsiniz:
SELECT 
    'SATIŞ VERİLƏRİ STATİSTİKASI' as info,
    'trInvoiceHeader' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete,
    MIN(CreatedDate) as oldest_record,
    MAX(CreatedDate) as newest_record
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1)

UNION ALL

SELECT 
    'SATIŞ VERİLƏRİ STATİSTİKASI',
    'trInvoiceLine',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM trInvoiceLine LIMIT 1)

UNION ALL

SELECT 
    'SATIŞ VERİLƏRİ STATİSTİKASI',
    'trOrderHeader',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1)

UNION ALL

SELECT 
    'SATIŞ VERİLƏRİ STATİSTİKASI',
    'trShipmentHeader',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trShipmentHeader
WHERE EXISTS (SELECT 1 FROM trShipmentHeader LIMIT 1);

-- =============================================================================
-- 3. SATIŞ VERİLƏRİNİ SİLMƏK ÜÇÜN PROSEDUR
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteSalesDataByDate(
    IN cutoff_date DATE,
    IN include_invoices BOOLEAN DEFAULT TRUE,
    IN include_orders BOOLEAN DEFAULT TRUE,
    IN include_shipments BOOLEAN DEFAULT TRUE,
    IN include_payments BOOLEAN DEFAULT TRUE,
    IN batch_size INT DEFAULT 500
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'SATIŞ VERİLƏRİ SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    SELECT 
        'SATIŞ VERİLƏRİ SİLMƏ BAŞLADI' as status,
        cutoff_date as cutoff_date,
        NOW() as start_time;
    
    -- =============================================================================
    -- ADDIM 1: EXTENSION VƏ DETAIL TABLE-LARI (ƏN DƏRİN CHILD-LAR)
    -- =============================================================================
    
    -- Invoice extension və detail table-ları
    IF include_invoices THEN
        DELETE FROM tpInvoiceHeaderExtension WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpInvoiceLineExtension WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpInvoiceHeaderSalesPerson WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpInvoiceUBLExtensions WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpInvoicePassportAndBoardingInfo WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpInvoiceLinePickingDetails WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Invoice extension table-ları silindi' as progress;
    END IF;
    
    -- Order extension table-ları
    IF include_orders THEN
        DELETE FROM tpOrderHeaderExtension WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpOrdersViaInternetInfo WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpOrderDeliveryDetail WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Order extension table-ları silindi' as progress;
    END IF;
    
    -- Shipment extension table-ları
    IF include_shipments THEN
        DELETE FROM tpShipmentHeaderExtension WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpShipmentUBLExtensions WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM tpShipmentLinePickingDetails WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Shipment extension table-ları silindi' as progress;
    END IF;
    
    -- =============================================================================
    -- ADDIM 2: LINE TABLE-LARI (CHILD TABLE-LAR)
    -- =============================================================================
    
    IF include_invoices THEN
        -- Invoice line və əlaqəli table-lar
        DELETE FROM trInvoiceLineBOM WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM trInvoiceLineSum WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM trInvoiceLineSumDetail WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Invoice line table-ları silindi' as progress;
    END IF;
    
    IF include_orders THEN
        -- Order line table-ları
        DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM trOrderAdvancePayments WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Order line table-ları silindi' as progress;
    END IF;
    
    IF include_shipments THEN
        -- Shipment line table-ları
        DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Shipment line table-ları silindi' as progress;
    END IF;
    
    -- Proposal line table-ları
    DELETE FROM trProposalLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    -- Reserve line table-ları
    DELETE FROM trReserveLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    -- Sales plan detail table-ları
    DELETE FROM trSalesPlanProductQty WHERE CreatedDate < cutoff_date LIMIT batch_size;
    DELETE FROM trSalesPlanProduct WHERE CreatedDate < cutoff_date LIMIT batch_size;
    DELETE FROM trSalesPlanChannel WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    IF include_payments THEN
        -- Payment line table-ları
        DELETE FROM trPaymentLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Payment line table-ları silindi' as progress;
    END IF;
    
    -- =============================================================================
    -- ADDIM 3: HEADER TABLE-LARI (PARENT TABLE-LAR)
    -- =============================================================================
    
    IF include_invoices THEN
        -- Invoice header
        DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Invoice header table-ı silindi' as progress;
    END IF;
    
    IF include_orders THEN
        -- Order header
        DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
        DELETE FROM trOrderAsnHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Order header table-ları silindi' as progress;
    END IF;
    
    IF include_shipments THEN
        -- Shipment header
        DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Shipment header table-ı silindi' as progress;
    END IF;
    
    -- Proposal header
    DELETE FROM trProposalHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    -- Reserve header
    DELETE FROM trReserveHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    -- Sales plan header
    DELETE FROM trSalesPlan WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    -- Reported sale header
    DELETE FROM trReportedSaleHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
    
    IF include_payments THEN
        -- Payment header
        DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        SELECT 'Payment header table-ı silindi' as progress;
    END IF;
    
    SELECT 
        'SATIŞ VERİLƏRİ SİLMƏ TAMAMLANDI' as final_status,
        NOW() as end_time;
    
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 4. SATIŞ AUDIT VERİLƏRİNİ SİLMƏK
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteSalesAuditData(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'SATIŞ AUDIT SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Satışla əlaqəli audit table-ları
    DELETE FROM auInvoiceTrace WHERE OperationDate < cutoff_date;
    DELETE FROM auInvoiceReprintTrace WHERE OperationDate < cutoff_date;
    DELETE FROM auShipmentTrace WHERE OperationDate < cutoff_date;
    DELETE FROM auShipmentReprintTrace WHERE OperationDate < cutoff_date;
    DELETE FROM auPaymentReprintTrace WHERE DocumentDate < cutoff_date;
    DELETE FROM auRetailInvoiceLineChangeTrace WHERE OperationDate < cutoff_date;
    DELETE FROM auCancelRetailTransactions WHERE CreatedDate < cutoff_date;
    DELETE FROM auMergeRetailCustomerTrace WHERE OperationDate < cutoff_date;
    
    -- Service log-ları (satışla əlaqəli)
    DELETE FROM auMobileStorePaymentLog WHERE CreatedDate < cutoff_date;
    DELETE FROM auInvoiceDufrySendStatus WHERE CreatedDate < cutoff_date;
    DELETE FROM auInvoiceUnifreeSendStatus WHERE CreatedDate < cutoff_date;
    DELETE FROM auInvoiceTsmPaymentInfo WHERE CreatedDate < cutoff_date;
    DELETE FROM auInvoiceTsmTransactionInfo WHERE CreatedDate < cutoff_date;
    
    SELECT 'SATIŞ AUDIT VERİLƏRİ SİLİNDİ' as status;
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 5. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

-- Əvvəl audit veriləri silin (təhlükəsiz):
-- CALL DeleteSalesAuditData('2025-01-01');

-- Sonra əsas satış veriləri silin:
-- CALL DeleteSalesDataByDate('2025-01-01', TRUE, TRUE, TRUE, TRUE, 500);

-- Yalnız faktura veriləri silmək:
-- CALL DeleteSalesDataByDate('2025-01-01', TRUE, FALSE, FALSE, FALSE, 500);

-- Yalnız sifarişlər silmək:
-- CALL DeleteSalesDataByDate('2025-01-01', FALSE, TRUE, FALSE, FALSE, 500);

-- =============================================================================
-- 6. BACKUP VƏ BƏRPA
-- =============================================================================

-- Satış veriləri üçün spesifik backup:
-- mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine trShipmentHeader trShipmentLine > sales_backup_$(date +%Y%m%d).sql

-- =============================================================================
-- 7. SON YOXLAMALAR
-- =============================================================================

-- Silmədən sonra bu query-ni icra edin:
SELECT 
    'SİLMƏ SONRASI YOXLAMA' as info,
    'trInvoiceHeader' as table_name,
    COUNT(*) as remaining_records
FROM trInvoiceHeader
UNION ALL
SELECT 
    'SİLMƏ SONRASI YOXLAMA',
    'trOrderHeader',
    COUNT(*)
FROM trOrderHeader
UNION ALL
SELECT 
    'SİLMƏ SONRASI YOXLAMA',
    'trShipmentHeader',
    COUNT(*)
FROM trShipmentHeader;

-- Foreign key constraint pozulmalarını yoxlayın:
-- Bu query heç bir nəticə qaytarmamalıdır
SELECT 'ORPHAN YOXLAMASI' as check_type, COUNT(*) as orphan_count
FROM trInvoiceLine il
LEFT JOIN trInvoiceHeader ih ON il.InvoiceID = ih.InvoiceID
WHERE ih.InvoiceID IS NULL;
