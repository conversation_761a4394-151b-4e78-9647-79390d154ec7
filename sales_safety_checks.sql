-- Satış veriləri silmədən ƏVVƏL mütləq icra edilməli təhlükəsizlik yoxlamaları
-- Bu skript sizə nə qədər veri silinəcəyini və potensial riskləri göstərəcək

-- =============================================================================
-- 1. SATIŞ VERİLƏRİ ÜMUMI STATİSTİKA
-- =============================================================================

SELECT 
    '=== SATIŞ VERİLƏRİ ÜMUMI STATİSTİKA ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Ən böyük satış table-ların<PERSON>n ölçüsü
SELECT 
    'TABLE ÖLÇÜSÜ' as section,
    table_name as info,
    CONCAT(table_rows, ' records') as value,
    CONCAT(ROUND(((data_length + index_length) / 1024 / 1024), 2), ' MB') as percentage
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND (table_name LIKE 'trInvoice%' OR table_name LIKE 'trOrder%' OR table_name LIKE 'trShipment%' OR table_name LIKE 'trPayment%')
ORDER BY (data_length + index_length) DESC;

-- =============================================================================
-- 2. TARİXƏ GÖRƏ VERİ PAYLANMASI ANALİZİ
-- =============================================================================

SELECT 
    '=== TARİXƏ GÖRƏ VERİ PAYLANMASI ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- trInvoiceHeader analizi
SELECT 
    'FAKTURA ANALİZİ' as section,
    'trInvoiceHeader' as info,
    CONCAT(
        COUNT(*), ' total, ',
        COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' silinəcək'
    ) as value,
    CONCAT(
        ROUND(COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) * 100.0 / COUNT(*), 2), '%'
    ) as percentage
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1);

-- trInvoiceLine analizi
SELECT 
    'FAKTURA SƏTIR ANALİZİ' as section,
    'trInvoiceLine' as info,
    CONCAT(
        COUNT(*), ' total, ',
        COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' silinəcək'
    ) as value,
    CONCAT(
        ROUND(COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) * 100.0 / COUNT(*), 2), '%'
    ) as percentage
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM trInvoiceLine LIMIT 1);

-- trOrderHeader analizi
SELECT 
    'SİFARİŞ ANALİZİ' as section,
    'trOrderHeader' as info,
    CONCAT(
        COUNT(*), ' total, ',
        COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' silinəcək'
    ) as value,
    CONCAT(
        ROUND(COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) * 100.0 / COUNT(*), 2), '%'
    ) as percentage
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1);

-- trShipmentHeader analizi
SELECT 
    'GÖNDƏRMƏ ANALİZİ' as section,
    'trShipmentHeader' as info,
    CONCAT(
        COUNT(*), ' total, ',
        COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' silinəcək'
    ) as value,
    CONCAT(
        ROUND(COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) * 100.0 / COUNT(*), 2), '%'
    ) as percentage
FROM trShipmentHeader
WHERE EXISTS (SELECT 1 FROM trShipmentHeader LIMIT 1);

-- =============================================================================
-- 3. MƏBLƏĞ VƏ MALIYYƏ ANALİZİ
-- =============================================================================

SELECT 
    '=== MALIYYƏ TƏSİRİ ANALİZİ ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Silinəcək fakturaların ümumi məbləği (əgər Amount sütunu varsa)
SELECT 
    'MALIYYƏ TƏSİRİ' as section,
    'Silinəcək faktura məbləği' as info,
    CONCAT(
        COALESCE(SUM(CASE WHEN CreatedDate < '2025-01-01' THEN NetAmount END), 0), ' AZN'
    ) as value,
    CONCAT(
        COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' faktura'
    ) as percentage
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader WHERE NetAmount IS NOT NULL LIMIT 1);

-- =============================================================================
-- 4. FOREIGN KEY CONSTRAINT YOXLAMALARİ
-- =============================================================================

SELECT 
    '=== FOREIGN KEY CONSTRAINT-LƏR ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Satış table-ları arasında constraint-lər
SELECT 
    'FK CONSTRAINT' as section,
    CONCAT(TABLE_NAME, ' -> ', REFERENCED_TABLE_NAME) as info,
    CONSTRAINT_NAME as value,
    CONCAT(COLUMN_NAME, ' -> ', REFERENCED_COLUMN_NAME) as percentage
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL
AND (TABLE_NAME LIKE 'tr%' AND REFERENCED_TABLE_NAME LIKE 'tr%')
AND (TABLE_NAME LIKE '%Invoice%' OR TABLE_NAME LIKE '%Order%' OR TABLE_NAME LIKE '%Shipment%' OR TABLE_NAME LIKE '%Payment%')
ORDER BY REFERENCED_TABLE_NAME, TABLE_NAME;

-- =============================================================================
-- 5. AKTİV TRANSACTION-LAR VƏ LOCK-LAR
-- =============================================================================

SELECT 
    '=== AKTİV TRANSACTION-LAR ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Hazırda işləyən satış əlaqəli query-lər
SELECT 
    'AKTİV QUERY-LƏR' as section,
    CONCAT('Process ID: ', ID) as info,
    CONCAT('User: ', USER, ', Time: ', TIME, 's') as value,
    LEFT(COALESCE(INFO, 'NULL'), 50) as percentage
FROM INFORMATION_SCHEMA.PROCESSLIST
WHERE DB = DATABASE()
AND (INFO LIKE '%trInvoice%' OR INFO LIKE '%trOrder%' OR INFO LIKE '%trShipment%' OR INFO LIKE '%trPayment%')
AND COMMAND != 'Sleep';

-- =============================================================================
-- 6. SON SATIŞ FƏALİYYƏTİ ANALİZİ
-- =============================================================================

SELECT 
    '=== SON SATIŞ FƏALİYYƏTİ ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Son 7 gündə yaradılmış satış veriləri
SELECT 
    'SON 7 GÜN' as section,
    'trInvoiceHeader' as info,
    COUNT(CASE WHEN CreatedDate >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as value,
    'yeni faktura' as percentage
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1)

UNION ALL

SELECT 
    'SON 7 GÜN',
    'trOrderHeader',
    COUNT(CASE WHEN CreatedDate >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END),
    'yeni sifariş'
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1);

-- Bugün yaradılmış satış veriləri
SELECT 
    'BUGÜN' as section,
    'trInvoiceHeader' as info,
    COUNT(CASE WHEN DATE(CreatedDate) = CURDATE() THEN 1 END) as value,
    'bugünkü faktura' as percentage
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1)

UNION ALL

SELECT 
    'BUGÜN',
    'trOrderHeader',
    COUNT(CASE WHEN DATE(CreatedDate) = CURDATE() THEN 1 END),
    'bugünkü sifariş'
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1);

-- =============================================================================
-- 7. BACKUP YOXLAMASI
-- =============================================================================

SELECT 
    '=== BACKUP YOXLAMASI ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

SELECT 
    'BACKUP XƏBƏRDARLIQ' as section,
    'Bu əməliyyatdan əvvəl backup yaradın!' as info,
    'mysqldump -u user -p db > sales_backup.sql' as value,
    'MÜTLƏQ LAZIMDIR!' as percentage;

-- =============================================================================
-- 8. RİSK QİYMƏTLƏNDİRMƏSİ
-- =============================================================================

SELECT 
    '=== RİSK QİYMƏTLƏNDİRMƏSİ ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

-- Yüksək risk faktoru hesablama
SELECT 
    'RİSK SEVİYYƏSİ' as section,
    CASE 
        WHEN (
            (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') > 10000 OR
            (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') > 50000
        ) THEN 'YÜKSƏK RİSK'
        WHEN (
            (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') > 1000 OR
            (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') > 5000
        ) THEN 'ORTA RİSK'
        ELSE 'AŞAĞI RİSK'
    END as info,
    'Silinəcək veri miqdarına əsasən' as value,
    'Test environment-də sınayın!' as percentage;

-- =============================================================================
-- 9. TÖVSİYƏLƏR
-- =============================================================================

SELECT 
    '=== TÖVSİYƏLƏR ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

SELECT 
    'TÖVSİYƏ 1' as section,
    'Əvvəl audit veriləri silin' as info,
    'CALL DeleteSalesAuditData(cutoff_date)' as value,
    'Təhlükəsiz başlanğıc' as percentage

UNION ALL

SELECT 
    'TÖVSİYƏ 2',
    'Kiçik batch ilə test edin',
    'CALL BatchDeleteSalesData(cutoff_date, 100, 1.0, 5)',
    'İlk test üçün'

UNION ALL

SELECT 
    'TÖVSİYƏ 3',
    'Off-peak saatlarda icra edin',
    'Gecə 02:00 - 05:00 arası',
    'Minimum təsir'

UNION ALL

SELECT 
    'TÖVSİYƏ 4',
    'Progress monitor edin',
    'SELECT * FROM sales_deletion_log',
    'Real-time izləmə'

UNION ALL

SELECT 
    'TÖVSİYƏ 5',
    'Emergency stop hazır olsun',
    'CALL StopSalesDeletion()',
    'Təcili dayandırma';

-- =============================================================================
-- 10. SON XƏBƏRDARLIQ
-- =============================================================================

SELECT 
    '=== SON XƏBƏRDARLIQ ===' as section,
    '' as info,
    '' as value,
    '' as percentage;

SELECT 
    'DİQQƏT!' as section,
    'Bu əməliyyat geri alına bilməz!' as info,
    'Backup yaradın və test edin!' as value,
    'MÜTLƏQ!' as percentage;
