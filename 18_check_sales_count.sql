-- SATIŞ VERİLƏRİNİN SAYI VƏ STATİSTİKASI
-- Silmədən əvvəl satış məlumatlarının sayını yoxlayır

-- =============================================================================
-- SATIŞ CƏDVƏL VƏ RECORD SAYLARINI YOXLAMAQ
-- =============================================================================

PRINT '📊 SATIŞ VERİLƏRİNİN SAYI YOXLANIR...';
PRINT '';

-- =============================================================================
-- 1. FAKTURA VERİLƏRİ (INVOICE)
-- =============================================================================

PRINT '🧾 FAKTURA VERİLƏRİ:';

SELECT 
    'FAKTURA HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceHeader')

UNION ALL

SELECT 
    'FAKTURA LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceLine');

PRINT '';

-- =============================================================================
-- 2. SİFARİŞ VERİLƏRİ (ORDER)
-- =============================================================================

PRINT '📋 SİFARİŞ VERİLƏRİ:';

SELECT 
    'SİFARİŞ HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderHeader')

UNION ALL

SELECT 
    'SİFARİŞ LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trOrderLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderLine');

PRINT '';

-- =============================================================================
-- 3. GÖNDƏRMƏ VERİLƏRİ (SHIPMENT)
-- =============================================================================

PRINT '📦 GÖNDƏRMƏ VERİLƏRİ:';

SELECT 
    'GÖNDƏRMƏ HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trShipmentHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentHeader')

UNION ALL

SELECT 
    'GÖNDƏRMƏ LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trShipmentLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentLine');

PRINT '';

-- =============================================================================
-- 4. ÖDƏNİŞ VERİLƏRİ (PAYMENT)
-- =============================================================================

PRINT '💰 ÖDƏNİŞ VERİLƏRİ:';

SELECT 
    'ÖDƏNİŞ HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trPaymentHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentHeader')

UNION ALL

SELECT 
    'ÖDƏNİŞ LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trPaymentLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentLine');

PRINT '';

-- =============================================================================
-- 5. BANK VERİLƏRİ
-- =============================================================================

PRINT '🏦 BANK VERİLƏRİ:';

SELECT 
    'BANK HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trBankHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankHeader')

UNION ALL

SELECT 
    'BANK LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trBankLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankLine');

PRINT '';

-- =============================================================================
-- 6. NAĞD PUL VERİLƏRİ
-- =============================================================================

PRINT '💵 NAĞD PUL VERİLƏRİ:';

SELECT 
    'NAĞD HEADER' as table_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END) as records_2025,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_before_2025,
    MIN(CreatedDate) as earliest_date,
    MAX(CreatedDate) as latest_date
FROM trCashHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashHeader')

UNION ALL

SELECT 
    'NAĞD LINE',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate >= '2025-01-01' THEN 1 END),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    MIN(CreatedDate),
    MAX(CreatedDate)
FROM trCashLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashLine');

PRINT '';

-- =============================================================================
-- 7. ÜMUMI XÜLASƏ
-- =============================================================================

PRINT '📊 ÜMUMI SATIŞ STATİSTİKASI:';
PRINT '';

DECLARE @TotalSalesRecords INT = 0;
DECLARE @SalesRecords2025 INT = 0;
DECLARE @SalesRecordsBefore2025 INT = 0;

-- Ümumi satış record sayı
SELECT 
    @TotalSalesRecords = 
        ISNULL((SELECT COUNT(*) FROM trInvoiceHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trOrderHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trShipmentHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trPaymentHeader), 0),
    @SalesRecords2025 = 
        ISNULL((SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate >= '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate >= '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trShipmentHeader WHERE CreatedDate >= '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trPaymentHeader WHERE CreatedDate >= '2025-01-01'), 0),
    @SalesRecordsBefore2025 = 
        ISNULL((SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trShipmentHeader WHERE CreatedDate < '2025-01-01'), 0) +
        ISNULL((SELECT COUNT(*) FROM trPaymentHeader WHERE CreatedDate < '2025-01-01'), 0);

SELECT 
    'ÜMUMI SATIŞ XÜLASƏSI' as info,
    @TotalSalesRecords as total_sales_records,
    @SalesRecords2025 as sales_2025,
    @SalesRecordsBefore2025 as sales_before_2025,
    CAST(ROUND((@SalesRecordsBefore2025 * 100.0 / NULLIF(@TotalSalesRecords, 0)), 2) AS DECIMAL(5,2)) as percent_before_2025;

PRINT '';
PRINT '🎯 NƏTİCƏ:';
PRINT '   Ümumi satış records: ' + CAST(@TotalSalesRecords AS VARCHAR(10));
PRINT '   2025-ci il: ' + CAST(@SalesRecords2025 AS VARCHAR(10));
PRINT '   2025-dən əvvəl: ' + CAST(@SalesRecordsBefore2025 AS VARCHAR(10));
PRINT '';

IF @SalesRecordsBefore2025 > 0
BEGIN
    PRINT '⚠️ SİLİNƏCƏK VERİLƏR:';
    PRINT '   2025-01-01-dən əvvəlki ' + CAST(@SalesRecordsBefore2025 AS VARCHAR(10)) + ' satış record-u silinəcək';
    PRINT '   Bu, ümumi satış verilərinin %' + CAST(ROUND((@SalesRecordsBefore2025 * 100.0 / NULLIF(@TotalSalesRecords, 0)), 1) AS VARCHAR(10)) + '-ni təşkil edir';
END
ELSE
BEGIN
    PRINT '✅ 2025-01-01-dən əvvəl satış verisi yoxdur';
END

PRINT '';
PRINT '📋 SONRAKI ADDIM:';
PRINT '   Əgər bu rəqəmlər düzgündürsə, 17_safe_delete_transaction_keep_master.sql icra edin';
