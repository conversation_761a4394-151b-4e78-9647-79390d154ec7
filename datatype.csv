﻿TableName;ColumnName;DataType
auAirConnTransactionInfo;CreatedDate;datetime
auAirConnTransactionInfo;LastUpdatedDate;datetime
auAirConnTransactionInfo;ShiftOpenTime;datetime
auAirConnZInfo;CreatedDate;datetime
auAirConnZInfo;LastUpdatedDate;datetime
auAirConnZInfo;OperationDate;date
auAllocationTrace;OperationDate;date
auAngolaSVCIntegrationInfo;CreatedDate;datetime
auAngolaSVCIntegrationInfo;LastUpdatedDate;datetime
auBankPermit;CreatedDate;datetime
auBankPermit;DeleteMaxDate;date
auBankPermit;DeleteMinDate;date
auBankPermit;InsertMaxDate;date
auBankPermit;InsertMinDate;date
auBankPermit;LastUpdatedDate;datetime
auBankPermit;UpdateMaxDate;date
auBankPermit;UpdateMinDate;date
auBasefyServiceLog;CreatedDate;datetime
auBasefyServiceLog;LastUpdatedDate;datetime
auBasePricePermit;CreatedDate;datetime
auBasePricePermit;LastUpdatedDate;datetime
auCancelRetailTransactions;CreatedDate;datetime
auCancelRetailTransactions;LastUpdatedDate;datetime
auCancelRetailTransactions;TransactionDate;date
auCardColumnPermit;CreatedDate;datetime
auCardColumnPermit;LastUpdatedDate;datetime
auCardElementPermit;CreatedDate;datetime
auCardElementPermit;LastUpdatedDate;datetime
auCardElementRequiredKey;CreatedDate;datetime
auCardElementRequiredKey;LastUpdatedDate;datetime
auCardPermit;CreatedDate;datetime
auCardPermit;LastUpdatedDate;datetime
auCashPermit;CreatedDate;datetime
auCashPermit;DeleteMaxDate;date
auCashPermit;DeleteMinDate;date
auCashPermit;InsertMaxDate;date
auCashPermit;InsertMinDate;date
auCashPermit;LastUpdatedDate;datetime
auCashPermit;UpdateMaxDate;date
auCashPermit;UpdateMinDate;date
auChequeDeny;CreatedDate;datetime
auChequeDeny;LastUpdatedDate;datetime
auChequeDenyTrace;OperationDate;date
auChequePermit;CreatedDate;datetime
auChequePermit;DeleteMaxDate;date
auChequePermit;DeleteMinDate;date
auChequePermit;InsertMaxDate;date
auChequePermit;InsertMinDate;date
auChequePermit;LastUpdatedDate;datetime
auChequePermit;UpdateMaxDate;date
auChequePermit;UpdateMinDate;date
auCreditCardPaymentPermit;CreatedDate;datetime
auCreditCardPaymentPermit;DeleteMaxDate;date
auCreditCardPaymentPermit;DeleteMinDate;date
auCreditCardPaymentPermit;InsertMaxDate;date
auCreditCardPaymentPermit;InsertMinDate;date
auCreditCardPaymentPermit;LastUpdatedDate;datetime
auCreditCardPaymentPermit;UpdateMaxDate;date
auCreditCardPaymentPermit;UpdateMinDate;date
auCurrAccBadDebtStatusTrace;OperationDate;date
auCustomerCompanyBrandAttributeTrace;OperationDate;date
auCustomizedDiscountEngineServiceGetReturnableItemsLog;CreatedDate;datetime
auCustomizedDiscountEngineServiceGetReturnableItemsLog;LastUpdatedDate;datetime
auCustomizedDiscountEngineServiceLog;CreatedDate;datetime
auCustomizedDiscountEngineServiceLog;LastUpdatedDate;datetime
auCustomQ3XFFPTransactionInfo;CreatedDate;datetime
auCustomQ3XFFPTransactionInfo;DocumentDateTime;datetime
auCustomQ3XFFPTransactionInfo;LastUpdatedDate;datetime
auCustomsServiceLog;CreatedDate;datetime
auCustomsServiceLog;LastUpdatedDate;datetime
auCustomTablePermit;CreatedDate;datetime
auCustomTablePermit;LastUpdatedDate;datetime
auDataTransferLog;EventDateTime;datetime
auDataTransferMissingRecordsTrace;OperationDate;date
auDataTransferTrace;OperationDate;date
auDebitPermit;CreatedDate;datetime
auDebitPermit;DeleteMaxDate;date
auDebitPermit;DeleteMinDate;date
auDebitPermit;InsertMaxDate;date
auDebitPermit;InsertMinDate;date
auDebitPermit;LastUpdatedDate;datetime
auDebitPermit;UpdateMaxDate;date
auDebitPermit;UpdateMinDate;date
auDunadPFRTransactionInfo;CreatedDate;datetime
auDunadPFRTransactionInfo;LastUpdatedDate;datetime
auETTNGuuduuTransactionInfo;CreatedDate;datetime
auETTNGuuduuTransactionInfo;LastUpdatedDate;datetime
auEuromsgSentAccountTrace;OperationDate;date
auExpenseSlipPermit;CreatedDate;datetime
auExpenseSlipPermit;DeleteMaxDate;date
auExpenseSlipPermit;DeleteMinDate;date
auExpenseSlipPermit;InsertMaxDate;date
auExpenseSlipPermit;InsertMinDate;date
auExpenseSlipPermit;LastUpdatedDate;datetime
auExpenseSlipPermit;UpdateMaxDate;date
auExpenseSlipPermit;UpdateMinDate;date
auFastPayServiceLog;CreatedDate;datetime
auFastPayServiceLog;LastUpdatedDate;datetime
auFiscalPrinterPaymentInfo;CreatedDate;datetime
auFiscalPrinterPaymentInfo;LastUpdatedDate;datetime
auFixEInvoiceStatusCodes;CreatedDate;datetime
auFixEInvoiceStatusCodes;LastUpdatedDate;datetime
auFixEInvoiceStatusCodes;OperationDate;date
auFixPaymentTrace;InvoiceDate;date
auFixPaymentTrace;OperationDate;date
auGettingDataTransferTraceHeader;CreatedDate;datetime
auGettingDataTransferTraceHeader;LastUpdatedDate;datetime
auGettingDataTransferTraceHeader;OperationBegginingDate;date
auGettingDataTransferTraceHeader;OperationEndingDate;date
auGettingDataTransferTraceLine;CreatedDate;datetime
auGettingDataTransferTraceLine;LastUpdatedDate;datetime
auGlobalDefaultTrace;OperationDate;date
auInnerProcessPermit;CreatedDate;datetime
auInnerProcessPermit;DeleteMaxDate;date
auInnerProcessPermit;DeleteMinDate;date
auInnerProcessPermit;InsertMaxDate;date
auInnerProcessPermit;InsertMinDate;date
auInnerProcessPermit;LastUpdatedDate;datetime
auInnerProcessPermit;UpdateMaxDate;date
auInnerProcessPermit;UpdateMinDate;date
auInnerTrace;DocumentDate;date
auInnerTrace;OperationDate;date
auInteractiveSMSTrace;MsgDate;datetime
auInteractiveSMSTrace;OperationDate;date
auInvoiceDufrySendStatus;CreatedDate;datetime
auInvoiceDufrySendStatus;LastUpdatedDate;datetime
auInvoiceReprintTrace;InvoiceDate;date
auInvoiceReprintTrace;OperationDate;date
auInvoiceReprintTrace;ReprintDate;date
auInvoiceTrace;InvoiceDate;date
auInvoiceTrace;OperationDate;date
auInvoiceTsmPaymentInfo;CreatedDate;datetime
auInvoiceTsmPaymentInfo;LastUpdatedDate;datetime
auInvoiceTsmTransactionInfo;CreatedDate;datetime
auInvoiceTsmTransactionInfo;LastUpdatedDate;datetime
auInvoiceUnifreeSendStatus;CreatedDate;datetime
auInvoiceUnifreeSendStatus;LastUpdatedDate;datetime
auItemCopyTrace;OperationDate;date
auItemTest;CreatedDate;datetime
auItemTest;DeleteMaxDate;date
auItemTest;DeleteMinDate;date
auItemTest;InsertMaxDate;date
auItemTest;InsertMinDate;date
auItemTest;LastUpdatedDate;datetime
auItemTest;UpdateMaxDate;date
auItemTest;UpdateMinDate;date
auIyzicoServiceLog;CreatedDate;datetime
auIyzicoServiceLog;LastUpdatedDate;datetime
auJournalPermit;CreatedDate;datetime
auJournalPermit;DeleteMaxDate;date
auJournalPermit;DeleteMinDate;date
auJournalPermit;InsertMaxDate;date
auJournalPermit;InsertMinDate;date
auJournalPermit;LastUpdatedDate;datetime
auJournalPermit;UpdateMaxDate;date
auJournalPermit;UpdateMinDate;date
auMacellanSuperappServiceLog;CreatedDate;datetime
auMacellanSuperappServiceLog;LastUpdatedDate;datetime
auMarketPlaceServiceTrace;OperationEndDate;date
auMarketPlaceServiceTrace;OperationStartDate;date
auMergeRetailCustomerTrace;OperationDate;date
auMernisQueryTrace;OperationDate;date
auMobilDevServiceLog;CreatedDate;datetime
auMobilDevServiceLog;LastUpdatedDate;datetime
auMobileStorePaymentLog;CreatedDate;datetime
auMobileStorePaymentLog;LastUpdatedDate;datetime
auMobileStorePermit;CreatedDate;datetime
auMobileStorePermit;LastUpdatedDate;datetime
auMobilRevenueReportPermit;CreatedDate;datetime
auMobilRevenueReportPermit;LastUpdatedDate;datetime
auN2AnimaServiceLog;CreatedDate;datetime
auN2AnimaServiceLog;LastUpdatedDate;datetime
auOpenDrawerWithNoSaleTrace;OpenDate;date
auOptInOptOutTrace;ConsentTime;datetime
auOptInOptOutTrace;OperationDate;datetime
auOptInOptOutTrace;SentDate;datetime
auPaymentPermit;CreatedDate;datetime
auPaymentPermit;DeleteMaxDate;date
auPaymentPermit;DeleteMinDate;date
auPaymentPermit;InsertMaxDate;date
auPaymentPermit;InsertMinDate;date
auPaymentPermit;LastUpdatedDate;datetime
auPaymentPermit;UpdateMaxDate;date
auPaymentPermit;UpdateMinDate;date
auPaymentReprintTrace;DocumentDate;date
auPaymentReprintTrace;ReprintDate;date
auPaynetServiceLog;CreatedDate;datetime
auPaynetServiceLog;LastUpdatedDate;datetime
auPlanetPaymentServiceLog;CreatedDate;datetime
auPlanetPaymentServiceLog;LastUpdatedDate;datetime
auPosTerminalLoginTrace;OperationDate;date
auPriceListPermit;CreatedDate;datetime
auPriceListPermit;DeleteMaxDate;date
auPriceListPermit;DeleteMinDate;date
auPriceListPermit;InsertMaxDate;date
auPriceListPermit;InsertMinDate;date
auPriceListPermit;LastUpdatedDate;datetime
auPriceListPermit;UpdateMaxDate;date
auPriceListPermit;UpdateMinDate;date
auProcessFlowDeny;CreatedDate;datetime
auProcessFlowDeny;LastUpdatedDate;datetime
auProcessFlowDenyTrace;OperationDate;date
auProcessPermit;CreatedDate;datetime
auProcessPermit;DeleteMaxDate;date
auProcessPermit;DeleteMinDate;date
auProcessPermit;InsertMaxDate;date
auProcessPermit;InsertMinDate;date
auProcessPermit;LastUpdatedDate;datetime
auProcessPermit;UpdateMaxDate;date
auProcessPermit;UpdateMinDate;date
auProformaProcessPermit;CreatedDate;datetime
auProformaProcessPermit;DeleteMaxDate;date
auProformaProcessPermit;DeleteMinDate;date
auProformaProcessPermit;InsertMaxDate;date
auProformaProcessPermit;InsertMinDate;date
auProformaProcessPermit;LastUpdatedDate;datetime
auProformaProcessPermit;UpdateMaxDate;date
auProformaProcessPermit;UpdateMinDate;date
auProgramPermit;CreatedDate;datetime
auProgramPermit;LastUpdatedDate;datetime
auProgramUseTrace;EndDate;datetime
auProgramUseTrace;StartDate;datetime
auProposalLineRevisionTrace;OperationDate;date
auPurchaseRequisitionLineRevisionTrace;OperationDate;date
auPurchaseRequisitionPermit;CreatedDate;datetime
auPurchaseRequisitionPermit;DeleteMaxDate;date
auPurchaseRequisitionPermit;DeleteMinDate;date
auPurchaseRequisitionPermit;InsertMaxDate;date
auPurchaseRequisitionPermit;InsertMinDate;date
auPurchaseRequisitionPermit;LastUpdatedDate;datetime
auPurchaseRequisitionPermit;UpdateMaxDate;date
auPurchaseRequisitionPermit;UpdateMinDate;date
auPurchaseRequisitionProposalPermit;CreatedDate;datetime
auPurchaseRequisitionProposalPermit;DeleteMaxDate;date
auPurchaseRequisitionProposalPermit;DeleteMinDate;date
auPurchaseRequisitionProposalPermit;InsertMaxDate;date
auPurchaseRequisitionProposalPermit;InsertMinDate;date
auPurchaseRequisitionProposalPermit;LastUpdatedDate;datetime
auPurchaseRequisitionProposalPermit;UpdateMaxDate;date
auPurchaseRequisitionProposalPermit;UpdateMinDate;date
auPurchaseRequisitionProposalRevisionTrace;OperationDate;date
auReconciliationReportNotificationTrace;OperationDate;datetime
auReportFilterMinMaxDateValue;CreatedDate;datetime
auReportFilterMinMaxDateValue;LastUpdatedDate;datetime
auReportFilterMinMaxDateValue;MaxDate;date
auReportFilterMinMaxDateValue;MinDate;date
auReportQueryPermit;CreatedDate;datetime
auReportQueryPermit;LastUpdatedDate;datetime
auRetailCustomerIdentitySharingSystemTrace;CreatedDate;datetime
auRetailCustomerIdentitySharingSystemTrace;LastUpdatedDate;datetime
auRetailInvoiceLineChangeTrace;InvoiceDate;date
auRetailInvoiceLineChangeTrace;OperationDate;date
auSeaBoxServiceLog;CreatedDate;datetime
auSeaBoxServiceLog;LastUpdatedDate;datetime
auSendingDataTransferTrace;CreatedDate;datetime
auSendingDataTransferTrace;LastUpdatedDate;datetime
auSendingDataTransferTrace;OperationBegginingDate;date
auSendingDataTransferTrace;OperationEndingDate;date
auShipmentReprintTrace;OperationDate;date
auShipmentReprintTrace;ReprintDate;date
auShipmentReprintTrace;ShippingDate;date
auShipmentTrace;OperationDate;date
auShipmentTrace;ShippingDate;date
auSoftekESIRLinkTransactionInfo;CreatedDate;datetime
auSoftekESIRLinkTransactionInfo;LastUpdatedDate;datetime
auSupportRequest;CreatedDate;datetime
auSupportRequest;DeleteMaxDate;date
auSupportRequest;DeleteMinDate;date
auSupportRequest;InsertMaxDate;date
auSupportRequest;InsertMinDate;date
auSupportRequest;LastUpdatedDate;datetime
auSupportRequest;UpdateMaxDate;date
auSupportRequest;UpdateMinDate;date
auSurveyPermit;CreatedDate;datetime
auSurveyPermit;DeleteMaxDate;date
auSurveyPermit;DeleteMinDate;date
auSurveyPermit;InsertMaxDate;date
auSurveyPermit;InsertMinDate;date
auSurveyPermit;LastUpdatedDate;datetime
auSurveyPermit;UpdateMaxDate;date
auSurveyPermit;UpdateMinDate;date
auSurveySectionPermit;CreatedDate;datetime
auSurveySectionPermit;LastUpdatedDate;datetime
auTransactionCheckInOutTrace;CheckInDate;smalldatetime
auTransactionCheckInOutTrace;CheckOutDate;smalldatetime
auTransferPlanTrace;OperationDate;date
auTRAVFDTransactionInfo;CreatedDate;datetime
auTRAVFDTransactionInfo;idate;date
auTRAVFDTransactionInfo;LastUpdatedDate;datetime
auTRINGT202TransactionInfo;CreatedDate;datetime
auTRINGT202TransactionInfo;DocumentDateTime;datetime
auTRINGT202TransactionInfo;LastUpdatedDate;datetime
auTsmIntegratorPaymentInfo;CreatedDate;datetime
auTsmIntegratorPaymentInfo;LastUpdatedDate;datetime
auTsmPosPaymentInfo;CreatedDate;datetime
auTsmPosPaymentInfo;LastUpdatedDate;datetime
auTsmTransactionInfo;CreatedDate;datetime
auTsmTransactionInfo;DocumentDate;date
auTsmTransactionInfo;LastUpdatedDate;datetime
auTuratelServiceLog;CreatedDate;datetime
auTuratelServiceLog;LastUpdatedDate;datetime
auUmicoServiceLog;CreatedDate;datetime
auUmicoServiceLog;LastUpdatedDate;datetime
auUnifreeServiceLog;CreatedDate;datetime
auUnifreeServiceLog;LastUpdatedDate;datetime
auUpdateItemCodeTrace;OperationDate;date
auVehicleLoadingPermit;CreatedDate;datetime
auVehicleLoadingPermit;DeleteMaxDate;date
auVehicleLoadingPermit;DeleteMinDate;date
auVehicleLoadingPermit;InsertMaxDate;date
auVehicleLoadingPermit;InsertMinDate;date
auVehicleLoadingPermit;LastUpdatedDate;datetime
auVehicleLoadingPermit;UpdateMaxDate;date
auVehicleLoadingPermit;UpdateMinDate;date
auVehicleUnLoadingPermit;CreatedDate;datetime
auVehicleUnLoadingPermit;DeleteMaxDate;date
auVehicleUnLoadingPermit;DeleteMinDate;date
auVehicleUnLoadingPermit;InsertMaxDate;date
auVehicleUnLoadingPermit;InsertMinDate;date
auVehicleUnLoadingPermit;LastUpdatedDate;datetime
auVehicleUnLoadingPermit;UpdateMaxDate;date
auVehicleUnLoadingPermit;UpdateMinDate;date
auVerifoneProcessTrace;BankaTarihSaat;datetime
auVerifoneProcessTrace;IptalIslemTarihi;datetime
auVerifoneProcessTrace;OperationDate;date
auWebTelZATCATransactionInfo;CreatedDate;datetime
auWebTelZATCATransactionInfo;LastUpdatedDate;datetime
bsCustomizedQuery;CreatedDate;datetime
bsCustomizedQuery;LastUpdatedDate;datetime
bsCustomizedSQLObject;CreatedDate;datetime
bsCustomizedSQLObject;LastUpdatedDate;datetime
bsNebimV3HotfixVersion;InstallationDate;smalldatetime
bsNebimV3Version;InstallationDate;smalldatetime
cdAccountant;CreatedDate;datetime
cdAccountant;LastUpdatedDate;datetime
cdAccountantDesc;CreatedDate;datetime
cdAccountantDesc;LastUpdatedDate;datetime
cdAddressShareCompanyWebService;CreatedDate;datetime
cdAddressShareCompanyWebService;LastUpdatedDate;datetime
cdAddressShareCompanyWebServiceDesc;CreatedDate;datetime
cdAddressShareCompanyWebServiceDesc;LastUpdatedDate;datetime
cdAddressType;CreatedDate;datetime
cdAddressType;LastUpdatedDate;datetime
cdAddressTypeDesc;CreatedDate;datetime
cdAddressTypeDesc;LastUpdatedDate;datetime
cdAllocationTemplate;CreatedDate;datetime
cdAllocationTemplate;LastUpdatedDate;datetime
cdAllocationTemplate;TemplateDate;date
cdAllocationTemplateDesc;CreatedDate;datetime
cdAllocationTemplateDesc;LastUpdatedDate;datetime
cdAmountRule;CreatedDate;datetime
cdAmountRule;LastUpdatedDate;datetime
cdAmountRuleDesc;CreatedDate;datetime
cdAmountRuleDesc;LastUpdatedDate;datetime
cdATAttribute;CreatedDate;datetime
cdATAttribute;LastUpdatedDate;datetime
cdATAttributeDesc;CreatedDate;datetime
cdATAttributeDesc;LastUpdatedDate;datetime
cdATAttributeType;CreatedDate;datetime
cdATAttributeType;LastUpdatedDate;datetime
cdATAttributeTypeDesc;CreatedDate;datetime
cdATAttributeTypeDesc;LastUpdatedDate;datetime
cdBadDebtLetterType;CreatedDate;datetime
cdBadDebtLetterType;LastUpdatedDate;datetime
cdBadDebtLetterTypeDesc;CreatedDate;datetime
cdBadDebtLetterTypeDesc;LastUpdatedDate;datetime
cdBadDebtReason;CreatedDate;datetime
cdBadDebtReason;LastUpdatedDate;datetime
cdBadDebtReasonDesc;CreatedDate;datetime
cdBadDebtReasonDesc;LastUpdatedDate;datetime
cdBank;CreatedDate;datetime
cdBank;LastUpdatedDate;datetime
cdBankAccType;CreatedDate;datetime
cdBankAccType;LastUpdatedDate;datetime
cdBankAccTypeDesc;CreatedDate;datetime
cdBankAccTypeDesc;LastUpdatedDate;datetime
cdBankCreditType;CreatedDate;datetime
cdBankCreditType;LastUpdatedDate;datetime
cdBankCreditTypeDesc;CreatedDate;datetime
cdBankCreditTypeDesc;LastUpdatedDate;datetime
cdBankDesc;CreatedDate;datetime
cdBankDesc;LastUpdatedDate;datetime
cdBankOpType;CreatedDate;datetime
cdBankOpType;LastUpdatedDate;datetime
cdBankOpTypeDesc;CreatedDate;datetime
cdBankOpTypeDesc;LastUpdatedDate;datetime
cdBarcodeCompany;CreatedDate;datetime
cdBarcodeCompany;LastUpdatedDate;datetime
cdBarcodeType;CreatedDate;datetime
cdBarcodeType;LastUpdatedDate;datetime
cdBarcodeTypeDesc;CreatedDate;datetime
cdBarcodeTypeDesc;LastUpdatedDate;datetime
cdBaseMaterial;CreatedDate;datetime
cdBaseMaterial;LastUpdatedDate;datetime
cdBaseMaterialDesc;CreatedDate;datetime
cdBaseMaterialDesc;LastUpdatedDate;datetime
cdBatch;CreatedDate;datetime
cdBatch;LastUpdatedDate;datetime
cdBatchDesc;CreatedDate;datetime
cdBatchDesc;LastUpdatedDate;datetime
cdBatchGroup;CreatedDate;datetime
cdBatchGroup;LastUpdatedDate;datetime
cdBatchGroupDesc;CreatedDate;datetime
cdBatchGroupDesc;LastUpdatedDate;datetime
cdBloodType;CreatedDate;datetime
cdBloodType;LastUpdatedDate;datetime
cdBloodTypeDesc;CreatedDate;datetime
cdBloodTypeDesc;LastUpdatedDate;datetime
cdBOM;CreatedDate;datetime
cdBOM;LastUpdatedDate;datetime
cdBOMDesc;CreatedDate;datetime
cdBOMDesc;LastUpdatedDate;datetime
cdBOMEntity;CreatedDate;datetime
cdBOMEntity;LastUpdatedDate;datetime
cdBOMEntityDesc;CreatedDate;datetime
cdBOMEntityDesc;LastUpdatedDate;datetime
cdBOMTemplate;CreatedDate;datetime
cdBOMTemplate;LastUpdatedDate;datetime
cdBOMTemplateAttribute;CreatedDate;datetime
cdBOMTemplateAttribute;LastUpdatedDate;datetime
cdBOMTemplateAttributeDesc;CreatedDate;datetime
cdBOMTemplateAttributeDesc;LastUpdatedDate;datetime
cdBOMTemplateAttributeType;CreatedDate;datetime
cdBOMTemplateAttributeType;LastUpdatedDate;datetime
cdBOMTemplateAttributeTypeDesc;CreatedDate;datetime
cdBOMTemplateAttributeTypeDesc;LastUpdatedDate;datetime
cdBOMTemplateDesc;CreatedDate;datetime
cdBOMTemplateDesc;LastUpdatedDate;datetime
cdBrand;CreatedDate;datetime
cdBrand;LastUpdatedDate;datetime
cdBrandDesc;CreatedDate;datetime
cdBrandDesc;LastUpdatedDate;datetime
cdBudgetType;CreatedDate;datetime
cdBudgetType;LastUpdatedDate;datetime
cdBudgetTypeDesc;CreatedDate;datetime
cdBudgetTypeDesc;LastUpdatedDate;datetime
cdBusinessGroup;CreatedDate;datetime
cdBusinessGroup;LastUpdatedDate;datetime
cdBusinessGroupDesc;CreatedDate;datetime
cdBusinessGroupDesc;LastUpdatedDate;datetime
cdCareWarning;CreatedDate;datetime
cdCareWarning;LastUpdatedDate;datetime
cdCareWarningDesc;CreatedDate;datetime
cdCareWarningDesc;LastUpdatedDate;datetime
cdCareWarningTemplate;CreatedDate;datetime
cdCareWarningTemplate;LastUpdatedDate;datetime
cdCareWarningTemplateDesc;CreatedDate;datetime
cdCareWarningTemplateDesc;LastUpdatedDate;datetime
cdChannelTemplate;CreatedDate;datetime
cdChannelTemplate;LastUpdatedDate;datetime
cdChannelTemplateDesc;CreatedDate;datetime
cdChannelTemplateDesc;LastUpdatedDate;datetime
cdCheckOutReason;CreatedDate;datetime
cdCheckOutReason;LastUpdatedDate;datetime
cdCheckOutReasonDesc;CreatedDate;datetime
cdCheckOutReasonDesc;LastUpdatedDate;datetime
cdCheque;ClosingDate;date
cdCheque;CreatedDate;datetime
cdCheque;DueDate;date
cdCheque;LastUpdatedDate;datetime
cdCheque;OrganizedDate;date
cdChequeAttribute;CreatedDate;datetime
cdChequeAttribute;LastUpdatedDate;datetime
cdChequeAttributeDesc;CreatedDate;datetime
cdChequeAttributeDesc;LastUpdatedDate;datetime
cdChequeAttributeType;CreatedDate;datetime
cdChequeAttributeType;LastUpdatedDate;datetime
cdChequeAttributeTypeDesc;CreatedDate;datetime
cdChequeAttributeTypeDesc;LastUpdatedDate;datetime
cdChequeDenyReason;CreatedDate;datetime
cdChequeDenyReason;LastUpdatedDate;datetime
cdChequeDenyReasonDesc;CreatedDate;datetime
cdChequeDenyReasonDesc;LastUpdatedDate;datetime
cdChequeDesc;CreatedDate;datetime
cdChequeDesc;LastUpdatedDate;datetime
cdCity;CreatedDate;datetime
cdCity;LastUpdatedDate;datetime
cdCityDesc;CreatedDate;datetime
cdCityDesc;LastUpdatedDate;datetime
cdCoatingType;CreatedDate;datetime
cdCoatingType;LastUpdatedDate;datetime
cdCoatingTypeDesc;CreatedDate;datetime
cdCoatingTypeDesc;LastUpdatedDate;datetime
cdCollection;CreatedDate;datetime
cdCollection;LastUpdatedDate;datetime
cdCollectionDesc;CreatedDate;datetime
cdCollectionDesc;LastUpdatedDate;datetime
cdColor;CreatedDate;datetime
cdColor;LastUpdatedDate;datetime
cdColorCatalog;CreatedDate;datetime
cdColorCatalog;LastUpdatedDate;datetime
cdColorCatalogDesc;CreatedDate;datetime
cdColorCatalogDesc;LastUpdatedDate;datetime
cdColorDesc;CreatedDate;datetime
cdColorDesc;LastUpdatedDate;datetime
cdColorGroup;CreatedDate;datetime
cdColorGroup;LastUpdatedDate;datetime
cdColorGroupDesc;CreatedDate;datetime
cdColorGroupDesc;LastUpdatedDate;datetime
cdColorTheme;CreatedDate;datetime
cdColorTheme;FirstIncomingDate;date
cdColorTheme;LastUpdatedDate;datetime
cdColorThemeAttribute;CreatedDate;datetime
cdColorThemeAttribute;LastUpdatedDate;datetime
cdColorThemeAttributeDesc;CreatedDate;datetime
cdColorThemeAttributeDesc;LastUpdatedDate;datetime
cdColorThemeAttributeType;CreatedDate;datetime
cdColorThemeAttributeType;LastUpdatedDate;datetime
cdColorThemeAttributeTypeDesc;CreatedDate;datetime
cdColorThemeAttributeTypeDesc;LastUpdatedDate;datetime
cdColorThemeDesc;CreatedDate;datetime
cdColorThemeDesc;LastUpdatedDate;datetime
cdColorType;CreatedDate;datetime
cdColorType;LastUpdatedDate;datetime
cdColorTypeDesc;CreatedDate;datetime
cdColorTypeDesc;LastUpdatedDate;datetime
cdCommercialRole;CreatedDate;datetime
cdCommercialRole;LastUpdatedDate;datetime
cdCommercialRoleDesc;CreatedDate;datetime
cdCommercialRoleDesc;LastUpdatedDate;datetime
cdCommunicationType;CreatedDate;datetime
cdCommunicationType;LastUpdatedDate;datetime
cdCommunicationTypeDesc;CreatedDate;datetime
cdCommunicationTypeDesc;LastUpdatedDate;datetime
cdCompany;CreatedDate;datetime
cdCompany;LastUpdatedDate;datetime
cdCompanyBrand;CreatedDate;datetime
cdCompanyBrand;LastUpdatedDate;datetime
cdCompanyBrandDesc;CreatedDate;datetime
cdCompanyBrandDesc;LastUpdatedDate;datetime
cdCompanyCreditCard;CreatedDate;datetime
cdCompanyCreditCard;LastUpdatedDate;datetime
cdCompanyCreditCardDesc;CreatedDate;datetime
cdCompanyCreditCardDesc;LastUpdatedDate;datetime
cdConditionType;CreatedDate;datetime
cdConditionType;LastUpdatedDate;datetime
cdConditionTypeDesc;CreatedDate;datetime
cdConditionTypeDesc;LastUpdatedDate;datetime
cdConfirmationFormStatus;CreatedDate;datetime
cdConfirmationFormStatus;LastUpdatedDate;datetime
cdConfirmationFormStatusDesc;CreatedDate;datetime
cdConfirmationFormStatusDesc;LastUpdatedDate;datetime
cdConfirmationFormType;CreatedDate;datetime
cdConfirmationFormType;LastUpdatedDate;datetime
cdConfirmationFormTypeDesc;CreatedDate;datetime
cdConfirmationFormTypeDesc;LastUpdatedDate;datetime
cdConfirmationReason;CreatedDate;datetime
cdConfirmationReason;LastUpdatedDate;datetime
cdConfirmationReasonDesc;CreatedDate;datetime
cdConfirmationReasonDesc;LastUpdatedDate;datetime
cdConfirmationRule;CreatedDate;datetime
cdConfirmationRule;LastUpdatedDate;datetime
cdContactType;CreatedDate;datetime
cdContactType;LastUpdatedDate;datetime
cdContactTypeDesc;CreatedDate;datetime
cdContactTypeDesc;LastUpdatedDate;datetime
cdContainerType;CreatedDate;datetime
cdContainerType;LastUpdatedDate;datetime
cdContainerTypeDesc;CreatedDate;datetime
cdContainerTypeDesc;LastUpdatedDate;datetime
cdContractContent;CreatedDate;datetime
cdContractContent;LastUpdatedDate;datetime
cdContractContentDesc;CreatedDate;datetime
cdContractContentDesc;LastUpdatedDate;datetime
cdContractStatus;CreatedDate;datetime
cdContractStatus;LastUpdatedDate;datetime
cdContractStatusDesc;CreatedDate;datetime
cdContractStatusDesc;LastUpdatedDate;datetime
cdCostCenter;CreatedDate;datetime
cdCostCenter;LastUpdatedDate;datetime
cdCostCenterAttribute;CreatedDate;datetime
cdCostCenterAttribute;LastUpdatedDate;datetime
cdCostCenterAttributeDesc;CreatedDate;datetime
cdCostCenterAttributeDesc;LastUpdatedDate;datetime
cdCostCenterAttributeType;CreatedDate;datetime
cdCostCenterAttributeType;LastUpdatedDate;datetime
cdCostCenterAttributeTypeDesc;CreatedDate;datetime
cdCostCenterAttributeTypeDesc;LastUpdatedDate;datetime
cdCostCenterDesc;CreatedDate;datetime
cdCostCenterDesc;LastUpdatedDate;datetime
cdCostOfGoodsSoldPeriod;CreatedDate;datetime
cdCostOfGoodsSoldPeriod;EndDate;smalldatetime
cdCostOfGoodsSoldPeriod;LastUpdatedDate;datetime
cdCostOfGoodsSoldPeriodDesc;CreatedDate;datetime
cdCostOfGoodsSoldPeriodDesc;LastUpdatedDate;datetime
cdCountry;CreatedDate;datetime
cdCountry;LastUpdatedDate;datetime
cdCountryDesc;CreatedDate;datetime
cdCountryDesc;LastUpdatedDate;datetime
cdCreditCardType;CreatedDate;datetime
cdCreditCardType;LastUpdatedDate;datetime
cdCreditCardTypeDesc;CreatedDate;datetime
cdCreditCardTypeDesc;LastUpdatedDate;datetime
cdCreditSurveyor;CreatedDate;datetime
cdCreditSurveyor;LastUpdatedDate;datetime
cdCurrAcc;AccountClosingDate;date
cdCurrAcc;AccountOpeningDate;date
cdCurrAcc;AgreementDate;date
cdCurrAcc;CreatedDate;datetime
cdCurrAcc;EInvoiceStartDate;date
cdCurrAcc;EShipmentStartDate;date
cdCurrAcc;LastUpdatedDate;datetime
cdCurrAcc;LockedDate;date
cdCurrAccAttribute;CreatedDate;datetime
cdCurrAccAttribute;LastUpdatedDate;datetime
cdCurrAccAttributeDesc;CreatedDate;datetime
cdCurrAccAttributeDesc;LastUpdatedDate;datetime
cdCurrAccAttributeType;CreatedDate;datetime
cdCurrAccAttributeType;LastUpdatedDate;datetime
cdCurrAccAttributeTypeDesc;CreatedDate;datetime
cdCurrAccAttributeTypeDesc;LastUpdatedDate;datetime
cdCurrAccDesc;CreatedDate;datetime
cdCurrAccDesc;LastUpdatedDate;datetime
cdCurrAccList;CreatedDate;datetime
cdCurrAccList;ExpireEndDate;datetime
cdCurrAccList;ExpireStartDate;datetime
cdCurrAccList;LastUpdatedDate;datetime
cdCurrAccListDesc;CreatedDate;datetime
cdCurrAccListDesc;LastUpdatedDate;datetime
cdCurrAccLotGr;CreatedDate;datetime
cdCurrAccLotGr;LastUpdatedDate;datetime
cdCurrAccLotGrDesc;CreatedDate;datetime
cdCurrAccLotGrDesc;LastUpdatedDate;datetime
cdCurrency;CreatedDate;datetime
cdCurrency;LastUpdatedDate;datetime
cdCurrencyDesc;CreatedDate;datetime
cdCurrencyDesc;LastUpdatedDate;datetime
cdCustomerAlertColor;CreatedDate;datetime
cdCustomerAlertColor;LastUpdatedDate;datetime
cdCustomerAlertColorDesc;CreatedDate;datetime
cdCustomerAlertColorDesc;LastUpdatedDate;datetime
cdCustomerCompanyBrandAttribute;CreatedDate;datetime
cdCustomerCompanyBrandAttribute;LastUpdatedDate;datetime
cdCustomerCompanyBrandAttributeDesc;CreatedDate;datetime
cdCustomerCompanyBrandAttributeDesc;LastUpdatedDate;datetime
cdCustomerCompanyBrandAttributeType;CreatedDate;datetime
cdCustomerCompanyBrandAttributeType;LastUpdatedDate;datetime
cdCustomerCompanyBrandAttributeTypeDesc;CreatedDate;datetime
cdCustomerCompanyBrandAttributeTypeDesc;LastUpdatedDate;datetime
cdCustomerConversationResult;CreatedDate;datetime
cdCustomerConversationResult;LastUpdatedDate;datetime
cdCustomerConversationResultDesc;CreatedDate;datetime
cdCustomerConversationResultDesc;LastUpdatedDate;datetime
cdCustomerConversationSubject;CreatedDate;datetime
cdCustomerConversationSubject;LastUpdatedDate;datetime
cdCustomerConversationSubjectDesc;CreatedDate;datetime
cdCustomerConversationSubjectDesc;LastUpdatedDate;datetime
cdCustomerConversationSubjectDetail;CreatedDate;datetime
cdCustomerConversationSubjectDetail;LastUpdatedDate;datetime
cdCustomerConversationSubjectDetailDesc;CreatedDate;datetime
cdCustomerConversationSubjectDetailDesc;LastUpdatedDate;datetime
cdCustomerConversationSubtitle;CreatedDate;datetime
cdCustomerConversationSubtitle;LastUpdatedDate;datetime
cdCustomerConversationSubtitleDesc;CreatedDate;datetime
cdCustomerConversationSubtitleDesc;LastUpdatedDate;datetime
cdCustomerCRMGroup;CreatedDate;datetime
cdCustomerCRMGroup;LastUpdatedDate;datetime
cdCustomerCRMGroupDesc;CreatedDate;datetime
cdCustomerCRMGroupDesc;LastUpdatedDate;datetime
cdCustomerDiscountGr;CreatedDate;datetime
cdCustomerDiscountGr;LastUpdatedDate;datetime
cdCustomerDiscountGrDesc;CreatedDate;datetime
cdCustomerDiscountGrDesc;LastUpdatedDate;datetime
cdCustomerMarkupGr;CreatedDate;datetime
cdCustomerMarkupGr;LastUpdatedDate;datetime
cdCustomerMarkupGrDesc;CreatedDate;datetime
cdCustomerMarkupGrDesc;LastUpdatedDate;datetime
cdCustomerPaymentPlanGr;CreatedDate;datetime
cdCustomerPaymentPlanGr;LastUpdatedDate;datetime
cdCustomerPaymentPlanGrDesc;CreatedDate;datetime
cdCustomerPaymentPlanGrDesc;LastUpdatedDate;datetime
cdCustomerShoppingHabit;CreatedDate;datetime
cdCustomerShoppingHabit;LastUpdatedDate;datetime
cdCustomerShoppingHabitDesc;CreatedDate;datetime
cdCustomerShoppingHabitDesc;LastUpdatedDate;datetime
cdCustomerShoppingLevel;CreatedDate;datetime
cdCustomerShoppingLevel;LastUpdatedDate;datetime
cdCustomerShoppingLevelDesc;CreatedDate;datetime
cdCustomerShoppingLevelDesc;LastUpdatedDate;datetime
cdCustomProcessGroup;CreatedDate;datetime
cdCustomProcessGroup;LastUpdatedDate;datetime
cdCustomProcessGroupDesc;CreatedDate;datetime
cdCustomProcessGroupDesc;LastUpdatedDate;datetime
cdCustomsCompany;CreatedDate;datetime
cdCustomsCompany;LastUpdatedDate;datetime
cdCustomsCompanyDesc;CreatedDate;datetime
cdCustomsCompanyDesc;LastUpdatedDate;datetime
cdCustomsOffices;CreatedDate;datetime
cdCustomsOffices;LastUpdatedDate;datetime
cdCustomsOfficesDesc;CreatedDate;datetime
cdCustomsOfficesDesc;LastUpdatedDate;datetime
cdCustomsTariffNumber;CreatedDate;datetime
cdCustomsTariffNumber;LastUpdatedDate;datetime
cdCustomsTariffNumberDesc;CreatedDate;datetime
cdCustomsTariffNumberDesc;LastUpdatedDate;datetime
cdDataLanguage;CreatedDate;datetime
cdDataLanguage;LastUpdatedDate;datetime
cdDataLanguageDesc;CreatedDate;datetime
cdDataLanguageDesc;LastUpdatedDate;datetime
cdDataTransferCompany;CreatedDate;datetime
cdDataTransferCompany;LastUpdatedDate;datetime
cdDataTransferCompanyDesc;CreatedDate;datetime
cdDataTransferCompanyDesc;LastUpdatedDate;datetime
cdDataTransferConvert;CreatedDate;datetime
cdDataTransferConvert;LastUpdatedDate;datetime
cdDataTransferConvertForAttribute;CreatedDate;datetime
cdDataTransferConvertForAttribute;LastUpdatedDate;datetime
cdDataTransferJob;CreatedDate;datetime
cdDataTransferJob;LastUpdatedDate;datetime
cdDataTransferSchedule;CreatedDate;datetime
cdDataTransferSchedule;LastUpdatedDate;datetime
cdDataTransferTemplate;CreatedDate;datetime
cdDataTransferTemplate;LastUpdatedDate;datetime
cdDebitReason;CreatedDate;datetime
cdDebitReason;LastUpdatedDate;datetime
cdDebitReasonDesc;CreatedDate;datetime
cdDebitReasonDesc;LastUpdatedDate;datetime
cdDeclaration;CreatedDate;datetime
cdDeclaration;EndDate;date
cdDeclaration;LastUpdatedDate;datetime
cdDeclaration;StartDate;date
cdDeclarationDesc;CreatedDate;datetime
cdDeclarationDesc;LastUpdatedDate;datetime
cdDeduction;CreatedDate;datetime
cdDeduction;LastUpdatedDate;datetime
cdDeductionDesc;CreatedDate;datetime
cdDeductionDesc;LastUpdatedDate;datetime
cdDeliveryCompany;CreatedDate;datetime
cdDeliveryCompany;LastUpdatedDate;datetime
cdDeliveryCompanyDesc;CreatedDate;datetime
cdDeliveryCompanyDesc;LastUpdatedDate;datetime
cdDiagnostic;CreatedDate;datetime
cdDiagnostic;LastUpdatedDate;datetime
cdDiagnosticDesc;CreatedDate;datetime
cdDiagnosticDesc;LastUpdatedDate;datetime
cdDigitalMarketingService;CreatedDate;datetime
cdDigitalMarketingService;LastUpdatedDate;datetime
cdDigitalMarketingServiceDesc;CreatedDate;datetime
cdDigitalMarketingServiceDesc;LastUpdatedDate;datetime
cdDiscountOffer;CreatedDate;datetime
cdDiscountOffer;LastUpdatedDate;datetime
cdDiscountOfferAttribute;CreatedDate;datetime
cdDiscountOfferAttribute;LastUpdatedDate;datetime
cdDiscountOfferAttributeDesc;CreatedDate;datetime
cdDiscountOfferAttributeDesc;LastUpdatedDate;datetime
cdDiscountOfferAttributeType;CreatedDate;datetime
cdDiscountOfferAttributeType;LastUpdatedDate;datetime
cdDiscountOfferAttributeTypeDesc;CreatedDate;datetime
cdDiscountOfferAttributeTypeDesc;LastUpdatedDate;datetime
cdDiscountOfferDesc;CreatedDate;datetime
cdDiscountOfferDesc;LastUpdatedDate;datetime
cdDiscountPointType;CreatedDate;datetime
cdDiscountPointType;LastUpdatedDate;datetime
cdDiscountPointTypeDesc;CreatedDate;datetime
cdDiscountPointTypeDesc;LastUpdatedDate;datetime
cdDiscountReason;CreatedDate;datetime
cdDiscountReason;LastUpdatedDate;datetime
cdDiscountReasonDesc;CreatedDate;datetime
cdDiscountReasonDesc;LastUpdatedDate;datetime
cdDiscountSubReason;CreatedDate;datetime
cdDiscountSubReason;LastUpdatedDate;datetime
cdDiscountSubReasonDesc;CreatedDate;datetime
cdDiscountSubReasonDesc;LastUpdatedDate;datetime
cdDiscountType;CreatedDate;datetime
cdDiscountType;LastUpdatedDate;datetime
cdDiscountTypeDesc;CreatedDate;datetime
cdDiscountTypeDesc;LastUpdatedDate;datetime
cdDiscountVoucher;CancelDate;smalldatetime
cdDiscountVoucher;CreatedDate;datetime
cdDiscountVoucher;FirstValidDate;date
cdDiscountVoucher;LastUpdatedDate;datetime
cdDiscountVoucher;LastValidDate;date
cdDiscountVoucherType;CreatedDate;datetime
cdDiscountVoucherType;LastUpdatedDate;datetime
cdDiscountVoucherTypeDesc;CreatedDate;datetime
cdDiscountVoucherTypeDesc;LastUpdatedDate;datetime
cdDistanceMatrixProvider;CreatedDate;datetime
cdDistanceMatrixProvider;LastUpdatedDate;datetime
cdDistrict;CreatedDate;datetime
cdDistrict;LastUpdatedDate;datetime
cdDistrictDesc;CreatedDate;datetime
cdDistrictDesc;LastUpdatedDate;datetime
cdDOV;CreatedDate;datetime
cdDOV;LastUpdatedDate;datetime
cdDOVDesc;CreatedDate;datetime
cdDOVDesc;LastUpdatedDate;datetime
cdDriver;CreatedDate;datetime
cdDriver;LastUpdatedDate;datetime
cdDueDateFormula;CreatedDate;datetime
cdDueDateFormula;LastUpdatedDate;datetime
cdDueDateFormulaDesc;CreatedDate;datetime
cdDueDateFormulaDesc;LastUpdatedDate;datetime
cdEArchiveWebService;CreatedDate;datetime
cdEArchiveWebService;LastUpdatedDate;datetime
cdEArchiveWebServiceDesc;CreatedDate;datetime
cdEArchiveWebServiceDesc;LastUpdatedDate;datetime
cdEarnings;CreatedDate;datetime
cdEarnings;LastUpdatedDate;datetime
cdEarningsDesc;CreatedDate;datetime
cdEarningsDesc;LastUpdatedDate;datetime
cdEducationStatus;CreatedDate;datetime
cdEducationStatus;LastUpdatedDate;datetime
cdEducationStatusDesc;CreatedDate;datetime
cdEducationStatusDesc;LastUpdatedDate;datetime
cdEInvoiceWebService;CreatedDate;datetime
cdEInvoiceWebService;LastUpdatedDate;datetime
cdEInvoiceWebServiceDesc;CreatedDate;datetime
cdEInvoiceWebServiceDesc;LastUpdatedDate;datetime
cdEMailService;CreatedDate;datetime
cdEMailService;LastUpdatedDate;datetime
cdEMailServiceDesc;CreatedDate;datetime
cdEMailServiceDesc;LastUpdatedDate;datetime
cdEmployeeDocumentType;CreatedDate;datetime
cdEmployeeDocumentType;LastUpdatedDate;datetime
cdEmployeeDocumentTypeDesc;CreatedDate;datetime
cdEmployeeDocumentTypeDesc;LastUpdatedDate;datetime
cdEmployeeRecordType;CreatedDate;datetime
cdEmployeeRecordType;LastUpdatedDate;datetime
cdEmployeeRecordTypeDesc;CreatedDate;datetime
cdEmployeeRecordTypeDesc;LastUpdatedDate;datetime
cdEmployeeSocialInsuranceStatus;CreatedDate;datetime
cdEmployeeSocialInsuranceStatus;LastUpdatedDate;datetime
cdEmployeeSocialInsuranceStatusDesc;CreatedDate;datetime
cdEmployeeSocialInsuranceStatusDesc;LastUpdatedDate;datetime
cdEmployeeTaxStatus;CreatedDate;datetime
cdEmployeeTaxStatus;LastUpdatedDate;datetime
cdEmployeeTaxStatusDesc;CreatedDate;datetime
cdEmployeeTaxStatusDesc;LastUpdatedDate;datetime
cdEmploymentLaw;CreatedDate;datetime
cdEmploymentLaw;LastUpdatedDate;datetime
cdEmploymentLaw;LawRepealDate;date
cdEmploymentLawDesc;CreatedDate;datetime
cdEmploymentLawDesc;LastUpdatedDate;datetime
cdEShipmentWebService;CreatedDate;datetime
cdEShipmentWebService;LastUpdatedDate;datetime
cdEShipmentWebServiceDesc;CreatedDate;datetime
cdEShipmentWebServiceDesc;LastUpdatedDate;datetime
cdExchangeType;CreatedDate;datetime
cdExchangeType;LastUpdatedDate;datetime
cdExchangeTypeDesc;CreatedDate;datetime
cdExchangeTypeDesc;LastUpdatedDate;datetime
cdExecutionOffice;CreatedDate;datetime
cdExecutionOffice;LastUpdatedDate;datetime
cdExecutionOfficeDesc;CreatedDate;datetime
cdExecutionOfficeDesc;LastUpdatedDate;datetime
cdExpensePeriod;CreatedDate;datetime
cdExpensePeriod;EndDate;smalldatetime
cdExpensePeriod;LastUpdatedDate;datetime
cdExpensePeriod;StartDate;smalldatetime
cdExpensePeriodDesc;CreatedDate;datetime
cdExpensePeriodDesc;LastUpdatedDate;datetime
cdExpenseType;CreatedDate;datetime
cdExpenseType;LastUpdatedDate;datetime
cdExpenseTypeDesc;CreatedDate;datetime
cdExpenseTypeDesc;LastUpdatedDate;datetime
cdExportFile;ClosingDate;date
cdExportFile;CreatedDate;datetime
cdExportFile;LastUpdatedDate;datetime
cdExportFile;LockedDate;date
cdExportFile;StartDate;date
cdExportFileAttribute;CreatedDate;datetime
cdExportFileAttribute;LastUpdatedDate;datetime
cdExportFileAttributeDesc;CreatedDate;datetime
cdExportFileAttributeDesc;LastUpdatedDate;datetime
cdExportFileAttributeType;CreatedDate;datetime
cdExportFileAttributeType;LastUpdatedDate;datetime
cdExportFileAttributeTypeDesc;CreatedDate;datetime
cdExportFileAttributeTypeDesc;LastUpdatedDate;datetime
cdExportFileDesc;CreatedDate;datetime
cdExportFileDesc;LastUpdatedDate;datetime
cdExportType;CreatedDate;datetime
cdExportType;LastUpdatedDate;datetime
cdExportTypeDesc;CreatedDate;datetime
cdExportTypeDesc;LastUpdatedDate;datetime
cdFabric;CreatedDate;datetime
cdFabric;LastUpdatedDate;datetime
cdFabricDesc;CreatedDate;datetime
cdFabricDesc;LastUpdatedDate;datetime
cdFinanceCompanyWebService;CreatedDate;datetime
cdFinanceCompanyWebService;LastUpdatedDate;datetime
cdFinanceCompanyWebServiceDesc;CreatedDate;datetime
cdFinanceCompanyWebServiceDesc;LastUpdatedDate;datetime
cdFiscalPeriod;BeforeFiscalPeriodEndDate;smalldatetime
cdFiscalPeriod;CreatedDate;datetime
cdFiscalPeriod;EndDate;smalldatetime
cdFiscalPeriod;LastUpdatedDate;datetime
cdFiscalPeriod;StartDate;smalldatetime
cdFiscalPeriodDesc;CreatedDate;datetime
cdFiscalPeriodDesc;LastUpdatedDate;datetime
cdFixedAssetStatus;CreatedDate;datetime
cdFixedAssetStatus;LastUpdatedDate;datetime
cdFixedAssetStatusDesc;CreatedDate;datetime
cdFixedAssetStatusDesc;LastUpdatedDate;datetime
cdFixedAssetType;CreatedDate;datetime
cdFixedAssetType;LastUpdatedDate;datetime
cdFixedAssetTypeDesc;CreatedDate;datetime
cdFixedAssetTypeDesc;LastUpdatedDate;datetime
cdFocalType;CreatedDate;datetime
cdFocalType;LastUpdatedDate;datetime
cdFocalTypeDesc;CreatedDate;datetime
cdFocalTypeDesc;LastUpdatedDate;datetime
cdForeignLanguage;CreatedDate;datetime
cdForeignLanguage;LastUpdatedDate;datetime
cdForeignLanguageDesc;CreatedDate;datetime
cdForeignLanguageDesc;LastUpdatedDate;datetime
cdForeignTradeStatus;CreatedDate;datetime
cdForeignTradeStatus;LastUpdatedDate;datetime
cdForeignTradeStatusDesc;CreatedDate;datetime
cdForeignTradeStatusDesc;LastUpdatedDate;datetime
cdFrameShapeType;CreatedDate;datetime
cdFrameShapeType;LastUpdatedDate;datetime
cdFrameShapeTypeDesc;CreatedDate;datetime
cdFrameShapeTypeDesc;LastUpdatedDate;datetime
cdFrameType;CreatedDate;datetime
cdFrameType;LastUpdatedDate;datetime
cdFrameTypeDesc;CreatedDate;datetime
cdFrameTypeDesc;LastUpdatedDate;datetime
cdFTAttribute;CreatedDate;datetime
cdFTAttribute;LastUpdatedDate;datetime
cdFTAttributeDesc;CreatedDate;datetime
cdFTAttributeDesc;LastUpdatedDate;datetime
cdFTAttributeType;CreatedDate;datetime
cdFTAttributeType;LastUpdatedDate;datetime
cdFTAttributeTypeDesc;CreatedDate;datetime
cdFTAttributeTypeDesc;LastUpdatedDate;datetime
cdGiftCard;CreatedDate;datetime
cdGiftCard;FirstValidDate;date
cdGiftCard;LastUpdatedDate;datetime
cdGiftCard;LastValidDate;date
cdGLAcc;CreatedDate;datetime
cdGLAcc;LastUpdatedDate;datetime
cdGLAcc;LockedDate;date
cdGLAccAttribute;CreatedDate;datetime
cdGLAccAttribute;LastUpdatedDate;datetime
cdGLAccAttributeDesc;CreatedDate;datetime
cdGLAccAttributeDesc;LastUpdatedDate;datetime
cdGLAccAttributeType;CreatedDate;datetime
cdGLAccAttributeType;LastUpdatedDate;datetime
cdGLAccAttributeTypeDesc;CreatedDate;datetime
cdGLAccAttributeTypeDesc;LastUpdatedDate;datetime
cdGLAccClass;CreatedDate;datetime
cdGLAccClass;LastUpdatedDate;datetime
cdGLAccClassDesc;CreatedDate;datetime
cdGLAccClassDesc;LastUpdatedDate;datetime
cdGLAccDesc;CreatedDate;datetime
cdGLAccDesc;LastUpdatedDate;datetime
cdGLAccGroup;CreatedDate;datetime
cdGLAccGroup;LastUpdatedDate;datetime
cdGLAccGroupDesc;CreatedDate;datetime
cdGLAccGroupDesc;LastUpdatedDate;datetime
cdGLAccMain;CreatedDate;datetime
cdGLAccMain;LastUpdatedDate;datetime
cdGLAccMainDesc;CreatedDate;datetime
cdGLAccMainDesc;LastUpdatedDate;datetime
cdGLAccSub;CreatedDate;datetime
cdGLAccSub;LastUpdatedDate;datetime
cdGLAccSubDesc;CreatedDate;datetime
cdGLAccSubDesc;LastUpdatedDate;datetime
cdGLReflection;CreatedDate;datetime
cdGLReflection;LastUpdatedDate;datetime
cdGLReflectionDesc;CreatedDate;datetime
cdGLReflectionDesc;LastUpdatedDate;datetime
cdGLType;CreatedDate;datetime
cdGLType;LastUpdatedDate;datetime
cdGLTypeDesc;CreatedDate;datetime
cdGLTypeDesc;LastUpdatedDate;datetime
cdGrandLedger;ConfirmedDate;datetime
cdGrandLedger;CreatedDate;datetime
cdGrandLedger;EndDate;date
cdGrandLedger;LastUpdatedDate;datetime
cdGrandLedger;StartDate;date
cdHandicapType;CreatedDate;datetime
cdHandicapType;LastUpdatedDate;datetime
cdHandicapTypeDesc;CreatedDate;datetime
cdHandicapTypeDesc;LastUpdatedDate;datetime
cdImportFile;ClosingDate;date
cdImportFile;CreatedDate;datetime
cdImportFile;LastUpdatedDate;datetime
cdImportFile;LockedDate;date
cdImportFile;StartDate;date
cdImportFileAttribute;CreatedDate;datetime
cdImportFileAttribute;LastUpdatedDate;datetime
cdImportFileAttributeDesc;CreatedDate;datetime
cdImportFileAttributeDesc;LastUpdatedDate;datetime
cdImportFileAttributeType;CreatedDate;datetime
cdImportFileAttributeType;LastUpdatedDate;datetime
cdImportFileAttributeTypeDesc;CreatedDate;datetime
cdImportFileAttributeTypeDesc;LastUpdatedDate;datetime
cdImportFileDesc;CreatedDate;datetime
cdImportFileDesc;LastUpdatedDate;datetime
cdInactivationReason;CreatedDate;datetime
cdInactivationReason;LastUpdatedDate;datetime
cdInactivationReasonDesc;CreatedDate;datetime
cdInactivationReasonDesc;LastUpdatedDate;datetime
cdIncentiveType;CreatedDate;datetime
cdIncentiveType;LastUpdatedDate;datetime
cdIncentiveTypeDesc;CreatedDate;datetime
cdIncentiveTypeDesc;LastUpdatedDate;datetime
cdIndustry;CreatedDate;datetime
cdIndustry;LastUpdatedDate;datetime
cdIndustryDesc;CreatedDate;datetime
cdIndustryDesc;LastUpdatedDate;datetime
cdInsuranceAgency;CreatedDate;datetime
cdInsuranceAgency;LastUpdatedDate;datetime
cdInsuranceAgencyDesc;CreatedDate;datetime
cdInsuranceAgencyDesc;LastUpdatedDate;datetime
cdInsuranceType;CreatedDate;datetime
cdInsuranceType;LastUpdatedDate;datetime
cdInsuranceTypeDesc;CreatedDate;datetime
cdInsuranceTypeDesc;LastUpdatedDate;datetime
cdInteractiveSmsParameters;CreatedDate;datetime
cdInteractiveSmsParameters;LastUpdatedDate;datetime
cdInternationalUnitOfMeasure;CreatedDate;datetime
cdInternationalUnitOfMeasure;LastUpdatedDate;datetime
cdInternationalUnitOfMeasureDesc;CreatedDate;datetime
cdInternationalUnitOfMeasureDesc;LastUpdatedDate;datetime
cdITAttribute;CreatedDate;datetime
cdITAttribute;LastUpdatedDate;datetime
cdITAttributeDesc;CreatedDate;datetime
cdITAttributeDesc;LastUpdatedDate;datetime
cdITAttributeType;CreatedDate;datetime
cdITAttributeType;LastUpdatedDate;datetime
cdITAttributeTypeDesc;CreatedDate;datetime
cdITAttributeTypeDesc;LastUpdatedDate;datetime
cdItem;CreatedDate;datetime
cdItem;LastUpdatedDate;datetime
cdItem;LockedDate;date
cdItemAccountGr;CreatedDate;datetime
cdItemAccountGr;LastUpdatedDate;datetime
cdItemAccountGrDesc;CreatedDate;datetime
cdItemAccountGrDesc;LastUpdatedDate;datetime
cdItemAttribute;CreatedDate;datetime
cdItemAttribute;LastUpdatedDate;datetime
cdItemAttributeDesc;CreatedDate;datetime
cdItemAttributeDesc;LastUpdatedDate;datetime
cdItemAttributeType;CreatedDate;datetime
cdItemAttributeType;LastUpdatedDate;datetime
cdItemAttributeTypeDesc;CreatedDate;datetime
cdItemAttributeTypeDesc;LastUpdatedDate;datetime
cdItemDesc;CreatedDate;datetime
cdItemDesc;LastUpdatedDate;datetime
cdItemDim1;CreatedDate;datetime
cdItemDim1;LastUpdatedDate;datetime
cdItemDim1Desc;CreatedDate;datetime
cdItemDim1Desc;LastUpdatedDate;datetime
cdItemDim2;CreatedDate;datetime
cdItemDim2;LastUpdatedDate;datetime
cdItemDim3;CreatedDate;datetime
cdItemDim3;LastUpdatedDate;datetime
cdItemDiscountGr;CreatedDate;datetime
cdItemDiscountGr;LastUpdatedDate;datetime
cdItemDiscountGrDesc;CreatedDate;datetime
cdItemDiscountGrDesc;LastUpdatedDate;datetime
cdItemLikeType;CreatedDate;datetime
cdItemLikeType;LastUpdatedDate;datetime
cdItemLikeTypeDesc;CreatedDate;datetime
cdItemLikeTypeDesc;LastUpdatedDate;datetime
cdItemList;CreatedDate;datetime
cdItemList;LastUpdatedDate;datetime
cdItemListDesc;CreatedDate;datetime
cdItemListDesc;LastUpdatedDate;datetime
cdItemOTAttribute;CreatedDate;datetime
cdItemOTAttribute;LastUpdatedDate;datetime
cdItemOTAttributeDesc;CreatedDate;datetime
cdItemOTAttributeDesc;LastUpdatedDate;datetime
cdItemOTAttributeType;CreatedDate;datetime
cdItemOTAttributeType;LastUpdatedDate;datetime
cdItemOTAttributeTypeDesc;CreatedDate;datetime
cdItemOTAttributeTypeDesc;LastUpdatedDate;datetime
cdItemPaymentPlanGr;CreatedDate;datetime
cdItemPaymentPlanGr;LastUpdatedDate;datetime
cdItemPaymentPlanGrDesc;CreatedDate;datetime
cdItemPaymentPlanGrDesc;LastUpdatedDate;datetime
cdItemTaxGr;CreatedDate;datetime
cdItemTaxGr;LastUpdatedDate;datetime
cdItemTaxGrDesc;CreatedDate;datetime
cdItemTaxGrDesc;LastUpdatedDate;datetime
cdItemTestType;CreatedDate;datetime
cdItemTestType;LastUpdatedDate;datetime
cdItemTestTypeDesc;CreatedDate;datetime
cdItemTestTypeDesc;LastUpdatedDate;datetime
cdItemTextileCareTemplate;CreatedDate;datetime
cdItemTextileCareTemplate;LastUpdatedDate;datetime
cdItemTextileCareTemplateDesc;CreatedDate;datetime
cdItemTextileCareTemplateDesc;LastUpdatedDate;datetime
cdItemVendorGr;CreatedDate;datetime
cdItemVendorGr;LastUpdatedDate;datetime
cdItemVendorGrDesc;CreatedDate;datetime
cdItemVendorGrDesc;LastUpdatedDate;datetime
cdJobDepartment;CreatedDate;datetime
cdJobDepartment;LastUpdatedDate;datetime
cdJobDepartmentDesc;CreatedDate;datetime
cdJobDepartmentDesc;LastUpdatedDate;datetime
cdJobInterviewResult;CreatedDate;datetime
cdJobInterviewResult;LastUpdatedDate;datetime
cdJobInterviewResultDesc;CreatedDate;datetime
cdJobInterviewResultDesc;LastUpdatedDate;datetime
cdJobPosition;CreatedDate;datetime
cdJobPosition;EndDate;date
cdJobPosition;LastUpdatedDate;datetime
cdJobPosition;StartDate;date
cdJobPositionDesc;CreatedDate;datetime
cdJobPositionDesc;LastUpdatedDate;datetime
cdJobTitle;CreatedDate;datetime
cdJobTitle;LastUpdatedDate;datetime
cdJobTitleDesc;CreatedDate;datetime
cdJobTitleDesc;LastUpdatedDate;datetime
cdJobTitleLevel;CreatedDate;datetime
cdJobTitleLevel;LastUpdatedDate;datetime
cdJobTitleLevelDesc;CreatedDate;datetime
cdJobTitleLevelDesc;LastUpdatedDate;datetime
cdJobTraining;CreatedDate;datetime
cdJobTraining;EndDate;smalldatetime
cdJobTraining;LastUpdatedDate;datetime
cdJobTraining;StartDate;smalldatetime
cdJobTrainingAttribute;CreatedDate;datetime
cdJobTrainingAttribute;LastUpdatedDate;datetime
cdJobTrainingAttributeDesc;CreatedDate;datetime
cdJobTrainingAttributeDesc;LastUpdatedDate;datetime
cdJobTrainingAttributeType;CreatedDate;datetime
cdJobTrainingAttributeType;LastUpdatedDate;datetime
cdJobTrainingAttributeTypeDesc;CreatedDate;datetime
cdJobTrainingAttributeTypeDesc;LastUpdatedDate;datetime
cdJobTrainingDesc;CreatedDate;datetime
cdJobTrainingDesc;LastUpdatedDate;datetime
cdJobType;CreatedDate;datetime
cdJobType;LastUpdatedDate;datetime
cdJobTypeDesc;CreatedDate;datetime
cdJobTypeDesc;LastUpdatedDate;datetime
cdJournalLedger;ConfirmedDate;datetime
cdJournalLedger;CreatedDate;datetime
cdJournalLedger;EndDate;date
cdJournalLedger;LastUpdatedDate;datetime
cdJournalLedger;StartDate;date
cdJournalTypeSub;CreatedDate;datetime
cdJournalTypeSub;LastUpdatedDate;datetime
cdJournalTypeSubDesc;CreatedDate;datetime
cdJournalTypeSubDesc;LastUpdatedDate;datetime
cdKnowLevel;CreatedDate;datetime
cdKnowLevel;LastUpdatedDate;datetime
cdKnowLevelDesc;CreatedDate;datetime
cdKnowLevelDesc;LastUpdatedDate;datetime
cdLabelType;CreatedDate;datetime
cdLabelType;LastUpdatedDate;datetime
cdLabelTypeDesc;CreatedDate;datetime
cdLabelTypeDesc;LastUpdatedDate;datetime
cdLawyer;CreatedDate;datetime
cdLawyer;LastUpdatedDate;datetime
cdLeaveType;CreatedDate;datetime
cdLeaveType;LastUpdatedDate;datetime
cdLeaveTypeDesc;CreatedDate;datetime
cdLeaveTypeDesc;LastUpdatedDate;datetime
cdLegalResignation;CreatedDate;datetime
cdLegalResignation;LastUpdatedDate;datetime
cdLegalResignationDesc;CreatedDate;datetime
cdLegalResignationDesc;LastUpdatedDate;datetime
cdLegalResignationLocal;CreatedDate;datetime
cdLegalResignationLocal;LastUpdatedDate;datetime
cdLegalResignationLocalDesc;CreatedDate;datetime
cdLegalResignationLocalDesc;LastUpdatedDate;datetime
cdLetterOfGuarantee;ClosingDate;date
cdLetterOfGuarantee;CreatedDate;datetime
cdLetterOfGuarantee;DocumentDate;date
cdLetterOfGuarantee;DueDate;date
cdLetterOfGuarantee;LastUpdatedDate;datetime
cdLetterOfGuarantee;LockedDate;date
cdLetterOfGuaranteeAttribute;CreatedDate;datetime
cdLetterOfGuaranteeAttribute;LastUpdatedDate;datetime
cdLetterOfGuaranteeAttributeDesc;CreatedDate;datetime
cdLetterOfGuaranteeAttributeDesc;LastUpdatedDate;datetime
cdLetterOfGuaranteeAttributeType;CreatedDate;datetime
cdLetterOfGuaranteeAttributeType;LastUpdatedDate;datetime
cdLetterOfGuaranteeAttributeTypeDesc;CreatedDate;datetime
cdLetterOfGuaranteeAttributeTypeDesc;LastUpdatedDate;datetime
cdLetterType;CreatedDate;datetime
cdLetterType;LastUpdatedDate;datetime
cdLetterTypeDesc;CreatedDate;datetime
cdLetterTypeDesc;LastUpdatedDate;datetime
cdLot;CreatedDate;datetime
cdLot;LastUpdatedDate;datetime
cdLotDesc;CreatedDate;datetime
cdLotDesc;LastUpdatedDate;datetime
cdLoyaltyProgram;CreatedDate;datetime
cdLoyaltyProgram;LastUpdatedDate;datetime
cdLoyaltyProgramDesc;CreatedDate;datetime
cdLoyaltyProgramDesc;LastUpdatedDate;datetime
cdLoyaltyProgramLevel;CreatedDate;datetime
cdLoyaltyProgramLevel;LastUpdatedDate;datetime
cdLoyaltyProgramLevelDesc;CreatedDate;datetime
cdLoyaltyProgramLevelDesc;LastUpdatedDate;datetime
cdLoyaltyProgramStatus;CreatedDate;datetime
cdLoyaltyProgramStatus;LastUpdatedDate;datetime
cdLoyaltyProgramStatusDesc;CreatedDate;datetime
cdLoyaltyProgramStatusDesc;LastUpdatedDate;datetime
cdLoyaltyProgramStatusModifyReason;CreatedDate;datetime
cdLoyaltyProgramStatusModifyReason;LastUpdatedDate;datetime
cdLoyaltyProgramStatusModifyReasonDesc;CreatedDate;datetime
cdLoyaltyProgramStatusModifyReasonDesc;LastUpdatedDate;datetime
cdMainJobTitle;CreatedDate;datetime
cdMainJobTitle;LastUpdatedDate;datetime
cdMainJobTitleDesc;CreatedDate;datetime
cdMainJobTitleDesc;LastUpdatedDate;datetime
cdMaladyType;CreatedDate;datetime
cdMaladyType;LastUpdatedDate;datetime
cdMaladyTypeDesc;CreatedDate;datetime
cdMaladyTypeDesc;LastUpdatedDate;datetime
cdManufacturer;CreatedDate;datetime
cdManufacturer;LastUpdatedDate;datetime
cdManufacturerDesc;CreatedDate;datetime
cdManufacturerDesc;LastUpdatedDate;datetime
cdMessageReason;CreatedDate;datetime
cdMessageReason;LastUpdatedDate;datetime
cdMessageReasonDesc;CreatedDate;datetime
cdMessageReasonDesc;LastUpdatedDate;datetime
cdMessageType;CreatedDate;datetime
cdMessageType;LastUpdatedDate;datetime
cdMessageTypeDesc;CreatedDate;datetime
cdMessageTypeDesc;LastUpdatedDate;datetime
cdMilitaryServiceStatus;CreatedDate;datetime
cdMilitaryServiceStatus;LastUpdatedDate;datetime
cdMilitaryServiceStatusDesc;CreatedDate;datetime
cdMilitaryServiceStatusDesc;LastUpdatedDate;datetime
cdMissingWorkReason;CreatedDate;datetime
cdMissingWorkReason;LastUpdatedDate;datetime
cdMissingWorkReasonDesc;CreatedDate;datetime
cdMissingWorkReasonDesc;LastUpdatedDate;datetime
cdMMSBusinessPartnerService;CreatedDate;datetime
cdMMSBusinessPartnerService;LastUpdatedDate;datetime
cdNationality;CreatedDate;datetime
cdNationality;LastUpdatedDate;datetime
cdNationalityDesc;CreatedDate;datetime
cdNationalityDesc;LastUpdatedDate;datetime
cdOffice;CreatedDate;datetime
cdOffice;LastUpdatedDate;datetime
cdOfficeCOGSGr;CreatedDate;datetime
cdOfficeCOGSGr;LastUpdatedDate;datetime
cdOfficeCOGSGrDesc;CreatedDate;datetime
cdOfficeCOGSGrDesc;LastUpdatedDate;datetime
cdOfficeDesc;CreatedDate;datetime
cdOfficeDesc;LastUpdatedDate;datetime
cdOnlineBankWebService;CreatedDate;datetime
cdOnlineBankWebService;LastUpdatedDate;datetime
cdOnlineBankWebService;StartDate;date
cdOnlineBankWebServiceDesc;CreatedDate;datetime
cdOnlineBankWebServiceDesc;LastUpdatedDate;datetime
cdOnlineDBSWebService;CreatedDate;datetime
cdOnlineDBSWebService;LastUpdatedDate;datetime
cdOnlineDBSWebServiceDesc;CreatedDate;datetime
cdOnlineDBSWebServiceDesc;LastUpdatedDate;datetime
cdOpticalGroupRange;CreatedDate;datetime
cdOpticalGroupRange;LastUpdatedDate;datetime
cdOpticalGroupRangeDesc;CreatedDate;datetime
cdOpticalGroupRangeDesc;LastUpdatedDate;datetime
cdOpticalSut;CreatedDate;datetime
cdOpticalSut;LastUpdatedDate;datetime
cdOpticalSutDesc;CreatedDate;datetime
cdOpticalSutDesc;LastUpdatedDate;datetime
cdOrderCancelReason;CreatedDate;datetime
cdOrderCancelReason;LastUpdatedDate;datetime
cdOrderCancelReasonDesc;CreatedDate;datetime
cdOrderCancelReasonDesc;LastUpdatedDate;datetime
cdOrderStatus;CreatedDate;datetime
cdOrderStatus;LastUpdatedDate;datetime
cdOrderStatusDesc;CreatedDate;datetime
cdOrderStatusDesc;LastUpdatedDate;datetime
cdOtherDocumentType;CreatedDate;datetime
cdOtherDocumentType;LastUpdatedDate;datetime
cdOtherDocumentTypeDesc;CreatedDate;datetime
cdOtherDocumentTypeDesc;LastUpdatedDate;datetime
cdPackageBrand;CreatedDate;datetime
cdPackageBrand;LastUpdatedDate;datetime
cdPackageBrandDesc;CreatedDate;datetime
cdPackageBrandDesc;LastUpdatedDate;datetime
cdPackageVolume;CreatedDate;datetime
cdPackageVolume;LastUpdatedDate;datetime
cdPackageVolumeDesc;CreatedDate;datetime
cdPackageVolumeDesc;LastUpdatedDate;datetime
cdPantone;CreatedDate;datetime
cdPantone;LastUpdatedDate;datetime
cdPantoneDesc;CreatedDate;datetime
cdPantoneDesc;LastUpdatedDate;datetime
cdPaymentMethod;CreatedDate;datetime
cdPaymentMethod;LastUpdatedDate;datetime
cdPaymentMethodDesc;CreatedDate;datetime
cdPaymentMethodDesc;LastUpdatedDate;datetime
cdPaymentPlan;CreatedDate;datetime
cdPaymentPlan;DueDate;date
cdPaymentPlan;LastUpdatedDate;datetime
cdPaymentPlanDesc;CreatedDate;datetime
cdPaymentPlanDesc;LastUpdatedDate;datetime
cdPaymentProvider;CreatedDate;datetime
cdPaymentProvider;LastUpdatedDate;datetime
cdPaymentProviderDesc;CreatedDate;datetime
cdPaymentProviderDesc;LastUpdatedDate;datetime
cdPayrollType;CreatedDate;datetime
cdPayrollType;LastUpdatedDate;datetime
cdPayrollTypeDesc;CreatedDate;datetime
cdPayrollTypeDesc;LastUpdatedDate;datetime
cdPCT;CreatedDate;datetime
cdPCT;LastUpdatedDate;datetime
cdPCTDesc;CreatedDate;datetime
cdPCTDesc;LastUpdatedDate;datetime
cdPerceptionOfFashion;CreatedDate;datetime
cdPerceptionOfFashion;LastUpdatedDate;datetime
cdPerceptionOfFashionDesc;CreatedDate;datetime
cdPerceptionOfFashionDesc;LastUpdatedDate;datetime
cdPermissionMarketingService;CreatedDate;datetime
cdPermissionMarketingService;LastUpdatedDate;datetime
cdPlasticBagType;CreatedDate;datetime
cdPlasticBagType;LastUpdatedDate;datetime
cdPlasticBagTypeDesc;CreatedDate;datetime
cdPlasticBagTypeDesc;LastUpdatedDate;datetime
cdPointModifyReason;CreatedDate;datetime
cdPointModifyReason;LastUpdatedDate;datetime
cdPointModifyReasonDesc;CreatedDate;datetime
cdPointModifyReasonDesc;LastUpdatedDate;datetime
cdPort;CreatedDate;datetime
cdPort;LastUpdatedDate;datetime
cdPortDesc;CreatedDate;datetime
cdPortDesc;LastUpdatedDate;datetime
cdPOSTerminal;CreatedDate;datetime
cdPOSTerminal;LastUpdatedDate;datetime
cdPresentCardType;CreatedDate;datetime
cdPresentCardType;LastUpdatedDate;datetime
cdPresentCardTypeDesc;CreatedDate;datetime
cdPresentCardTypeDesc;LastUpdatedDate;datetime
cdPrevJobType;CreatedDate;datetime
cdPrevJobType;LastUpdatedDate;datetime
cdPrevJobTypeDesc;CreatedDate;datetime
cdPrevJobTypeDesc;LastUpdatedDate;datetime
cdPriceGroup;CreatedDate;datetime
cdPriceGroup;LastUpdatedDate;datetime
cdPriceGroupDesc;CreatedDate;datetime
cdPriceGroupDesc;LastUpdatedDate;datetime
cdPriceListType;CreatedDate;datetime
cdPriceListType;LastUpdatedDate;datetime
cdPriceListTypeDesc;CreatedDate;datetime
cdPriceListTypeDesc;LastUpdatedDate;datetime
cdPriority;CreatedDate;datetime
cdPriority;LastUpdatedDate;datetime
cdPriorityDesc;CreatedDate;datetime
cdPriorityDesc;LastUpdatedDate;datetime
cdPrivateInsurance;CreatedDate;datetime
cdPrivateInsurance;LastUpdatedDate;datetime
cdPrivateInsuranceDesc;CreatedDate;datetime
cdPrivateInsuranceDesc;LastUpdatedDate;datetime
cdProcessFlowDenyReason;CreatedDate;datetime
cdProcessFlowDenyReason;LastUpdatedDate;datetime
cdProcessFlowDenyReasonDesc;CreatedDate;datetime
cdProcessFlowDenyReasonDesc;LastUpdatedDate;datetime
cdProductCollectionGr;CreatedDate;datetime
cdProductCollectionGr;LastUpdatedDate;datetime
cdProductColorAttribute;CreatedDate;datetime
cdProductColorAttribute;LastUpdatedDate;datetime
cdProductColorAttributeDesc;CreatedDate;datetime
cdProductColorAttributeDesc;LastUpdatedDate;datetime
cdProductColorAttributeType;CreatedDate;datetime
cdProductColorAttributeType;LastUpdatedDate;datetime
cdProductColorAttributeTypeDesc;CreatedDate;datetime
cdProductColorAttributeTypeDesc;LastUpdatedDate;datetime
cdProductColorSet;CreatedDate;datetime
cdProductColorSet;LastUpdatedDate;datetime
cdProductColorSetDesc;CreatedDate;datetime
cdProductColorSetDesc;LastUpdatedDate;datetime
cdProductDimSet;CreatedDate;datetime
cdProductDimSet;LastUpdatedDate;datetime
cdProductDimSetDesc;CreatedDate;datetime
cdProductDimSetDesc;LastUpdatedDate;datetime
cdProductHierarchyLevel;CreatedDate;datetime
cdProductHierarchyLevel;LastUpdatedDate;datetime
cdProductHierarchyLevelDesc;CreatedDate;datetime
cdProductHierarchyLevelDesc;LastUpdatedDate;datetime
cdProductPart;CreatedDate;datetime
cdProductPart;LastUpdatedDate;datetime
cdProductPartDesc;CreatedDate;datetime
cdProductPartDesc;LastUpdatedDate;datetime
cdProductPointType;CreatedDate;datetime
cdProductPointType;LastUpdatedDate;datetime
cdProductPointTypeDesc;CreatedDate;datetime
cdProductPointTypeDesc;LastUpdatedDate;datetime
cdProductStatus;CreatedDate;datetime
cdProductStatus;LastUpdatedDate;datetime
cdProductStatusDesc;CreatedDate;datetime
cdProductStatusDesc;LastUpdatedDate;datetime
cdPromotionGroup;CreatedDate;datetime
cdPromotionGroup;LastUpdatedDate;datetime
cdPromotionGroupDesc;CreatedDate;datetime
cdPromotionGroupDesc;LastUpdatedDate;datetime
cdProposalConfirmationLimit;CreatedDate;datetime
cdProposalConfirmationLimit;LastUpdatedDate;datetime
cdProposalConfirmationRule;CreatedDate;datetime
cdProposalConfirmationRule;LastUpdatedDate;datetime
cdPurchasePlan;CreatedDate;datetime
cdPurchasePlan;LastUpdatedDate;datetime
cdPurchasePlanDesc;CreatedDate;datetime
cdPurchasePlanDesc;LastUpdatedDate;datetime
cdPurchasingAgent;CreatedDate;datetime
cdPurchasingAgent;LastUpdatedDate;datetime
cdQuarter;CreatedDate;datetime
cdQuarter;LastUpdatedDate;datetime
cdReasonForNotShopping;CreatedDate;datetime
cdReasonForNotShopping;LastUpdatedDate;datetime
cdReasonForNotShoppingDesc;CreatedDate;datetime
cdReasonForNotShoppingDesc;LastUpdatedDate;datetime
cdRecidivistType;CreatedDate;datetime
cdRecidivistType;LastUpdatedDate;datetime
cdRecidivistTypeDesc;CreatedDate;datetime
cdRecidivistTypeDesc;LastUpdatedDate;datetime
cdReconciliation;CreatedDate;datetime
cdReconciliation;LastUpdatedDate;datetime
cdReconciliationDesc;CreatedDate;datetime
cdReconciliationDesc;LastUpdatedDate;datetime
cdRegisteredEMailService;CreatedDate;datetime
cdRegisteredEMailService;LastUpdatedDate;datetime
cdRequisition;CreatedDate;datetime
cdRequisition;LastUpdatedDate;datetime
cdRequisitionAttribute;CreatedDate;datetime
cdRequisitionAttribute;LastUpdatedDate;datetime
cdRequisitionAttributeDesc;CreatedDate;datetime
cdRequisitionAttributeDesc;LastUpdatedDate;datetime
cdRequisitionAttributeType;CreatedDate;datetime
cdRequisitionAttributeType;LastUpdatedDate;datetime
cdRequisitionAttributeTypeDesc;CreatedDate;datetime
cdRequisitionAttributeTypeDesc;LastUpdatedDate;datetime
cdRequisitionConfirmationLimit;CreatedDate;datetime
cdRequisitionConfirmationLimit;LastUpdatedDate;datetime
cdRequisitionConfirmationRule;CreatedDate;datetime
cdRequisitionConfirmationRule;LastUpdatedDate;datetime
cdRequisitionDesc;CreatedDate;datetime
cdRequisitionDesc;LastUpdatedDate;datetime
cdRequisitionType;CreatedDate;datetime
cdRequisitionType;LastUpdatedDate;datetime
cdRequisitionTypeDesc;CreatedDate;datetime
cdRequisitionTypeDesc;LastUpdatedDate;datetime
cdResignation;CreatedDate;datetime
cdResignation;LastUpdatedDate;datetime
cdResignationDesc;CreatedDate;datetime
cdResignationDesc;LastUpdatedDate;datetime
cdResponsibilityArea;CreatedDate;datetime
cdResponsibilityArea;LastUpdatedDate;datetime
cdResponsibilityAreaDesc;CreatedDate;datetime
cdResponsibilityAreaDesc;LastUpdatedDate;datetime
cdReturnReason;CreatedDate;datetime
cdReturnReason;LastUpdatedDate;datetime
cdReturnReasonDesc;CreatedDate;datetime
cdReturnReasonDesc;LastUpdatedDate;datetime
cdRole;CreatedDate;datetime
cdRole;LastUpdatedDate;datetime
cdRoleDesc;CreatedDate;datetime
cdRoleDesc;LastUpdatedDate;datetime
cdRoll;CreatedDate;datetime
cdRoll;LastUpdatedDate;datetime
cdRollNoteType;CreatedDate;datetime
cdRollNoteType;LastUpdatedDate;datetime
cdRollNoteTypeDesc;CreatedDate;datetime
cdRollNoteTypeDesc;LastUpdatedDate;datetime
cdRoundsman;CreatedDate;datetime
cdRoundsman;LastUpdatedDate;datetime
cdSalesChannel;CreatedDate;datetime
cdSalesChannel;LastUpdatedDate;datetime
cdSalesChannelDesc;CreatedDate;datetime
cdSalesChannelDesc;LastUpdatedDate;datetime
cdSalesperson;CreatedDate;datetime
cdSalesperson;LastUpdatedDate;datetime
cdSalespersonTeam;CreatedDate;datetime
cdSalespersonTeam;LastUpdatedDate;datetime
cdSalespersonTeamDesc;CreatedDate;datetime
cdSalespersonTeamDesc;LastUpdatedDate;datetime
cdSalespersonType;CreatedDate;datetime
cdSalespersonType;LastUpdatedDate;datetime
cdSalespersonTypeDesc;CreatedDate;datetime
cdSalespersonTypeDesc;LastUpdatedDate;datetime
cdSalesPlanType;CreatedDate;datetime
cdSalesPlanType;LastUpdatedDate;datetime
cdSalesPlanTypeDesc;CreatedDate;datetime
cdSalesPlanTypeDesc;LastUpdatedDate;datetime
cdScheduleReSendSMSForCustomerRelationship;CreatedDate;datetime
cdScheduleReSendSMSForCustomerRelationship;LastUpdatedDate;datetime
cdScheduleSMSForCustomerRelationship;CreatedDate;datetime
cdScheduleSMSForCustomerRelationship;LastUpdatedDate;datetime
cdScrapReason;CreatedDate;datetime
cdScrapReason;LastUpdatedDate;datetime
cdScrapReasonDesc;CreatedDate;datetime
cdScrapReasonDesc;LastUpdatedDate;datetime
cdSeason;CreatedDate;datetime
cdSeason;EndDate;smalldatetime
cdSeason;LastUpdatedDate;datetime
cdSeason;StartDate;smalldatetime
cdSeasonDesc;CreatedDate;datetime
cdSeasonDesc;LastUpdatedDate;datetime
cdSectionType;CreatedDate;datetime
cdSectionType;LastUpdatedDate;datetime
cdSectionTypeDesc;CreatedDate;datetime
cdSectionTypeDesc;LastUpdatedDate;datetime
cdServiceman;CreatedDate;datetime
cdServiceman;LastUpdatedDate;datetime
cdSGKBorrowingType;CreatedDate;datetime
cdSGKBorrowingType;LastUpdatedDate;datetime
cdSGKBorrowingTypeDesc;CreatedDate;datetime
cdSGKBorrowingTypeDesc;LastUpdatedDate;datetime
cdSGKProfession;CreatedDate;datetime
cdSGKProfession;LastUpdatedDate;datetime
cdSGKProfessionDesc;CreatedDate;datetime
cdSGKProfessionDesc;LastUpdatedDate;datetime
cdShipmentMethod;CreatedDate;datetime
cdShipmentMethod;LastUpdatedDate;datetime
cdShipmentMethodDesc;CreatedDate;datetime
cdShipmentMethodDesc;LastUpdatedDate;datetime
cdSMSGatewayService;CreatedDate;datetime
cdSMSGatewayService;LastUpdatedDate;datetime
cdSMSJobType;CreatedDate;datetime
cdSMSJobType;LastUpdatedDate;datetime
cdSMSJobTypeDesc;CreatedDate;datetime
cdSMSJobTypeDesc;LastUpdatedDate;datetime
cdSoftware;CreatedDate;datetime
cdSoftware;LastUpdatedDate;datetime
cdSoftwareDesc;CreatedDate;datetime
cdSoftwareDesc;LastUpdatedDate;datetime
cdSoftwareType;CreatedDate;datetime
cdSoftwareType;LastUpdatedDate;datetime
cdSoftwareTypeDesc;CreatedDate;datetime
cdSoftwareTypeDesc;LastUpdatedDate;datetime
cdSpecialDayType;CreatedDate;datetime
cdSpecialDayType;LastUpdatedDate;datetime
cdSpecialDayTypeDesc;CreatedDate;datetime
cdSpecialDayTypeDesc;LastUpdatedDate;datetime
cdState;CreatedDate;datetime
cdState;LastUpdatedDate;datetime
cdStateDesc;CreatedDate;datetime
cdStateDesc;LastUpdatedDate;datetime
cdStoreCapacityLevel;CreatedDate;datetime
cdStoreCapacityLevel;LastUpdatedDate;datetime
cdStoreCapacityLevelDesc;CreatedDate;datetime
cdStoreCapacityLevelDesc;LastUpdatedDate;datetime
cdStoreClimateZone;CreatedDate;datetime
cdStoreClimateZone;LastUpdatedDate;datetime
cdStoreClimateZoneDesc;CreatedDate;datetime
cdStoreClimateZoneDesc;LastUpdatedDate;datetime
cdStoreConcept;CreatedDate;datetime
cdStoreConcept;LastUpdatedDate;datetime
cdStoreConceptDesc;CreatedDate;datetime
cdStoreConceptDesc;LastUpdatedDate;datetime
cdStoreCRMGroup;CreatedDate;datetime
cdStoreCRMGroup;LastUpdatedDate;datetime
cdStoreCRMGroupDesc;CreatedDate;datetime
cdStoreCRMGroupDesc;LastUpdatedDate;datetime
cdStoreDistributionGroup;CreatedDate;datetime
cdStoreDistributionGroup;LastUpdatedDate;datetime
cdStoreDistributionGroupDesc;CreatedDate;datetime
cdStoreDistributionGroupDesc;LastUpdatedDate;datetime
cdStoreHierarchyLevel;CreatedDate;datetime
cdStoreHierarchyLevel;LastUpdatedDate;datetime
cdStoreHierarchyLevelDesc;CreatedDate;datetime
cdStoreHierarchyLevelDesc;LastUpdatedDate;datetime
cdStorePriceLevel;CreatedDate;datetime
cdStorePriceLevel;LastUpdatedDate;datetime
cdStorePriceLevelDesc;CreatedDate;datetime
cdStorePriceLevelDesc;LastUpdatedDate;datetime
cdStoryBoard;CreatedDate;datetime
cdStoryBoard;LastUpdatedDate;datetime
cdStoryBoardDesc;CreatedDate;datetime
cdStoryBoardDesc;LastUpdatedDate;datetime
cdStreet;CreatedDate;datetime
cdStreet;LastUpdatedDate;datetime
cdSubCurrAccAttribute;CreatedDate;datetime
cdSubCurrAccAttribute;LastUpdatedDate;datetime
cdSubCurrAccAttributeDesc;CreatedDate;datetime
cdSubCurrAccAttributeDesc;LastUpdatedDate;datetime
cdSubCurrAccAttributeType;CreatedDate;datetime
cdSubCurrAccAttributeType;LastUpdatedDate;datetime
cdSubCurrAccAttributeTypeDesc;CreatedDate;datetime
cdSubCurrAccAttributeTypeDesc;LastUpdatedDate;datetime
cdSubJobDepartment;CreatedDate;datetime
cdSubJobDepartment;LastUpdatedDate;datetime
cdSubJobDepartmentDesc;CreatedDate;datetime
cdSubJobDepartmentDesc;LastUpdatedDate;datetime
cdSubSeason;CreatedDate;datetime
cdSubSeason;EndDate;smalldatetime
cdSubSeason;LastUpdatedDate;datetime
cdSubSeason;StartDate;smalldatetime
cdSubSeasonDesc;CreatedDate;datetime
cdSubSeasonDesc;LastUpdatedDate;datetime
cdSupportResolveType;CreatedDate;datetime
cdSupportResolveType;LastUpdatedDate;datetime
cdSupportResolveTypeDesc;CreatedDate;datetime
cdSupportResolveTypeDesc;LastUpdatedDate;datetime
cdSupportStatus;CreatedDate;datetime
cdSupportStatus;LastUpdatedDate;datetime
cdSupportStatusDesc;CreatedDate;datetime
cdSupportStatusDesc;LastUpdatedDate;datetime
cdSurvey;CreatedDate;datetime
cdSurvey;LastUpdatedDate;datetime
cdSurveyDesc;CreatedDate;datetime
cdSurveyDesc;LastUpdatedDate;datetime
cdSurveyQuestion;CreatedDate;datetime
cdSurveyQuestion;LastUpdatedDate;datetime
cdSurveyQuestionDesc;CreatedDate;datetime
cdSurveyQuestionDesc;LastUpdatedDate;datetime
cdSurveyQuestionOption;CreatedDate;datetime
cdSurveyQuestionOption;LastUpdatedDate;datetime
cdSurveyQuestionOptionDesc;CreatedDate;datetime
cdSurveyQuestionOptionDesc;LastUpdatedDate;datetime
cdSurveySection;CreatedDate;datetime
cdSurveySection;LastUpdatedDate;datetime
cdSurveySectionDesc;CreatedDate;datetime
cdSurveySectionDesc;LastUpdatedDate;datetime
cdTaxDistrict;CreatedDate;datetime
cdTaxDistrict;LastUpdatedDate;datetime
cdTaxDistrictDesc;CreatedDate;datetime
cdTaxDistrictDesc;LastUpdatedDate;datetime
cdTaxOffice;CreatedDate;datetime
cdTaxOffice;LastUpdatedDate;datetime
cdTaxOfficeDesc;CreatedDate;datetime
cdTaxOfficeDesc;LastUpdatedDate;datetime
cdTechnicalResponsible;CreatedDate;datetime
cdTechnicalResponsible;LastUpdatedDate;datetime
cdTest;CreatedDate;datetime
cdTest;LastUpdatedDate;datetime
cdTestDesc;CreatedDate;datetime
cdTestDesc;LastUpdatedDate;datetime
cdTestType;CreatedDate;datetime
cdTestType;LastUpdatedDate;datetime
cdTestTypeDesc;CreatedDate;datetime
cdTestTypeDesc;LastUpdatedDate;datetime
cdTextileCareSymbol;CreatedDate;datetime
cdTextileCareSymbol;LastUpdatedDate;datetime
cdTextileCareSymbolDesc;CreatedDate;datetime
cdTextileCareSymbolDesc;LastUpdatedDate;datetime
cdTimePeriod;CreatedDate;datetime
cdTimePeriod;EndDate;smalldatetime
cdTimePeriod;LastUpdatedDate;datetime
cdTimePeriod;StartDate;smalldatetime
cdTimePeriodDesc;CreatedDate;datetime
cdTimePeriodDesc;LastUpdatedDate;datetime
cdTitle;CreatedDate;datetime
cdTitle;LastUpdatedDate;datetime
cdTitleDesc;CreatedDate;datetime
cdTitleDesc;LastUpdatedDate;datetime
cdTradeRegistryOffice;CreatedDate;datetime
cdTradeRegistryOffice;LastUpdatedDate;datetime
cdTransactionCancelReason;CreatedDate;datetime
cdTransactionCancelReason;LastUpdatedDate;datetime
cdTransactionCancelReasonDesc;CreatedDate;datetime
cdTransactionCancelReasonDesc;LastUpdatedDate;datetime
cdTransferPlanTemplate;CreatedDate;datetime
cdTransferPlanTemplate;LastUpdatedDate;datetime
cdTransferPlanTemplate;TemplateDate;date
cdTransferPlanTemplateDesc;CreatedDate;datetime
cdTransferPlanTemplateDesc;LastUpdatedDate;datetime
cdTranslationProvider;CreatedDate;datetime
cdTranslationProvider;LastUpdatedDate;datetime
cdTurnoverTargetType;CreatedDate;datetime
cdTurnoverTargetType;LastUpdatedDate;datetime
cdTurnoverTargetTypeDesc;CreatedDate;datetime
cdTurnoverTargetTypeDesc;LastUpdatedDate;datetime
cdUnDeliveryReason;CreatedDate;datetime
cdUnDeliveryReason;LastUpdatedDate;datetime
cdUnDeliveryReasonDesc;CreatedDate;datetime
cdUnDeliveryReasonDesc;LastUpdatedDate;datetime
cdUniFreeTenderType;CreatedDate;datetime
cdUniFreeTenderType;LastUpdatedDate;datetime
cdUnitOfMeasure;CreatedDate;datetime
cdUnitOfMeasure;LastUpdatedDate;datetime
cdUnitOfMeasureDesc;CreatedDate;datetime
cdUnitOfMeasureDesc;LastUpdatedDate;datetime
cdUniversity;CreatedDate;datetime
cdUniversity;LastUpdatedDate;datetime
cdUniversityDesc;CreatedDate;datetime
cdUniversityDesc;LastUpdatedDate;datetime
cdUniversityFaculty;CreatedDate;datetime
cdUniversityFaculty;LastUpdatedDate;datetime
cdUniversityFacultyDep;CreatedDate;datetime
cdUniversityFacultyDep;LastUpdatedDate;datetime
cdUniversityFacultyDepDesc;CreatedDate;datetime
cdUniversityFacultyDepDesc;LastUpdatedDate;datetime
cdUniversityFacultyDesc;CreatedDate;datetime
cdUniversityFacultyDesc;LastUpdatedDate;datetime
cdUniversityLevel;CreatedDate;datetime
cdUniversityLevel;LastUpdatedDate;datetime
cdUniversityLevelDesc;CreatedDate;datetime
cdUniversityLevelDesc;LastUpdatedDate;datetime
cdUniversityType;CreatedDate;datetime
cdUniversityType;LastUpdatedDate;datetime
cdUniversityTypeDesc;CreatedDate;datetime
cdUniversityTypeDesc;LastUpdatedDate;datetime
cdUserWarning;CreatedDate;datetime
cdUserWarning;LastUpdatedDate;datetime
cdUserWarningDesc;CreatedDate;datetime
cdUserWarningDesc;LastUpdatedDate;datetime
cdUTSAttribute;CreatedDate;datetime
cdUTSAttribute;LastUpdatedDate;datetime
cdUTSMRGInfo;CreatedDate;datetime
cdUTSMRGInfo;LastUpdatedDate;datetime
cdVat;CreatedDate;datetime
cdVat;LastUpdatedDate;datetime
cdVatDesc;CreatedDate;datetime
cdVatDesc;LastUpdatedDate;datetime
cdVehicle;CreatedDate;datetime
cdVehicle;LastUpdatedDate;datetime
cdVehicleType;CreatedDate;datetime
cdVehicleType;LastUpdatedDate;datetime
cdVehicleTypeDesc;CreatedDate;datetime
cdVehicleTypeDesc;LastUpdatedDate;datetime
cdVendorPaymentPlanGr;CreatedDate;datetime
cdVendorPaymentPlanGr;LastUpdatedDate;datetime
cdVendorPaymentPlanGrDesc;CreatedDate;datetime
cdVendorPaymentPlanGrDesc;LastUpdatedDate;datetime
cdVisitFrequency;CreatedDate;datetime
cdVisitFrequency;LastUpdatedDate;datetime
cdVisitFrequencyDesc;CreatedDate;datetime
cdVisitFrequencyDesc;LastUpdatedDate;datetime
cdWageGarnishmentType;CreatedDate;datetime
cdWageGarnishmentType;LastUpdatedDate;datetime
cdWageGarnishmentTypeDesc;CreatedDate;datetime
cdWageGarnishmentTypeDesc;LastUpdatedDate;datetime
cdWagePlanType;CreatedDate;datetime
cdWagePlanType;LastUpdatedDate;datetime
cdWagePlanTypeDesc;CreatedDate;datetime
cdWagePlanTypeDesc;LastUpdatedDate;datetime
cdWarehouse;CreatedDate;datetime
cdWarehouse;LastUpdatedDate;datetime
cdWarehouseCategory;CreatedDate;datetime
cdWarehouseCategory;LastUpdatedDate;datetime
cdWarehouseCategoryDesc;CreatedDate;datetime
cdWarehouseCategoryDesc;LastUpdatedDate;datetime
cdWarehouseChannelTemplate;CreatedDate;datetime
cdWarehouseChannelTemplate;LastUpdatedDate;datetime
cdWarehouseChannelTemplateDesc;CreatedDate;datetime
cdWarehouseChannelTemplateDesc;LastUpdatedDate;datetime
cdWarehouseDesc;CreatedDate;datetime
cdWarehouseDesc;LastUpdatedDate;datetime
cdWarehouseType;CreatedDate;datetime
cdWarehouseType;LastUpdatedDate;datetime
cdWarehouseTypeDesc;CreatedDate;datetime
cdWarehouseTypeDesc;LastUpdatedDate;datetime
cdWorkForce;CreatedDate;datetime
cdWorkForce;LastUpdatedDate;datetime
cdWorkForceDesc;CreatedDate;datetime
cdWorkForceDesc;LastUpdatedDate;datetime
cdWorkPlace;CreatedDate;datetime
cdWorkPlace;LastUpdatedDate;datetime
cdWorkPlace;WorkplaceClosingDate;date
cdWorkPlace;WorkplaceOpeningDate;date
cdWorkPlaceDesc;CreatedDate;datetime
cdWorkPlaceDesc;LastUpdatedDate;datetime
cdWorkPlaceGroup;CreatedDate;datetime
cdWorkPlaceGroup;LastUpdatedDate;datetime
cdWorkPlaceGroupDesc;CreatedDate;datetime
cdWorkPlaceGroupDesc;LastUpdatedDate;datetime
cdWorkPlaceType;CreatedDate;datetime
cdWorkPlaceType;LastUpdatedDate;datetime
cdWorkPlaceTypeDesc;CreatedDate;datetime
cdWorkPlaceTypeDesc;LastUpdatedDate;datetime
cdZone;CreatedDate;datetime
cdZone;LastUpdatedDate;datetime
cdZoneDesc;CreatedDate;datetime
cdZoneDesc;LastUpdatedDate;datetime
dfAgentContractProductLevels;CreatedDate;datetime
dfAgentContractProductLevels;LastUpdatedDate;datetime
dfAirportExchangeRateWidgetParameters;CreatedDate;datetime
dfAirportExchangeRateWidgetParameters;LastUpdatedDate;datetime
dfAttTypesForMarketPlaceCategory;CreatedDate;datetime
dfAttTypesForMarketPlaceCategory;LastUpdatedDate;datetime
dfAvailableTaxTypesOnPos;CreatedDate;datetime
dfAvailableTaxTypesOnPos;LastUpdatedDate;datetime
dfBankCreditOfficialForm;CreatedDate;datetime
dfBankCreditOfficialForm;LastUpdatedDate;datetime
dfBankDefATAttribute;CreatedDate;datetime
dfBankDefATAttribute;LastUpdatedDate;datetime
dfBankOfficialForm;CreatedDate;datetime
dfBankOfficialForm;LastUpdatedDate;datetime
dfBankPaymentInstructionOfficialForm;CreatedDate;datetime
dfBankPaymentInstructionOfficialForm;LastUpdatedDate;datetime
dfBankPaymentListOfficialForm;CreatedDate;datetime
dfBankPaymentListOfficialForm;LastUpdatedDate;datetime
dfBankPOSReturnsRule;CreatedDate;datetime
dfBankPOSReturnsRule;LastUpdatedDate;datetime
dfBankPOSReturnsRule;ValidDate;date
dfBankPOSReturnsTermRule;CreatedDate;datetime
dfBankPOSReturnsTermRule;LastUpdatedDate;datetime
dfBasefyStore;CreatedDate;datetime
dfBasefyStore;LastUpdatedDate;datetime
dfBulkMailServiceProviderAccount;CreatedDate;datetime
dfBulkMailServiceProviderAccount;LastUpdatedDate;datetime
dfBulutTahsilatVPosCompany;CreatedDate;datetime
dfBulutTahsilatVPosCompany;LastUpdatedDate;datetime
dfBulutTahsilatVPosOffice;CreatedDate;datetime
dfBulutTahsilatVPosOffice;LastUpdatedDate;datetime
dfCarriageExpenseCodes;CreatedDate;datetime
dfCarriageExpenseCodes;LastUpdatedDate;datetime
dfCashDefATAttribute;CreatedDate;datetime
dfCashDefATAttribute;LastUpdatedDate;datetime
dfCashOfficialForm;CreatedDate;datetime
dfCashOfficialForm;LastUpdatedDate;datetime
dfChequeDefATAttribute;CreatedDate;datetime
dfChequeDefATAttribute;LastUpdatedDate;datetime
dfChequeOfficialForm;CreatedDate;datetime
dfChequeOfficialForm;LastUpdatedDate;datetime
dfChippin;CreatedDate;datetime
dfChippin;LastUpdatedDate;datetime
dfChippinPOSTerminal;CreatedDate;datetime
dfChippinPOSTerminal;LastUpdatedDate;datetime
dfCommunicationForm;CreatedDate;datetime
dfCommunicationForm;LastUpdatedDate;datetime
dfCOMOPOSTerminal;CreatedDate;datetime
dfCOMOPOSTerminal;LastUpdatedDate;datetime
dfCOMOProductProperty;CreatedDate;datetime
dfCOMOProductProperty;LastUpdatedDate;datetime
dfCOMOStore;CreatedDate;datetime
dfCOMOStore;LastUpdatedDate;datetime
dfCompanyClosedPeriod;CreatedDate;datetime
dfCompanyClosedPeriod;LastUpdatedDate;datetime
dfCompanyCostOfGoodsSold;CreatedDate;datetime
dfCompanyCostOfGoodsSold;LastUpdatedDate;datetime
dfCompanyCurrAccSize;CreatedDate;datetime
dfCompanyCurrAccSize;LastUpdatedDate;datetime
dfCompanyDeductionDefault;ClosedDate;date
dfCompanyDeductionDefault;CreatedDate;datetime
dfCompanyDeductionDefault;LastUpdatedDate;datetime
dfCompanyDefault;CreatedDate;datetime
dfCompanyDefault;EArchiveStartDate;date
dfCompanyDefault;ELedgerStartDate;date
dfCompanyDefault;EShipmentStartDate;date
dfCompanyDefault;ExportSalesEInvoiceStartDate;date
dfCompanyDefault;IYSIntegrationStartDate;date
dfCompanyDefault;LastUpdatedDate;datetime
dfCompanyDefault;TaxFreeEInvoiceStartDate;date
dfCompanyDefault;UTSStartDate;date
dfCompanyDefault;UTSTransitionDate;date
dfCompanyDigitalMarketingServiceAdress;CreatedDate;datetime
dfCompanyDigitalMarketingServiceAdress;LastUpdatedDate;datetime
dfCompanyEarningsDefault;ClosedDate;date
dfCompanyEarningsDefault;CreatedDate;datetime
dfCompanyEarningsDefault;LastUpdatedDate;datetime
dfCompanyEarningsMonthly;CreatedDate;datetime
dfCompanyEarningsMonthly;LastUpdatedDate;datetime
dfCompanyEmailDefault;CreatedDate;datetime
dfCompanyEmailDefault;LastUpdatedDate;datetime
dfCompanyFolder;CreatedDate;datetime
dfCompanyFolder;LastUpdatedDate;datetime
dfCompanyLockTransaction;CreatedDate;datetime
dfCompanyLockTransaction;LastUpdatedDate;datetime
dfCompanyLockTransaction;LockTransAfterThisDate;date
dfCompanyLockTransaction;LockTransBeforeThisDate;date
dfCompanyLoyaltyProgram;CreatedDate;datetime
dfCompanyLoyaltyProgram;LastUpdatedDate;datetime
dfCompanyMarkup;CreatedDate;datetime
dfCompanyMarkup;LastUpdatedDate;datetime
dfCompanyPriceGroup;CreatedDate;datetime
dfCompanyPriceGroup;LastUpdatedDate;datetime
dfCompanyProcessLockTransaction;CreatedDate;datetime
dfCompanyProcessLockTransaction;LastUpdatedDate;datetime
dfCompanyProcessLockTransaction;LockTransAfterThisDate;date
dfCompanyProcessLockTransaction;LockTransBeforeThisDate;date
dfConsignmentStore;CreatedDate;datetime
dfConsignmentStore;LastUpdatedDate;datetime
dfConsStoreDistributionWarehouse;CreatedDate;datetime
dfConsStoreDistributionWarehouse;LastUpdatedDate;datetime
dfCreditableConfirmation;CreatedDate;datetime
dfCreditableConfirmation;LastUpdatedDate;datetime
dfCreditCardPaymentDefATAttribute;CreatedDate;datetime
dfCreditCardPaymentDefATAttribute;LastUpdatedDate;datetime
dfCreditCardPaymentOfficialForm;CreatedDate;datetime
dfCreditCardPaymentOfficialForm;LastUpdatedDate;datetime
dfCurrAccProductLotLevels;CreatedDate;datetime
dfCurrAccProductLotLevels;LastUpdatedDate;datetime
dfCustomerOnlinePayment;CreatedDate;datetime
dfCustomerOnlinePayment;LastUpdatedDate;datetime
dfCustomizedDiscountEngineCompany;CreatedDate;datetime
dfCustomizedDiscountEngineCompany;LastUpdatedDate;datetime
dfCustomsCompany;CreatedDate;datetime
dfCustomsCompany;LastUpdatedDate;datetime
dfCustomsStore;CreatedDate;datetime
dfCustomsStore;LastUpdatedDate;datetime
dfDebitOfficialForm;CreatedDate;datetime
dfDebitOfficialForm;LastUpdatedDate;datetime
dfDepartmentReceiptOfficialForm;CreatedDate;datetime
dfDepartmentReceiptOfficialForm;LastUpdatedDate;datetime
dfDMSCompany;CreatedDate;datetime
dfDMSCompany;LastUpdatedDate;datetime
dfDMSPOSTerminal;CreatedDate;datetime
dfDMSPOSTerminal;LastUpdatedDate;datetime
dfDMSStore;CreatedDate;datetime
dfDMSStore;LastUpdatedDate;datetime
dfDomesticPPI;CreatedDate;datetime
dfDomesticPPI;LastUpdatedDate;datetime
dfDufryCompany;CreatedDate;datetime
dfDufryCompany;LastUpdatedDate;datetime
dfDufryStore;CreatedDate;datetime
dfDufryStore;LastUpdatedDate;datetime
dfEArchiveOfficialForm;CreatedDate;datetime
dfEArchiveOfficialForm;LastUpdatedDate;datetime
dfEArchiveWebServiceParameters;CreatedDate;datetime
dfEArchiveWebServiceParameters;LastUpdatedDate;datetime
dfEInvoiceOfficialForm;CreatedDate;datetime
dfEInvoiceOfficialForm;LastUpdatedDate;datetime
dfEInvoiceWebServiceParameters;CreatedDate;datetime
dfEInvoiceWebServiceParameters;LastUpdatedDate;datetime
dfEShipmentOfficialForm;CreatedDate;datetime
dfEShipmentOfficialForm;LastUpdatedDate;datetime
dfEShipmentWebServiceParameters;CreatedDate;datetime
dfEShipmentWebServiceParameters;LastUpdatedDate;datetime
dfExpenseSlipForm;CreatedDate;datetime
dfExpenseSlipForm;LastUpdatedDate;datetime
dffastPayCompany;CreatedDate;datetime
dffastPayCompany;LastUpdatedDate;datetime
dffastPayPosTerminal;CreatedDate;datetime
dffastPayPosTerminal;LastUpdatedDate;datetime
dffastPayStore;CreatedDate;datetime
dffastPayStore;LastUpdatedDate;datetime
dfFixedAssetReassessmentRates;CreatedDate;datetime
dfFixedAssetReassessmentRates;LastUpdatedDate;datetime
dfGetirCarsiCompany;CreatedDate;datetime
dfGetirCarsiCompany;LastUpdatedDate;datetime
dfGetirCarsiOrderCancelReasonConvert;CreatedDate;datetime
dfGetirCarsiOrderCancelReasonConvert;LastUpdatedDate;datetime
dfGetirCarsiStore;CreatedDate;datetime
dfGetirCarsiStore;LastUpdatedDate;datetime
dfGLClosedYear;CreatedDate;datetime
dfGLClosedYear;LastUpdatedDate;datetime
dfGlobalBlueCompany;CreatedDate;datetime
dfGlobalBlueCompany;LastUpdatedDate;datetime
dfGlobalBlueStore;CreatedDate;datetime
dfGlobalBlueStore;LastUpdatedDate;datetime
dfGlobalDataMatrix;CreatedDate;datetime
dfGlobalDataMatrix;LastUpdatedDate;datetime
dfGlobalDefault;CreatedDate;datetime
dfGlobalDefault;LastUpdatedDate;datetime
dfGlobalFolder;CreatedDate;datetime
dfGlobalFolder;LastUpdatedDate;datetime
dfGlobalItemSize;CreatedDate;datetime
dfGlobalItemSize;LastUpdatedDate;datetime
dfGlobalMernisUser;CreatedDate;datetime
dfGlobalMernisUser;LastUpdatedDate;datetime
dfGuidedSalesCustomerParameters;CreatedDate;datetime
dfGuidedSalesCustomerParameters;LastUpdatedDate;datetime
dfGuidedSalesParameters;CreatedDate;datetime
dfGuidedSalesParameters;LastUpdatedDate;datetime
dfGuidedSalesProductParameters;CreatedDate;datetime
dfGuidedSalesProductParameters;LastUpdatedDate;datetime
dfHopiCompany;CreatedDate;datetime
dfHopiCompany;LastUpdatedDate;datetime
dfHopiStore;CreatedDate;datetime
dfHopiStore;LastUpdatedDate;datetime
dfIGACompany;CreatedDate;datetime
dfIGACompany;LastUpdatedDate;datetime
dfIGAPosTerminal;CreatedDate;datetime
dfIGAPosTerminal;LastUpdatedDate;datetime
dfIncomeTaxRelief;CreatedDate;datetime
dfIncomeTaxRelief;LastUpdatedDate;datetime
dfInnerOrderProcessOfficialForm;CreatedDate;datetime
dfInnerOrderProcessOfficialForm;LastUpdatedDate;datetime
dfInnerProcessOfficialForm;CreatedDate;datetime
dfInnerProcessOfficialForm;LastUpdatedDate;datetime
dfInstallmentCountRulesBracket;CreatedDate;datetime
dfInstallmentCountRulesBracket;LastUpdatedDate;datetime
dfInsuaranceExpenseCodes;CreatedDate;datetime
dfInsuaranceExpenseCodes;LastUpdatedDate;datetime
dfItemDimensionNames;CreatedDate;datetime
dfItemDimensionNames;LastUpdatedDate;datetime
dfItemTestOfficialForm;CreatedDate;datetime
dfItemTestOfficialForm;LastUpdatedDate;datetime
dfIyzicoCompany;CreatedDate;datetime
dfIyzicoCompany;LastUpdatedDate;datetime
dfIyzicoCPCompany;CreatedDate;datetime
dfIyzicoCPCompany;LastUpdatedDate;datetime
dfJournalDefATAttribute;CreatedDate;datetime
dfJournalDefATAttribute;LastUpdatedDate;datetime
dfJournalOfficialForm;CreatedDate;datetime
dfJournalOfficialForm;LastUpdatedDate;datetime
dfJoyRefundCompany;CreatedDate;datetime
dfJoyRefundCompany;LastUpdatedDate;datetime
dfJoyRefundStore;CreatedDate;datetime
dfJoyRefundStore;LastUpdatedDate;datetime
dfMacellanSuperappCompany;CreatedDate;datetime
dfMacellanSuperappCompany;LastUpdatedDate;datetime
dfMarketPlaceParameters;CreatedDate;datetime
dfMarketPlaceParameters;LastUpdatedDate;datetime
dfMedulaLogonInfo;CreatedDate;datetime
dfMedulaLogonInfo;LastUpdatedDate;datetime
dfMedulaStoreLogonInfo;CreatedDate;datetime
dfMedulaStoreLogonInfo;LastUpdatedDate;datetime
dfMobilDevCompanyBrandCollectorID;CreatedDate;datetime
dfMobilDevCompanyBrandCollectorID;LastUpdatedDate;datetime
dfMobilDevCompanyThirdPartyCollectorID;CreatedDate;datetime
dfMobilDevCompanyThirdPartyCollectorID;LastUpdatedDate;datetime
dfMobildevStoreCollectorID;CreatedDate;datetime
dfMobildevStoreCollectorID;LastUpdatedDate;datetime
dfMobilDevStoreCompanyBrandCollectorID;CreatedDate;datetime
dfMobilDevStoreCompanyBrandCollectorID;LastUpdatedDate;datetime
dfMobilRevenueUser;CreatedDate;datetime
dfMobilRevenueUser;LastUpdatedDate;datetime
dfMobilRevenueUserSalesPoint;CreatedDate;datetime
dfMobilRevenueUserSalesPoint;LastUpdatedDate;datetime
dfMonthlyTurnoverTarget;CreatedDate;datetime
dfMonthlyTurnoverTarget;LastUpdatedDate;datetime
dfOfficeCreditCardType;CreatedDate;datetime
dfOfficeCreditCardType;LastUpdatedDate;datetime
dfOfficeDefault;CreatedDate;datetime
dfOfficeDefault;LastUpdatedDate;datetime
dfOfficeEArchiveStartDate;CreatedDate;datetime
dfOfficeEArchiveStartDate;EArchiveStartDate;date
dfOfficeEArchiveStartDate;LastUpdatedDate;datetime
dfOfficeNotAvailableProcess;CreatedDate;datetime
dfOfficeNotAvailableProcess;LastUpdatedDate;datetime
dfOfflinePosServiceParameters;CreatedDate;datetime
dfOfflinePosServiceParameters;LastUpdatedDate;datetime
dfOfflinePosTerminalParameters;CreatedDate;datetime
dfOfflinePosTerminalParameters;LastUpdatedDate;datetime
dfOnlineBankWebServiceParameters;CreatedDate;datetime
dfOnlineBankWebServiceParameters;LastUpdatedDate;datetime
dfOnlineDistributor;CreatedDate;datetime
dfOnlineDistributor;LastUpdatedDate;datetime
dfOnlineInstallmentPaymentBankAccs;CreatedDate;datetime
dfOnlineInstallmentPaymentBankAccs;LastUpdatedDate;datetime
dfOnlineInstallmentPaymentParameters;CreatedDate;datetime
dfOnlineInstallmentPaymentParameters;LastUpdatedDate;datetime
dfOnlineSalesandPaymentBankAccs;CreatedDate;datetime
dfOnlineSalesandPaymentBankAccs;LastUpdatedDate;datetime
dfOnlineSalesandPaymentParameters;CreatedDate;datetime
dfOnlineSalesandPaymentParameters;LastUpdatedDate;datetime
dfOnlineSalesAndPaymentParametersForConnection;CreatedDate;datetime
dfOnlineSalesAndPaymentParametersForConnection;LastUpdatedDate;datetime
dfOtherPaymentOfficialForm;CreatedDate;datetime
dfOtherPaymentOfficialForm;LastUpdatedDate;datetime
dfOtpServiceCompany;CreatedDate;datetime
dfOtpServiceCompany;LastUpdatedDate;datetime
dfPARO;CreatedDate;datetime
dfPARO;LastUpdatedDate;datetime
dfPAROCompany;CreatedDate;datetime
dfPAROCompany;LastUpdatedDate;datetime
dfPAROPOSTerminal;CreatedDate;datetime
dfPAROPOSTerminal;LastUpdatedDate;datetime
dfPAROProductProperty;CreatedDate;datetime
dfPAROProductProperty;LastUpdatedDate;datetime
dfPAROStore;CreatedDate;datetime
dfPAROStore;LastUpdatedDate;datetime
dfPaxEftPosCompany;CreatedDate;datetime
dfPaxEftPosCompany;LastUpdatedDate;datetime
dfPaymentOfficialForm;CreatedDate;datetime
dfPaymentOfficialForm;LastUpdatedDate;datetime
dfPaynetBankIDConvert;CreatedDate;datetime
dfPaynetBankIDConvert;LastUpdatedDate;datetime
dfPaynetCompany;CreatedDate;datetime
dfPaynetCompany;LastUpdatedDate;datetime
dfPaynetPaymentPlan;CreatedDate;datetime
dfPaynetPaymentPlan;LastUpdatedDate;datetime
dfPaynetStore;CreatedDate;datetime
dfPaynetStore;LastUpdatedDate;datetime
dfPayrollDefault;CreatedDate;datetime
dfPayrollDefault;LastUpdatedDate;datetime
dfPayrollForm;CreatedDate;datetime
dfPayrollForm;LastUpdatedDate;datetime
dfPDCCurrAcc;CreatedDate;datetime
dfPDCCurrAcc;LastUpdatedDate;datetime
dfPDCCurrAccAttribute;CreatedDate;datetime
dfPDCCurrAccAttribute;LastUpdatedDate;datetime
dfPDCCurrAccCommunication;CreatedDate;datetime
dfPDCCurrAccCommunication;LastUpdatedDate;datetime
dfPDCCurrAccContact;CreatedDate;datetime
dfPDCCurrAccContact;LastUpdatedDate;datetime
dfPDCCurrAccPersonalInfo;CreatedDate;datetime
dfPDCCurrAccPersonalInfo;LastUpdatedDate;datetime
dfPDCCurrAccPostalAddress;CreatedDate;datetime
dfPDCCurrAccPostalAddress;LastUpdatedDate;datetime
dfPDCCustomerCompanyBrandAttribute;CreatedDate;datetime
dfPDCCustomerCompanyBrandAttribute;LastUpdatedDate;datetime
dfPDCElements;CreatedDate;datetime
dfPDCElements;LastUpdatedDate;datetime
dfPDCQuery;CreatedDate;datetime
dfPDCQuery;LastUpdatedDate;datetime
dfPeriodicalAllocationRule;CreatedDate;datetime
dfPeriodicalAllocationRule;EndDate;smalldatetime
dfPeriodicalAllocationRule;LastUpdatedDate;datetime
dfPeriodicalAllocationRule;StartDate;smalldatetime
dfPeriodicalSMSRule;CreatedDate;datetime
dfPeriodicalSMSRule;LastUpdatedDate;datetime
dfPeriodicalTransferPlanRule;CreatedDate;datetime
dfPeriodicalTransferPlanRule;EndDate;smalldatetime
dfPeriodicalTransferPlanRule;LastUpdatedDate;datetime
dfPeriodicalTransferPlanRule;StartDate;smalldatetime
dfPlanetPaymentCompany;CreatedDate;datetime
dfPlanetPaymentCompany;LastUpdatedDate;datetime
dfPlanetPaymentStore;CreatedDate;datetime
dfPlanetPaymentStore;LastUpdatedDate;datetime
dfPosCustomerScreenLayout;CreatedDate;datetime
dfPosCustomerScreenLayout;LastUpdatedDate;datetime
dfPosCustomerScreenWidgetParameters;CreatedDate;datetime
dfPosCustomerScreenWidgetParameters;LastUpdatedDate;datetime
dfPosDeviceParameters;CreatedDate;datetime
dfPosDeviceParameters;LastUpdatedDate;datetime
dfPosNewCustomer;CreatedDate;datetime
dfPosNewCustomer;LastUpdatedDate;datetime
dfPosNewCustomerActions;CreatedDate;datetime
dfPosNewCustomerActions;LastUpdatedDate;datetime
dfPosNewCustomerField;CreatedDate;datetime
dfPosNewCustomerField;LastUpdatedDate;datetime
dfPosOrderOpticalProductField;CreatedDate;datetime
dfPosOrderOpticalProductField;LastUpdatedDate;datetime
dfPosUI;CreatedDate;datetime
dfPosUI;LastUpdatedDate;datetime
dfPosUIDesc;CreatedDate;datetime
dfPosUIDesc;LastUpdatedDate;datetime
dfPosUISetting;CreatedDate;datetime
dfPosUISetting;LastUpdatedDate;datetime
dfPriceListForm;CreatedDate;datetime
dfPriceListForm;LastUpdatedDate;datetime
dfProcessOfficialForm;CreatedDate;datetime
dfProcessOfficialForm;LastUpdatedDate;datetime
dfProductGroupLevelForOfficeBasedSerialNumberTracking;CreatedDate;datetime
dfProductGroupLevelForOfficeBasedSerialNumberTracking;LastUpdatedDate;datetime
dfProductHierarchy;CreatedDate;datetime
dfProductHierarchy;LastUpdatedDate;datetime
dfProductHierarchyAttribute;CreatedDate;datetime
dfProductHierarchyAttribute;LastUpdatedDate;datetime
dfProductHierarchyColorSet;CreatedDate;datetime
dfProductHierarchyColorSet;LastUpdatedDate;datetime
dfProductHierarchyDefault;CreatedDate;datetime
dfProductHierarchyDefault;LastUpdatedDate;datetime
dfProductHierarchyDimSet;CreatedDate;datetime
dfProductHierarchyDimSet;LastUpdatedDate;datetime
dfProductHierarchyLevelNames;CreatedDate;datetime
dfProductHierarchyLevelNames;LastUpdatedDate;datetime
dfProductsForOfficeBasedSerialNumberTracking;CreatedDate;datetime
dfProductsForOfficeBasedSerialNumberTracking;LastUpdatedDate;datetime
dfProductsForOfficeBasedSerialNumberTracking;SerialNumberTrackingStartDate;date
dfProductsForSerialNumberTrackingOnCustomer;CreatedDate;datetime
dfProductsForSerialNumberTrackingOnCustomer;LastUpdatedDate;datetime
dfProductsForSerialNumberTrackingOnCustomer;SerialNumberTrackingStartDate;date
dfPurchaseRequisitionOfficialForm;CreatedDate;datetime
dfPurchaseRequisitionOfficialForm;LastUpdatedDate;datetime
dfPurchaseRequisitionProposalOfficialForm;CreatedDate;datetime
dfPurchaseRequisitionProposalOfficialForm;LastUpdatedDate;datetime
dfReSendSMSForCustomerRelationship;CreatedDate;datetime
dfReSendSMSForCustomerRelationship;LastUpdatedDate;datetime
dfRetailCustomerConditionalRequiredFields;CreatedDate;datetime
dfRetailCustomerConditionalRequiredFields;LastUpdatedDate;datetime
dfRomaniaGoosfrabaeInvoiceOffice;CreatedDate;datetime
dfRomaniaGoosfrabaeInvoiceOffice;LastUpdatedDate;datetime
dfRomaniaGoosfrabaeInvoiceOffice;RomaniaEInvoiceStartDate;date
dfRomaniaGoosfrabaeShipmentOffice;CreatedDate;datetime
dfRomaniaGoosfrabaeShipmentOffice;LastUpdatedDate;datetime
dfRomaniaGoosfrabaeShipmentOffice;RomaniaEShipmentStartDate;date
dfSeaBoxCompany;CreatedDate;datetime
dfSeaBoxCompany;LastUpdatedDate;datetime
dfSlipPrinterTemplate;CreatedDate;datetime
dfSlipPrinterTemplate;LastUpdatedDate;datetime
dfSMSForCustomerRelationship;CreatedDate;datetime
dfSMSForCustomerRelationship;LastUpdatedDate;datetime
dfSocialInsuranceRates;CreatedDate;datetime
dfSocialInsuranceRates;LastUpdatedDate;datetime
dfStoreConsStore;CreatedDate;datetime
dfStoreConsStore;LastUpdatedDate;datetime
dfStoreCreditableConfirmation;CreatedDate;datetime
dfStoreCreditableConfirmation;LastUpdatedDate;datetime
dfStoreDefault;CreatedDate;datetime
dfStoreDefault;LastUpdatedDate;datetime
dfStoreDeliveryWarehouse;CreatedDate;datetime
dfStoreDeliveryWarehouse;LastUpdatedDate;datetime
dfStoreDigitalMarketingService;CreatedDate;datetime
dfStoreDigitalMarketingService;LastUpdatedDate;datetime
dfStoreDistributionWarehouse;CreatedDate;datetime
dfStoreDistributionWarehouse;LastUpdatedDate;datetime
dfStoreFolder;CreatedDate;datetime
dfStoreFolder;LastUpdatedDate;datetime
dfStoreForeignCurrency;CreatedDate;datetime
dfStoreForeignCurrency;LastUpdatedDate;datetime
dfStoreHierarchy;CreatedDate;datetime
dfStoreHierarchy;LastUpdatedDate;datetime
dfStoreHierarchyLevelNames;CreatedDate;datetime
dfStoreHierarchyLevelNames;LastUpdatedDate;datetime
dfStoreLabelTypes;CreatedDate;datetime
dfStoreLabelTypes;LastUpdatedDate;datetime
dfStoreProductInformation;CreatedDate;datetime
dfStoreProductInformation;LastUpdatedDate;datetime
dfStoreSupportWarehouse;CreatedDate;datetime
dfStoreSupportWarehouse;LastUpdatedDate;datetime
dfStoreTotalDiscountAuthority;CreatedDate;datetime
dfStoreTotalDiscountAuthority;LastUpdatedDate;datetime
dfStoreTransferStore;CreatedDate;datetime
dfStoreTransferStore;LastUpdatedDate;datetime
dfSupportRequestOfficialForm;CreatedDate;datetime
dfSupportRequestOfficialForm;LastUpdatedDate;datetime
dfSupportRequestProductLevels;CreatedDate;datetime
dfSupportRequestProductLevels;LastUpdatedDate;datetime
dfSupportRequestSurveyDefault;CreatedDate;datetime
dfSupportRequestSurveyDefault;LastUpdatedDate;datetime
dfTaxFreePointCompany;CreatedDate;datetime
dfTaxFreePointCompany;LastUpdatedDate;datetime
dfTaxFreeRefundRate;CreatedDate;datetime
dfTaxFreeRefundRate;LastUpdatedDate;datetime
dfTaxFreeZoneCompany;CreatedDate;datetime
dfTaxFreeZoneCompany;LastUpdatedDate;datetime
dfTaxFreeZoneStore;CreatedDate;datetime
dfTaxFreeZoneStore;LastUpdatedDate;datetime
dfTransactionDefFTAttribute;CreatedDate;datetime
dfTransactionDefFTAttribute;LastUpdatedDate;datetime
dfUmicoPOSTerminal;CreatedDate;datetime
dfUmicoPOSTerminal;LastUpdatedDate;datetime
dfUnifreeCompany;CreatedDate;datetime
dfUnifreeCompany;LastUpdatedDate;datetime
dfUnifreeStore;CreatedDate;datetime
dfUnifreeStore;LastUpdatedDate;datetime
dfUserAllowedOffice;CreatedDate;datetime
dfUserAllowedOffice;LastUpdatedDate;datetime
dfUserAllowedStore;CreatedDate;datetime
dfUserAllowedStore;LastUpdatedDate;datetime
dfUserAllowedWarehouse;CreatedDate;datetime
dfUserAllowedWarehouse;LastUpdatedDate;datetime
dfUserPosition;CreatedDate;datetime
dfUserPosition;LastUpdatedDate;datetime
dfUserPositionHistory;CreatedDate;datetime
dfUserPosUISettings;CreatedDate;datetime
dfUserPosUISettings;LastUpdatedDate;datetime
dfUTSWebServiceToken;CreatedDate;datetime
dfUTSWebServiceToken;LastUpdatedDate;datetime
dfVehicleLoadingOfficialForm;CreatedDate;datetime
dfVehicleLoadingOfficialForm;LastUpdatedDate;datetime
dfVehicleUnLoadingOfficialForm;CreatedDate;datetime
dfVehicleUnLoadingOfficialForm;LastUpdatedDate;datetime
dfVendorPriceListForm;CreatedDate;datetime
dfVendorPriceListForm;LastUpdatedDate;datetime
dfVirementOfficialForm;CreatedDate;datetime
dfVirementOfficialForm;LastUpdatedDate;datetime
dfWeArePlanetTaxFreeCompany;CreatedDate;datetime
dfWeArePlanetTaxFreeCompany;LastUpdatedDate;datetime
dfWeArePlanetTaxFreeStore;CreatedDate;datetime
dfWeArePlanetTaxFreeStore;LastUpdatedDate;datetime
e_InboxShipmentHeader;ActualDate;smalldatetime
e_InboxShipmentHeader;ActualTime;smalldatetime
e_InboxShipmentHeader;IssueDate;smalldatetime
e_InboxShipmentHeader;IssueTime;smalldatetime
e_InboxShipmentResponseHeader;DespatchDocumentReferenceIssueDate;smalldatetime
e_InboxShipmentResponseHeader;IssueDate;smalldatetime
e_InboxShipmentResponseHeader;IssueTime;smalldatetime
e_LastShipmentAskDate;LastShipmentAskDate;datetime
e_LastShipmentAskDate;LastShipmentResponseAskDate;datetime
e_OutboxShipmentHeader;ActualDate;smalldatetime
e_OutboxShipmentHeader;ActualTime;smalldatetime
e_OutboxShipmentHeader;IssueDate;smalldatetime
e_OutboxShipmentHeader;IssueTime;smalldatetime
e_OutboxShipmentOrder;IssueDate;datetime
e_OutboxShipmentResponseHeader;DespatchDocumentReferenceIssueDate;smalldatetime
e_OutboxShipmentResponseHeader;IssueDate;smalldatetime
e_OutboxShipmentResponseHeader;IssueTime;smalldatetime
e_SubjectToEInvoice;CreatedDate;datetime
e_SubjectToEInvoice;LastUpdatedDate;datetime
e_SubjectToEInvoice;StartDate;smalldatetime
e_SubjectToEShipment;CreatedDate;datetime
e_SubjectToEShipment;LastUpdatedDate;datetime
e_SubjectToEShipment;StartDate;smalldatetime
hrEmployeeAGI;CreatedDate;datetime
hrEmployeeAGI;LastUpdatedDate;datetime
hrEmployeeJobTitle;CreatedDate;datetime
hrEmployeeJobTitle;LastUpdatedDate;datetime
hrEmployeeJobTitle;StartDate;date
hrEmployeeMonthlySum;CreatedDate;datetime
hrEmployeeMonthlySum;JournalDate;date
hrEmployeeMonthlySum;LastUpdatedDate;datetime
hrEmployeeMonthlySumDetail;CreatedDate;datetime
hrEmployeeMonthlySumDetail;JournalDate;date
hrEmployeeMonthlySumDetail;LastUpdatedDate;datetime
hrEmployeeOrganizationChart;CreatedDate;datetime
hrEmployeeOrganizationChart;LastUpdatedDate;datetime
hrEmployeePayrollProfile;CreatedDate;datetime
hrEmployeePayrollProfile;EstimatedPensionDate;date
hrEmployeePayrollProfile;JobEndDate;date
hrEmployeePayrollProfile;LastUpdatedDate;datetime
hrEmployeePrivateInsurance;CreatedDate;datetime
hrEmployeePrivateInsurance;EndDate;date
hrEmployeePrivateInsurance;LastUpdatedDate;datetime
hrEmployeePrivateInsurance;StartDate;date
hrEmployeeSGKBorrowing;CreatedDate;datetime
hrEmployeeSGKBorrowing;LastUpdatedDate;datetime
hrEmployeeSGKBorrowing;OrganizedDate;date
hrEmployeeWage;CreatedDate;datetime
hrEmployeeWage;LastUpdatedDate;datetime
hrEmployeeWage;StartDate;date
hrEmployeeWorkPlace;CreatedDate;datetime
hrEmployeeWorkPlace;JobStartDate;date
hrEmployeeWorkPlace;LastUpdatedDate;datetime
hrJobInterview;CreatedDate;datetime
hrJobInterview;InterviewDate;date
hrJobInterview;LastUpdatedDate;datetime
hrJobInterviewResults;CreatedDate;datetime
hrJobInterviewResults;LastUpdatedDate;datetime
hrJobInterviewResults;OperationDate;date
hrJobPositionCandidate;CreatedDate;datetime
hrJobPositionCandidate;LastUpdatedDate;datetime
hrJobTitleOrganizationChart;CreatedDate;datetime
hrJobTitleOrganizationChart;LastUpdatedDate;datetime
hrSGKEmployeeJobEndDeclaration;CreatedDate;datetime
hrSGKEmployeeJobEndDeclaration;LastUpdatedDate;datetime
hrSGKEmployeeJobEndDeclaration;OperationDate;datetime
hrSGKEmployeeJobStartDeclaration;CreatedDate;datetime
hrSGKEmployeeJobStartDeclaration;LastUpdatedDate;datetime
hrSGKEmployeeJobStartDeclaration;OperationDate;datetime
hrSGKMonthlyDocument;CreatedDate;datetime
hrSGKMonthlyDocument;JobEndDate;date
hrSGKMonthlyDocument;JobStartDate;date
hrSGKMonthlyDocument;LastUpdatedDate;datetime
hrSGKMonthlyDocumentDeclaration;CreatedDate;datetime
hrSGKMonthlyDocumentDeclaration;LastUpdatedDate;datetime
hrTestResult;CreatedDate;datetime
hrTestResult;LastUpdatedDate;datetime
hrTestResult;TestDate;date
hrWageGarnishment;ClosedDate;date
hrWageGarnishment;CreatedDate;datetime
hrWageGarnishment;DocumentDate;date
hrWageGarnishment;LastUpdatedDate;datetime
lgSMSForCustomerRelationshipNonFormattedCommunications;CreatedDate;datetime
lgSMSForCustomerRelationshipNonFormattedCommunications;LastUpdatedDate;datetime
lgV3OfflinePOSSendStatusLog;CreatedDate;datetime
lgV3OfflinePOSSendStatusLog;LastUpdatedDate;datetime
prAllocationResultViewCustomization;CreatedDate;datetime
prAllocationResultViewCustomization;LastUpdatedDate;datetime
prAllocationRuleScript;CreatedDate;datetime
prAllocationRuleScript;LastUpdatedDate;datetime
prAllocationTemplateParameterValue;CreatedDate;datetime
prAllocationTemplateParameterValue;LastUpdatedDate;datetime
prAmountRuleBracket;CreatedDate;datetime
prAmountRuleBracket;LastUpdatedDate;datetime
prAvailableDeclarationPostTypes;CreatedDate;datetime
prAvailableDeclarationPostTypes;LastUpdatedDate;datetime
prBankAdditionalChargeTypeGLAccs;CreatedDate;datetime
prBankAdditionalChargeTypeGLAccs;LastUpdatedDate;datetime
prBankBranch;CreatedDate;datetime
prBankBranch;LastUpdatedDate;datetime
prBankPOSAccounts;CreatedDate;datetime
prBankPOSAccounts;LastUpdatedDate;datetime
prBankPOSGLAccs;CreatedDate;datetime
prBankPOSGLAccs;LastUpdatedDate;datetime
prBankPosID;CreatedDate;datetime
prBankPosID;LastUpdatedDate;datetime
prBankPOSProviderConvert;CreatedDate;datetime
prBankPOSProviderConvert;LastUpdatedDate;datetime
prBOMContent;CreatedDate;datetime
prBOMContent;LastUpdatedDate;datetime
prBOMTemplateAttribute;CreatedDate;datetime
prBOMTemplateAttribute;LastUpdatedDate;datetime
prBOMTemplateContent;CreatedDate;datetime
prBOMTemplateContent;LastUpdatedDate;datetime
prBOMTemplateItemConditionalFilter;CreatedDate;datetime
prBOMTemplateItemConditionalFilter;LastUpdatedDate;datetime
prBOMTemplateItemFilter;CreatedDate;datetime
prBOMTemplateItemFilter;LastUpdatedDate;datetime
prCareWarningTemplateAtt;CreatedDate;datetime
prCareWarningTemplateAtt;LastUpdatedDate;datetime
prChannelTemplateCurrAcc;CreatedDate;datetime
prChannelTemplateCurrAcc;LastUpdatedDate;datetime
prChequeAttribute;CreatedDate;datetime
prChequeAttribute;LastUpdatedDate;datetime
prChequeGLAccs;CreatedDate;datetime
prChequeGLAccs;LastUpdatedDate;datetime
prCityMapLocation;CreatedDate;datetime
prCityMapLocation;LastUpdatedDate;datetime
prColorThemeAttribute;CreatedDate;datetime
prColorThemeAttribute;LastUpdatedDate;datetime
prCompanyCostCenter;CreatedDate;datetime
prCompanyCostCenter;LastUpdatedDate;datetime
prCompanyCreditCardEarnedPoints;CreatedDate;datetime
prCompanyCreditCardEarnedPoints;DocumentDate;date
prCompanyCreditCardEarnedPoints;LastUpdatedDate;datetime
prCompanyCreditCardEmployee;CreatedDate;datetime
prCompanyCreditCardEmployee;EndRegisteredDate;date
prCompanyCreditCardEmployee;LastUpdatedDate;datetime
prCompanyCreditCardEmployee;RegisteredDate;date
prCompanyCreditCardExpense;CreatedDate;datetime
prCompanyCreditCardExpense;DocumentDate;date
prCompanyCreditCardExpense;LastUpdatedDate;datetime
prCompanyCreditCardUsageFee;CreatedDate;datetime
prCompanyCreditCardUsageFee;LastUpdatedDate;datetime
prCompanyCreditCardUsageFee;StartDate;date
prCompanyExpense;CreatedDate;datetime
prCompanyExpense;LastUpdatedDate;datetime
prCompanyExpenseInvoiceConfirmationRule;CreatedDate;datetime
prCompanyExpenseInvoiceConfirmationRule;LastUpdatedDate;datetime
prCompanyHierarchy;CreatedDate;datetime
prCompanyHierarchy;LastUpdatedDate;datetime
prConfirmationFormCommTypes;CreatedDate;datetime
prConfirmationFormCommTypes;LastUpdatedDate;datetime
prConfirmationFormContent;CreatedDate;datetime
prConfirmationFormContent;LastUpdatedDate;datetime
prConfirmationFormContent;OperationDate;datetime
prConfirmationRequiredProductGroups;CreatedDate;datetime
prConfirmationRequiredProductGroups;LastUpdatedDate;datetime
prConfirmationRuleStep;CreatedDate;datetime
prConfirmationRuleStep;LastUpdatedDate;datetime
prConfirmationRuleStepUser;CreatedDate;datetime
prConfirmationRuleStepUser;LastUpdatedDate;datetime
prConsentSourceConvert;CreatedDate;datetime
prConsentSourceConvert;LastUpdatedDate;datetime
prConvertedJobCandidate;ConvertedDate;date
prConvertedJobCandidate;CreatedDate;datetime
prConvertedJobCandidate;LastUpdatedDate;datetime
prConvertedPotentialCustomer;ConvertedDate;date
prConvertedPotentialCustomer;CreatedDate;datetime
prConvertedPotentialCustomer;LastUpdatedDate;datetime
prCostCenterAttribute;CreatedDate;datetime
prCostCenterAttribute;LastUpdatedDate;datetime
prCostCenterCostDriver;CreatedDate;datetime
prCostCenterCostDriver;LastUpdatedDate;datetime
prCostCenterHierarchy;CreatedDate;datetime
prCostCenterHierarchy;LastUpdatedDate;datetime
prCountryCallingCode;CreatedDate;datetime
prCountryCallingCode;LastUpdatedDate;datetime
prCountryPCTApplicablePaymentTypes;CreatedDate;datetime
prCountryPCTApplicablePaymentTypes;LastUpdatedDate;datetime
prCreditCardTypeBIN;CreatedDate;datetime
prCreditCardTypeBIN;LastUpdatedDate;datetime
prCreditCardTypeGLAccs;CreatedDate;datetime
prCreditCardTypeGLAccs;LastUpdatedDate;datetime
prCreditCardValidity;CreatedDate;datetime
prCreditCardValidity;LastUpdatedDate;datetime
prCreditSurveyorResponsibilityArea;CreatedDate;datetime
prCreditSurveyorResponsibilityArea;LastUpdatedDate;datetime
prCurrAccAttribute;CreatedDate;datetime
prCurrAccAttribute;LastUpdatedDate;datetime
prCurrAccAvailableForeignCurrencyTrans;CreatedDate;datetime
prCurrAccAvailableForeignCurrencyTrans;LastUpdatedDate;datetime
prCurrAccBadDebtStatus;CreatedDate;datetime
prCurrAccBadDebtStatus;LastUpdatedDate;datetime
prCurrAccBankAccNo;CreatedDate;datetime
prCurrAccBankAccNo;LastUpdatedDate;datetime
prCurrAccCommunication;CreatedDate;datetime
prCurrAccCommunication;LastUpdatedDate;datetime
prCurrAccCommunicationEnc;CreatedDate;datetime
prCurrAccCommunicationEnc;LastUpdatedDate;datetime
prCurrAccCommunicationFormatted;CreatedDate;datetime
prCurrAccCommunicationFormatted;LastUpdatedDate;datetime
prCurrAccCompanyBrand;CreatedDate;datetime
prCurrAccCompanyBrand;LastUpdatedDate;datetime
prCurrAccContact;CreatedDate;datetime
prCurrAccContact;LastUpdatedDate;datetime
prCurrAccDefault;CreatedDate;datetime
prCurrAccDefault;LastUpdatedDate;datetime
prCurrAccEInvoiceAlias;CreatedDate;datetime
prCurrAccEInvoiceAlias;LastUpdatedDate;datetime
prCurrAccEInvoiceOfficialForm;CreatedDate;datetime
prCurrAccEInvoiceOfficialForm;LastUpdatedDate;datetime
prCurrAccEnc;CreatedDate;datetime
prCurrAccEnc;LastUpdatedDate;datetime
prCurrAccEShipmentAlias;CreatedDate;datetime
prCurrAccEShipmentAlias;LastUpdatedDate;datetime
prCurrAccEShipmentOfficialForm;CreatedDate;datetime
prCurrAccEShipmentOfficialForm;LastUpdatedDate;datetime
prCurrAccExtendedProperties;CreatedDate;datetime
prCurrAccExtendedProperties;LastUpdatedDate;datetime
prCurrAccGLAccount;CreatedDate;datetime
prCurrAccGLAccount;LastUpdatedDate;datetime
prCurrAccInformation;CreatedDate;datetime
prCurrAccInformation;LastUpdatedDate;datetime
prCurrAccListContent;CreatedDate;datetime
prCurrAccListContent;LastUpdatedDate;datetime
prCurrAccLotGrAtt;CreatedDate;datetime
prCurrAccLotGrAtt;LastUpdatedDate;datetime
prCurrAccMapLocation;CreatedDate;datetime
prCurrAccMapLocation;LastUpdatedDate;datetime
prCurrAccNotes;CreatedDate;datetime
prCurrAccNotes;LastUpdatedDate;datetime
prCurrAccOnlineBank;CreatedDate;datetime
prCurrAccOnlineBank;LastUpdatedDate;datetime
prCurrAccOptInOptOutStatus;CreatedDate;datetime
prCurrAccOptInOptOutStatus;LastUpdatedDate;datetime
prCurrAccPersonalDataConfirmation;ConfirmationDate;datetime
prCurrAccPersonalDataConfirmation;CreatedDate;datetime
prCurrAccPersonalDataConfirmation;InactivationDate;date
prCurrAccPersonalDataConfirmation;LastUpdatedDate;datetime
prCurrAccPersonalDataConfirmationStatus;CreatedDate;datetime
prCurrAccPersonalDataConfirmationStatus;LastUpdatedDate;datetime
prCurrAccPersonalInfo;BirthDate;date
prCurrAccPersonalInfo;CreatedDate;datetime
prCurrAccPersonalInfo;HighSchoolFinishedDate;date
prCurrAccPersonalInfo;LastUpdatedDate;datetime
prCurrAccPersonalInfo;MarriedDate;date
prCurrAccPersonalInfo;MilitaryServiceFinishedDate;date
prCurrAccPersonalInfo;PassportIssueDate;date
prCurrAccPersonalInfo;PrimarySchoolFinishedDate;date
prCurrAccPhoto;CreatedDate;datetime
prCurrAccPhoto;LastUpdatedDate;datetime
prCurrAccPostalAddress;CreatedDate;datetime
prCurrAccPostalAddress;LastUpdatedDate;datetime
prCurrAccReconciliationContact;CreatedDate;datetime
prCurrAccReconciliationContact;LastUpdatedDate;datetime
prCurrAccReconciliationContactReports;CreatedDate;datetime
prCurrAccReconciliationContactReports;LastUpdatedDate;datetime
prCurrAccUBLExtensions;CreatedDate;datetime
prCurrAccUBLExtensions;LastUpdatedDate;datetime
prCurrAccUserWarning;CreatedDate;datetime
prCurrAccUserWarning;LastUpdatedDate;datetime
prCurrAccUserWarning;OperationDate;date
prCurrAccUTSInformation;CreatedDate;datetime
prCurrAccUTSInformation;LastUpdatedDate;datetime
prCustomerCompanyBrandAttribute;CreatedDate;datetime
prCustomerCompanyBrandAttribute;LastUpdatedDate;datetime
prCustomerCompanyBrandAttribute;StartDate;date
prCustomerConversation;CreatedDate;datetime
prCustomerConversation;LastUpdatedDate;datetime
prCustomerConversation;NextConversationDate;date
prCustomerConversation;PromisedPaymentDate;date
prCustomerConversationSubjectRelatedResult;CreatedDate;datetime
prCustomerConversationSubjectRelatedResult;LastUpdatedDate;datetime
prCustomerCreditLimit;CreatedDate;datetime
prCustomerCreditLimit;LastUpdatedDate;datetime
prCustomerCreditLimit;StartDate;date
prCustomerDBSAccount;CreatedDate;datetime
prCustomerDBSAccount;LastUpdatedDate;datetime
prCustomerDiscountGrAtt;CreatedDate;datetime
prCustomerDiscountGrAtt;LastUpdatedDate;datetime
prCustomerLoyaltyProgram;CreatedDate;datetime
prCustomerLoyaltyProgram;LastUpdatedDate;datetime
prCustomerLoyaltyProgram;LoyaltyProgramLevelCodeUpdateDate;datetime
prCustomerLoyaltyProgram;LoyaltyProgramStatusCodeUpdateDate;datetime
prCustomerLoyaltyProgramHistory;CreatedDate;datetime
prCustomerLoyaltyProgramHistory;LastUpdatedDate;datetime
prCustomerLoyaltyProgramHistory;LoyaltyProgramLevelCodeUpdateDate;datetime
prCustomerLoyaltyProgramHistory;LoyaltyProgramStatusCodeUpdateDate;datetime
prCustomerLoyaltyProgramHistory;OperationDate;datetime
prCustomerMarkupGrAtt;CreatedDate;datetime
prCustomerMarkupGrAtt;LastUpdatedDate;datetime
prCustomerOnlinePayment;CreatedDate;datetime
prCustomerOnlinePayment;LastUpdatedDate;datetime
prCustomerOnlinePaymentContact;CreatedDate;datetime
prCustomerOnlinePaymentContact;LastUpdatedDate;datetime
prCustomerPaymentPlanGrAtt;CreatedDate;datetime
prCustomerPaymentPlanGrAtt;LastUpdatedDate;datetime
prCustomerPresentCard;ActivationDate;date
prCustomerPresentCard;CreatedDate;datetime
prCustomerPresentCard;IssueDate;date
prCustomerPresentCard;LastUpdatedDate;datetime
prCustomerPresentCard;LastValidDate;date
prCustomerSalesperson;CreatedDate;datetime
prCustomerSalesperson;LastUpdatedDate;datetime
prCustomerSalesperson;StartDate;date
prCustomerStore;CreatedDate;datetime
prCustomerStore;LastUpdatedDate;datetime
prCustomerVendorAccount;CreatedDate;datetime
prCustomerVendorAccount;LastUpdatedDate;datetime
prCustomerVerificationPassword;CreatedDate;datetime
prCustomerVerificationPassword;LastUpdatedDate;datetime
prCustomerVerificationPassword;PasswordLastUpdatedDate;date
prCustomProcessGroupAtt;CreatedDate;datetime
prCustomProcessGroupAtt;LastUpdatedDate;datetime
prDataTransferCompanyParameter;CreatedDate;datetime
prDataTransferCompanyParameter;LastUpdatedDate;datetime
prDataTransferJobClients;CreatedDate;datetime
prDataTransferJobClients;LastUpdatedDate;datetime
prDataTransferJobSchedule;CreatedDate;datetime
prDataTransferJobSchedule;LastUpdatedDate;datetime
prDataTransferTemplateQuery;CreatedDate;datetime
prDataTransferTemplateQuery;LastUpdatedDate;datetime
prDataTransferTemplateQueryFilter;CreatedDate;datetime
prDataTransferTemplateQueryFilter;LastUpdatedDate;datetime
prDBSLimitAkbank;CreatedDate;datetime
prDBSLimitAkbank;LastUpdatedDate;datetime
prDBSLimitAkbank;LimitDate;date
prDBSLimitAkbankHistory;CreatedDate;datetime
prDBSLimitAkbankHistory;LastUpdatedDate;datetime
prDBSLimitAkbankHistory;LimitDate;date
prDBSLimitIsBankasi;CreatedDate;datetime
prDBSLimitIsBankasi;LastUpdatedDate;datetime
prDBSLimitIsBankasi;LimitDate;date
prDBSLimitIsBankasiHistory;CreatedDate;datetime
prDBSLimitIsBankasiHistory;LastUpdatedDate;datetime
prDBSLimitIsBankasiHistory;LimitDate;date
prDBSLimitTEB;CreatedDate;datetime
prDBSLimitTEB;LastUpdatedDate;datetime
prDBSLimitTEB;LimitDate;date
prDBSLimitTEBHistory;CreatedDate;datetime
prDBSLimitTEBHistory;LastUpdatedDate;datetime
prDBSLimitTEBHistory;LimitDate;date
prDBSLimitYKB;CreatedDate;datetime
prDBSLimitYKB;LastUpdatedDate;datetime
prDBSLimitYKB;LimitDate;date
prDBSLimitYKBHistory;CreatedDate;datetime
prDBSLimitYKBHistory;LastUpdatedDate;datetime
prDBSLimitYKBHistory;LimitDate;date
prDeclarationGLAccs;CreatedDate;datetime
prDeclarationGLAccs;LastUpdatedDate;datetime
prDeclarationTypeDetail;CreatedDate;datetime
prDeclarationTypeDetail;LastUpdatedDate;datetime
prDeclarationXML;CreatedDate;datetime
prDeclarationXML;LastUpdatedDate;datetime
prDeliveryCompanyMarketPlaceMapping;CreatedDate;datetime
prDeliveryCompanyMarketPlaceMapping;LastUpdatedDate;datetime
prDigitalChannelStockConfiguration;CreatedDate;datetime
prDigitalChannelStockConfiguration;LastUpdatedDate;datetime
prDiscountOfferActiveLog;CreatedDate;datetime
prDiscountOfferActiveLog;LastUpdatedDate;datetime
prDiscountOfferActiveLog;OperationDate;datetime
prDiscountOfferAttribute;CreatedDate;datetime
prDiscountOfferAttribute;LastUpdatedDate;datetime
prDiscountOfferCreditCard;CreatedDate;datetime
prDiscountOfferCreditCard;LastUpdatedDate;datetime
prDiscountOfferCustomer;CreatedDate;datetime
prDiscountOfferCustomer;LastUpdatedDate;datetime
prDiscountOfferDescription;CreatedDate;datetime
prDiscountOfferDescription;LastUpdatedDate;datetime
prDiscountOfferLocation;CreatedDate;datetime
prDiscountOfferLocation;LastUpdatedDate;datetime
prDiscountOfferMethodParameter;CreatedDate;datetime
prDiscountOfferMethodParameter;LastUpdatedDate;datetime
prDiscountOfferMethodScript;CreatedDate;datetime
prDiscountOfferMethodScript;LastUpdatedDate;datetime
prDiscountOfferNotes;CreatedDate;datetime
prDiscountOfferNotes;LastUpdatedDate;datetime
prDiscountOfferParameterValue;CreatedDate;datetime
prDiscountOfferParameterValue;LastUpdatedDate;datetime
prDiscountOfferPassword;CreatedDate;datetime
prDiscountOfferPassword;LastUpdatedDate;datetime
prDiscountOfferPaymentPlan;CreatedDate;datetime
prDiscountOfferPaymentPlan;LastUpdatedDate;datetime
prDiscountOfferPaymentProvider;CreatedDate;datetime
prDiscountOfferPaymentProvider;LastUpdatedDate;datetime
prDiscountOfferProduct;CreatedDate;datetime
prDiscountOfferProduct;LastUpdatedDate;datetime
prDiscountOfferRules;CreatedDate;datetime
prDiscountOfferRules;LastUpdatedDate;datetime
prDiscountOfferRules;LastValidDate;date
prDiscountOfferTurnoverTarget;CreatedDate;datetime
prDiscountOfferTurnoverTarget;LastUpdatedDate;datetime
prDiscountOfferTurnoverTarget;StartDate;date
prDiscountPoint;CreatedDate;datetime
prDiscountPoint;FirstValidDate;date
prDiscountPoint;LastUpdatedDate;datetime
prDiscountPoint;LastValidDate;date
prDiscountPointTypeNotes;CreatedDate;datetime
prDiscountPointTypeNotes;LastUpdatedDate;datetime
prDiscountReasonSubReason;CreatedDate;datetime
prDiscountReasonSubReason;LastUpdatedDate;datetime
prDiscountTypeGLAccs;CreatedDate;datetime
prDiscountTypeGLAccs;LastUpdatedDate;datetime
prDiscountVoucherTypeNotes;CreatedDate;datetime
prDiscountVoucherTypeNotes;LastUpdatedDate;datetime
prDistrictMapLocation;CreatedDate;datetime
prDistrictMapLocation;LastUpdatedDate;datetime
prDOVGLAccs;CreatedDate;datetime
prDOVGLAccs;LastUpdatedDate;datetime
prEArchiveWebServiceCompany;CreatedDate;datetime
prEArchiveWebServiceCompany;LastUpdatedDate;datetime
prEArchiveWebServiceOffice;CreatedDate;datetime
prEArchiveWebServiceOffice;LastUpdatedDate;datetime
prEasyStartupComments;CreatedDate;datetime
prEasyStartupComments;LastUpdatedDate;datetime
prEasyStartupNotes;CreatedDate;datetime
prEasyStartupNotes;LastUpdatedDate;datetime
prEInvoiceWebServiceCompany;CreatedDate;datetime
prEInvoiceWebServiceCompany;LastUpdatedDate;datetime
prEInvoiceWebServiceOffice;CreatedDate;datetime
prEInvoiceWebServiceOffice;LastUpdatedDate;datetime
prEmployee7252Incentive;CreatedDate;datetime
prEmployee7252Incentive;LastUpdatedDate;datetime
prEmployeeDocument;CreatedDate;datetime
prEmployeeDocument;DeliveryDate;date
prEmployeeDocument;LastUpdatedDate;datetime
prEmployeeEducation;CreatedDate;datetime
prEmployeeEducation;EndingDate;date
prEmployeeEducation;LastUpdatedDate;datetime
prEmployeeEducation;StartingDate;date
prEmployeeForeignLanguage;CreatedDate;datetime
prEmployeeForeignLanguage;LastUpdatedDate;datetime
prEmployeeJobTraining;CreatedDate;datetime
prEmployeeJobTraining;EndDate;date
prEmployeeJobTraining;LastUpdatedDate;datetime
prEmployeeJobTraining;StartDate;date
prEmployeeLeaveDay;CreatedDate;datetime
prEmployeeLeaveDay;LastUpdatedDate;datetime
prEmployeeLeaveDay;LeaveDate;date
prEmployeeLeaveHour;CreatedDate;datetime
prEmployeeLeaveHour;LastUpdatedDate;datetime
prEmployeeLeaveHour;LeaveDate;date
prEmployeeLeaveRequest;CreatedDate;datetime
prEmployeeLeaveRequest;LastUpdatedDate;datetime
prEmployeeLeaveRequest;LeaveDate;date
prEmployeeLeaveRequest;StatusUpdateDate;datetime
prEmployeeLeaveRequestHour;CreatedDate;datetime
prEmployeeLeaveRequestHour;LastUpdatedDate;datetime
prEmployeeLeaveRequestHour;LeaveDate;date
prEmployeeLeaveRequestHour;StatusUpdateDate;datetime
prEmployeeLeaveTransfered;CreatedDate;datetime
prEmployeeLeaveTransfered;LastUpdatedDate;datetime
prEmployeeLeaveTransfered;TurnOverDate;date
prEmployeePrevJob;CreatedDate;datetime
prEmployeePrevJob;JobEndDate;date
prEmployeePrevJob;JobStartDate;date
prEmployeePrevJob;LastUpdatedDate;datetime
prEmployeeRecord;CreatedDate;datetime
prEmployeeRecord;LastUpdatedDate;datetime
prEmployeeRecord;RecordDate;date
prEmployeeRemoteWorkDays;CreatedDate;datetime
prEmployeeRemoteWorkDays;LastUpdatedDate;datetime
prEmployeeSeniorityTransfered;CreatedDate;datetime
prEmployeeSeniorityTransfered;JobStartDateForSeniorityTransfer;date
prEmployeeSeniorityTransfered;LastUpdatedDate;datetime
prEmployeeShoppingLimit;CreatedDate;datetime
prEmployeeShoppingLimit;LastUpdatedDate;datetime
prEmployeeShoppingLimit;StartDate;date
prEmployeeSoftware;CreatedDate;datetime
prEmployeeSoftware;LastUpdatedDate;datetime
prEmployeeWorkplaceInformation;CreatedDate;datetime
prEmployeeWorkplaceInformation;LastUpdatedDate;datetime
prEmployeeWorkplaceInformation;OperationDate;datetime
prExpenseInvoiceConfirmationRule;CreatedDate;datetime
prExpenseInvoiceConfirmationRule;LastUpdatedDate;datetime
prExportFileAttribute;CreatedDate;datetime
prExportFileAttribute;LastUpdatedDate;datetime
prExportFileIndirectExpense;CreatedDate;datetime
prExportFileIndirectExpense;DocumentDate;date
prExportFileIndirectExpense;LastUpdatedDate;datetime
prExportFileInsurance;CreatedDate;datetime
prExportFileInsurance;EndDate;date
prExportFileInsurance;LastUpdatedDate;datetime
prExportFileInsurance;StartDate;date
prExportFileShippingInfo;ActualDateOfArrival;date
prExportFileShippingInfo;CreatedDate;datetime
prExportFileShippingInfo;DateOfLading;date
prExportFileShippingInfo;DateOfNationalization;date
prExportFileShippingInfo;EstimatedDateOfArrival;date
prExportFileShippingInfo;LastUpdatedDate;datetime
prExportFileStatusHistory;CreatedDate;datetime
prExportFileStatusHistory;LastUpdatedDate;datetime
prExportFileStatusHistory;OperationDate;date
prFixedAssetDepreciationInfo;CreatedDate;datetime
prFixedAssetDepreciationInfo;DepreciationBeginningDate;date
prFixedAssetDepreciationInfo;EndDate;date
prFixedAssetDepreciationInfo;LastReassessmentDate;date
prFixedAssetDepreciationInfo;LastUpdatedDate;datetime
prFixedAssetDepreciationInfo;TurnOverDate;date
prFixedAssetEmployee;CreatedDate;datetime
prFixedAssetEmployee;FAEndRegisteredDate;date
prFixedAssetEmployee;FARegisteredDate;date
prFixedAssetEmployee;LastUpdatedDate;datetime
prFixedAssetExpense;CreatedDate;datetime
prFixedAssetExpense;EndDate;date
prFixedAssetExpense;JournalDate;date
prFixedAssetExpense;LastUpdatedDate;datetime
prFixedAssetExpense;OperationDate;date
prFixedAssetExpense;StartDate;date
prFixedAssetExpenseReassessment;CreatedDate;datetime
prFixedAssetExpenseReassessment;LastUpdatedDate;datetime
prFixedAssetInflationAdjustment;CreatedDate;datetime
prFixedAssetInflationAdjustment;LastUpdatedDate;datetime
prFixedAssetInsurance;CreatedDate;datetime
prFixedAssetInsurance;EndDate;date
prFixedAssetInsurance;LastUpdatedDate;datetime
prFixedAssetInsurance;StartDate;date
prFixedAssetPurchases;CreatedDate;datetime
prFixedAssetPurchases;LastUpdatedDate;datetime
prFixedAssetPurchases;OperationDate;date
prFixedAssetReassessmentRates;CreatedDate;datetime
prFixedAssetReassessmentRates;LastUpdatedDate;datetime
prFixedAssetSales;CreatedDate;datetime
prFixedAssetSales;LastUpdatedDate;datetime
prFixedAssetSales;OperationDate;date
prFixedAssetStatusHistory;CreatedDate;datetime
prFixedAssetStatusHistory;LastUpdatedDate;datetime
prFixedAssetStatusHistory;OperationDate;datetime
prGiftCardCharge;CreatedDate;datetime
prGiftCardCharge;LastUpdatedDate;datetime
prGiftCardCharge;OperationDate;date
prGLAccAttribute;CreatedDate;datetime
prGLAccAttribute;LastUpdatedDate;datetime
prGLAccAvailableForeignCurrencyTrans;CreatedDate;datetime
prGLAccAvailableForeignCurrencyTrans;LastUpdatedDate;datetime
prGLAccAvailableJournalTypeSub;CreatedDate;datetime
prGLAccAvailableJournalTypeSub;LastUpdatedDate;datetime
prGLAccNotes;CreatedDate;datetime
prGLAccNotes;LastUpdatedDate;datetime
prGLAccOnlineBank;CreatedDate;datetime
prGLAccOnlineBank;LastUpdatedDate;datetime
prGLAccUserWarning;CreatedDate;datetime
prGLAccUserWarning;LastUpdatedDate;datetime
prGLAccUserWarning;OperationDate;date
prGLReflectionAccount;CreatedDate;datetime
prGLReflectionAccount;LastUpdatedDate;datetime
prImportFileAttribute;CreatedDate;datetime
prImportFileAttribute;LastUpdatedDate;datetime
prImportFileExpense;CreatedDate;datetime
prImportFileExpense;LastUpdatedDate;datetime
prImportFileGLAccs;CreatedDate;datetime
prImportFileGLAccs;LastUpdatedDate;datetime
prImportFileIndirectExpense;CreatedDate;datetime
prImportFileIndirectExpense;DocumentDate;date
prImportFileIndirectExpense;LastUpdatedDate;datetime
prImportFileInsurance;CreatedDate;datetime
prImportFileInsurance;EndDate;date
prImportFileInsurance;LastUpdatedDate;datetime
prImportFileInsurance;StartDate;date
prImportFileShippingInfo;ActualDateOfArrival;date
prImportFileShippingInfo;CreatedDate;datetime
prImportFileShippingInfo;DateOfLading;date
prImportFileShippingInfo;DateOfNationalization;date
prImportFileShippingInfo;EstimatedDateOfArrival;date
prImportFileShippingInfo;LastUpdatedDate;datetime
prImportFileStatusHistory;CreatedDate;datetime
prImportFileStatusHistory;LastUpdatedDate;datetime
prImportFileStatusHistory;OperationDate;date
prInnerProcessInfo;CreatedDate;datetime
prInnerProcessInfo;LastUpdatedDate;datetime
prInnerProcessITAttribute;CreatedDate;datetime
prInnerProcessITAttribute;LastUpdatedDate;datetime
prInnerProcessItemType;CreatedDate;datetime
prInnerProcessItemType;LastUpdatedDate;datetime
prInsuranceAgencyContribution;CreatedDate;datetime
prInsuranceAgencyContribution;LastUpdatedDate;datetime
prInteractiveSMSApplications;CreatedDate;datetime
prInteractiveSMSApplications;LastUpdatedDate;datetime
prITAttributeTypeRequiredProcesses;CreatedDate;datetime
prITAttributeTypeRequiredProcesses;LastUpdatedDate;datetime
prItemAccountGrGLAccs;CreatedDate;datetime
prItemAccountGrGLAccs;LastUpdatedDate;datetime
prItemAirportSalesCommissionGroup;CreatedDate;datetime
prItemAirportSalesCommissionGroup;LastUpdatedDate;datetime
prItemAlike;CreatedDate;datetime
prItemAlike;LastUpdatedDate;datetime
prItemAttribute;CreatedDate;datetime
prItemAttribute;LastUpdatedDate;datetime
prItemBarcode;CreatedDate;datetime
prItemBarcode;LastUpdatedDate;datetime
prItemBasePrice;CreatedDate;datetime
prItemBasePrice;LastUpdatedDate;datetime
prItemBasePrice;PriceDate;date
prItemBatchBarcode;CreatedDate;datetime
prItemBatchBarcode;LastUpdatedDate;datetime
prItemColorAttributes;CreatedDate;datetime
prItemColorAttributes;LastUpdatedDate;datetime
prItemColorFabricBlend;CreatedDate;datetime
prItemColorFabricBlend;LastUpdatedDate;datetime
prItemCompanyBrand;CreatedDate;datetime
prItemCompanyBrand;LastUpdatedDate;datetime
prItemCostCenter;CreatedDate;datetime
prItemCostCenter;LastUpdatedDate;datetime
prItemCostCenterRates;CreatedDate;datetime
prItemCostCenterRates;LastUpdatedDate;datetime
prItemCrossUnitOfMeasure;CreatedDate;datetime
prItemCrossUnitOfMeasure;LastUpdatedDate;datetime
prItemDim1Equ;CreatedDate;datetime
prItemDim1Equ;LastUpdatedDate;datetime
prItemDim2Equ;CreatedDate;datetime
prItemDim2Equ;LastUpdatedDate;datetime
prItemDim3Equ;CreatedDate;datetime
prItemDim3Equ;LastUpdatedDate;datetime
prItemDiscountGrAtt;CreatedDate;datetime
prItemDiscountGrAtt;LastUpdatedDate;datetime
prItemInformation;CreatedDate;datetime
prItemInformation;LastUpdatedDate;datetime
prItemListContent;CreatedDate;datetime
prItemListContent;LastUpdatedDate;datetime
prItemMeasuresOfVolume;CreatedDate;datetime
prItemMeasuresOfVolume;LastUpdatedDate;datetime
prItemNotes;CreatedDate;datetime
prItemNotes;LastUpdatedDate;datetime
prItemPaymentPlanGrAtt;CreatedDate;datetime
prItemPaymentPlanGrAtt;LastUpdatedDate;datetime
prItemPhoto;CreatedDate;datetime
prItemPhoto;LastUpdatedDate;datetime
prItemProcessPermits;CreatedDate;datetime
prItemProcessPermits;LastUpdatedDate;datetime
prItemRequisition;CreatedDate;datetime
prItemRequisition;LastUpdatedDate;datetime
prItemSection;CreatedDate;datetime
prItemSection;LastUpdatedDate;datetime
prItemSerialNumber;CreatedDate;datetime
prItemSerialNumber;LastUpdatedDate;datetime
prItemSerialNumberPool;CreatedDate;datetime
prItemSerialNumberPool;LastUpdatedDate;datetime
prItemStockLevel;CreatedDate;datetime
prItemStockLevel;LastUpdatedDate;datetime
prItemTaxGrAtt;CreatedDate;datetime
prItemTaxGrAtt;LastUpdatedDate;datetime
prItemTextileCareSymbol;CreatedDate;datetime
prItemTextileCareSymbol;LastUpdatedDate;datetime
prItemTextileCareTemplateSymbol;CreatedDate;datetime
prItemTextileCareTemplateSymbol;LastUpdatedDate;datetime
prItemUnAcceptableExpense;CreatedDate;datetime
prItemUnAcceptableExpense;LastUpdatedDate;datetime
prItemVariant;CreatedDate;datetime
prItemVariant;LastUpdatedDate;datetime
prItemVendorGrAtt;CreatedDate;datetime
prItemVendorGrAtt;LastUpdatedDate;datetime
prJobTrainingAttribute;CreatedDate;datetime
prJobTrainingAttribute;LastUpdatedDate;datetime
prJobTrainingNotes;CreatedDate;datetime
prJobTrainingNotes;LastUpdatedDate;datetime
prJobTrainingPlanned;CreatedDate;datetime
prJobTrainingPlanned;EndDate;date
prJobTrainingPlanned;LastUpdatedDate;datetime
prJobTrainingPlanned;StartDate;date
prJobTrainingRealised;CreatedDate;datetime
prJobTrainingRealised;JobTrainingDate;date
prJobTrainingRealised;LastUpdatedDate;datetime
prLetterOfGuaranteeAttribute;CreatedDate;datetime
prLetterOfGuaranteeAttribute;LastUpdatedDate;datetime
prLinkedProductContent;CreatedDate;datetime
prLinkedProductContent;LastUpdatedDate;datetime
prLinkedProductContentSum;CreatedDate;datetime
prLinkedProductContentSum;LastUpdatedDate;datetime
prLinkedProductContentSumDetail;CreatedDate;datetime
prLinkedProductContentSumDetail;LastUpdatedDate;datetime
prLinkedProductProperties;CreatedDate;datetime
prLinkedProductProperties;LastUpdatedDate;datetime
prLotQty;CreatedDate;datetime
prLotQty;LastUpdatedDate;datetime
prLoyaltyProgramLevel;CreatedDate;datetime
prLoyaltyProgramLevel;EndDate;datetime
prLoyaltyProgramLevel;LastUpdatedDate;datetime
prLoyaltyProgramLevel;StartDate;datetime
prLoyaltyProgramLevelHistory;CreatedDate;datetime
prLoyaltyProgramLevelHistory;EndDate;datetime
prLoyaltyProgramLevelHistory;LastUpdatedDate;datetime
prLoyaltyProgramLevelHistory;OperationDate;datetime
prLoyaltyProgramLevelHistory;StartDate;datetime
prLoyaltyProgramNotes;CreatedDate;datetime
prLoyaltyProgramNotes;LastUpdatedDate;datetime
prLoyaltyProgramProcessAvailableStatus;CreatedDate;datetime
prLoyaltyProgramProcessAvailableStatus;LastUpdatedDate;datetime
prLoyaltyProgramProcessAvailableStatusHistory;CreatedDate;datetime
prLoyaltyProgramProcessAvailableStatusHistory;LastUpdatedDate;datetime
prLoyaltyProgramProcessAvailableStatusHistory;OperationDate;datetime
prLoyaltyProgramProcessStatus;CreatedDate;datetime
prLoyaltyProgramProcessStatus;LastUpdatedDate;datetime
prLoyaltyProgramProcessStatusHistory;CreatedDate;datetime
prLoyaltyProgramProcessStatusHistory;LastUpdatedDate;datetime
prLoyaltyProgramProcessStatusHistory;OperationDate;datetime
prMarketPlaceCategoryAttConvert;CreatedDate;datetime
prMarketPlaceCategoryAttConvert;LastUpdatedDate;datetime
prMarketPlaceCategoryAttType;CreatedDate;datetime
prMarketPlaceCategoryAttType;LastUpdatedDate;datetime
prMarketPlaceCategoryAttTypeConvert;CreatedDate;datetime
prMarketPlaceCategoryAttTypeConvert;LastUpdatedDate;datetime
prMarketPlaceCategoryConvert;CreatedDate;datetime
prMarketPlaceCategoryConvert;LastUpdatedDate;datetime
prMarketPlaceCreditCardMappings;CreatedDate;datetime
prMarketPlaceCreditCardMappings;LastUpdatedDate;datetime
prMarketPlaceItemVariant;CreatedDate;datetime
prMarketPlaceItemVariant;LastUpdatedDate;datetime
prMarketPlaceProduct;CreatedDate;datetime
prMarketPlaceProduct;LastUpdatedDate;datetime
prMarketPlaceProductHierarchyConvert;CreatedDate;datetime
prMarketPlaceProductHierarchyConvert;LastUpdatedDate;datetime
prMarketPlaceProductInformation;CreatedDate;datetime
prMarketPlaceProductInformation;LastSendDate;datetime
prMarketPlaceProductInformation;LastUpdatedDate;datetime
prMarketPlaceProductInformation;SaleEndDate;date
prMarketPlaceProductInformation;SaleStartDate;date
prMDMCommunication;CreatedDate;datetime
prMDMCommunication;LastUpdatedDate;datetime
prMDMPostalAddress;CreatedDate;datetime
prMDMPostalAddress;LastUpdatedDate;datetime
prMedicalProductImportCountries;CreatedDate;datetime
prMedicalProductImportCountries;LastUpdatedDate;datetime
prMedicalProductOriginCountries;CreatedDate;datetime
prMedicalProductOriginCountries;LastUpdatedDate;datetime
prMedicalProductProperties;CreatedDate;datetime
prMedicalProductProperties;LastUpdatedDate;datetime
prMT940ProcessRules;CreatedDate;datetime
prMT940ProcessRules;LastUpdatedDate;datetime
prNotesGLAccs;CreatedDate;datetime
prNotesGLAccs;LastUpdatedDate;datetime
prOfficeCOGSGrAtt;CreatedDate;datetime
prOfficeCOGSGrAtt;LastUpdatedDate;datetime
prOfficeGLAccs;CreatedDate;datetime
prOfficeGLAccs;LastUpdatedDate;datetime
prOfficeMapLocation;CreatedDate;datetime
prOfficeMapLocation;LastUpdatedDate;datetime
prOnlineBankWebServiceBankInternalParameter;CreatedDate;datetime
prOnlineBankWebServiceBankInternalParameter;LastUpdatedDate;datetime
prOnlineBankWebServiceCreditCardParameter;CreatedDate;datetime
prOnlineBankWebServiceCreditCardParameter;LastUpdatedDate;datetime
prOnlineDBSLimit;CreatedDate;datetime
prOnlineDBSLimit;LastUpdateDate;datetime
prOnlineDBSLimit;LastUpdatedDate;datetime
prOnlineDBSLimitHistory;CreatedDate;datetime
prOnlineDBSLimitHistory;LastUpdateDate;datetime
prOnlineDBSLimitHistory;LastUpdatedDate;datetime
prOpticalSutContributionAmount;CreatedDate;datetime
prOpticalSutContributionAmount;LastUpdatedDate;datetime
prOpticalSutContributionAmount;StartDate;date
prPaylinkCardOwner;CreatedDate;datetime
prPaylinkCardOwner;LastUpdatedDate;datetime
prPaymentNumberGroup;CreatedDate;datetime
prPaymentNumberGroup;LastUpdatedDate;datetime
prPaymentPlanAdditionalInstallmentAuthority;CreatedDate;datetime
prPaymentPlanAdditionalInstallmentAuthority;LastUpdatedDate;datetime
prPaymentPlanAdditionalInstallmentCampaign;CreatedDate;datetime
prPaymentPlanAdditionalInstallmentCampaign;EndDate;date
prPaymentPlanAdditionalInstallmentCampaign;LastUpdatedDate;datetime
prPaymentPlanAdditionalInstallmentCampaign;StartDate;date
prPaymentPlanBIN;CreatedDate;datetime
prPaymentPlanBIN;LastUpdatedDate;datetime
prPaymentProviderConvert;CreatedDate;datetime
prPaymentProviderConvert;LastUpdatedDate;datetime
prPaymentProviderGLAccs;CreatedDate;datetime
prPaymentProviderGLAccs;LastUpdatedDate;datetime
prPCTGLAccs;CreatedDate;datetime
prPCTGLAccs;LastUpdatedDate;datetime
prPersonalDataConfirmationFormTypeForCurrAccTypes;CreatedDate;datetime
prPersonalDataConfirmationFormTypeForCurrAccTypes;LastUpdatedDate;datetime
prPOSTerminalATAttribute;CreatedDate;datetime
prPOSTerminalATAttribute;LastUpdatedDate;datetime
prPosTerminalDevice;CreatedDate;datetime
prPosTerminalDevice;LastUpdatedDate;datetime
prPosTerminalFiscalPrinter;CreatedDate;datetime
prPosTerminalFiscalPrinter;LastUpdatedDate;datetime
prPOSTerminalPrinter;CreatedDate;datetime
prPOSTerminalPrinter;LastUpdatedDate;datetime
prPOSTerminalSlipPrinter;CreatedDate;datetime
prPOSTerminalSlipPrinter;LastUpdatedDate;datetime
prPresentCardActivationSteps;CreatedDate;datetime
prPresentCardActivationSteps;LastUpdatedDate;datetime
prPresentCardValidCardTypes;CreatedDate;datetime
prPresentCardValidCardTypes;LastUpdatedDate;datetime
prProcessATAttribute;CreatedDate;datetime
prProcessATAttribute;LastUpdatedDate;datetime
prProcessDefaultExpenseType;CreatedDate;datetime
prProcessDefaultExpenseType;LastUpdatedDate;datetime
prProcessDiscount;CreatedDate;datetime
prProcessDiscount;LastUpdatedDate;datetime
prProcessFlowRules;CreatedDate;datetime
prProcessFlowRules;LastUpdatedDate;datetime
prProcessFTAttribute;CreatedDate;datetime
prProcessFTAttribute;LastUpdatedDate;datetime
prProcessInfo;CreatedDate;datetime
prProcessInfo;LastUpdatedDate;datetime
prProcessITAttribute;CreatedDate;datetime
prProcessITAttribute;LastUpdatedDate;datetime
prProcessItemType;CreatedDate;datetime
prProcessItemType;LastUpdatedDate;datetime
prProductCareWarning;CreatedDate;datetime
prProductCareWarning;LastUpdatedDate;datetime
prProductColorAttribute;CreatedDate;datetime
prProductColorAttribute;LastUpdatedDate;datetime
prProductColorSetContent;CreatedDate;datetime
prProductColorSetContent;LastUpdatedDate;datetime
prProductDimSetContent;CreatedDate;datetime
prProductDimSetContent;LastUpdatedDate;datetime
prProductFrameProperties;CreatedDate;datetime
prProductFrameProperties;LastUpdatedDate;datetime
prProductImageURLs;CreatedDate;datetime
prProductImageURLs;LastUpdatedDate;datetime
prProductLensProperties;CreatedDate;datetime
prProductLensProperties;LastUpdatedDate;datetime
prProductLot;CreatedDate;datetime
prProductLot;LastUpdatedDate;datetime
prProductLotBarcode;CreatedDate;datetime
prProductLotBarcode;LastUpdatedDate;datetime
prProductPartAvailableFabric;CreatedDate;datetime
prProductPartAvailableFabric;LastUpdatedDate;datetime
prProductPoint;CreatedDate;datetime
prProductPoint;LastUpdatedDate;datetime
prProductStatusHistory;CreatedDate;datetime
prProductStatusHistory;LastUpdatedDate;datetime
prProductStatusHistory;OperationDate;date
prProposalConfirmationRuleDepartments;CreatedDate;datetime
prProposalConfirmationRuleDepartments;LastUpdatedDate;datetime
prProposalConfirmationRuleStep;CreatedDate;datetime
prProposalConfirmationRuleStep;LastUpdatedDate;datetime
prProposalConfirmationRuleStepUser;CreatedDate;datetime
prProposalConfirmationRuleStepUser;LastUpdatedDate;datetime
prPurchasingAgentAvailableRequisition;CreatedDate;datetime
prPurchasingAgentAvailableRequisition;LastUpdatedDate;datetime
prRelatedCurrAcc;CreatedDate;datetime
prRelatedCurrAcc;LastUpdatedDate;datetime
prRelationalPriceGroups;CreatedDate;datetime
prRelationalPriceGroups;LastUpdatedDate;datetime
prRequisitionAttribute;CreatedDate;datetime
prRequisitionAttribute;LastUpdatedDate;datetime
prRequisitionConfirmationRuleDepartments;CreatedDate;datetime
prRequisitionConfirmationRuleDepartments;LastUpdatedDate;datetime
prRequisitionConfirmationRuleStep;CreatedDate;datetime
prRequisitionConfirmationRuleStep;LastUpdatedDate;datetime
prRequisitionConfirmationRuleStepUser;CreatedDate;datetime
prRequisitionConfirmationRuleStepUser;LastUpdatedDate;datetime
prRequisitionCurrAcc;CreatedDate;datetime
prRequisitionCurrAcc;LastUpdatedDate;datetime
prRequisitionLimit;CreatedDate;datetime
prRequisitionLimit;LastUpdatedDate;datetime
prResponsibilityAreaPostalAddress;CreatedDate;datetime
prResponsibilityAreaPostalAddress;LastUpdatedDate;datetime
prRetailCustomerSegmentationFilters;CreatedDate;datetime
prRetailCustomerSegmentationFilters;LastUpdatedDate;datetime
prRetailCustomerSegmentationSQL;CreatedDate;datetime
prRetailCustomerSegmentationSQL;LastUpdatedDate;datetime
prReturnReasonAvailableProcess;CreatedDate;datetime
prReturnReasonAvailableProcess;LastUpdatedDate;datetime
prRoleMember;CreatedDate;datetime
prRoleMember;LastUpdatedDate;datetime
prRollNotes;CreatedDate;datetime
prRollNotes;LastUpdatedDate;datetime
prRoundsmanResponsibilityArea;CreatedDate;datetime
prRoundsmanResponsibilityArea;LastUpdatedDate;datetime
prSection;CreatedDate;datetime
prSection;LastUpdatedDate;datetime
prServiceAvailableProductLevel;CreatedDate;datetime
prServiceAvailableProductLevel;LastUpdatedDate;datetime
prServiceAvailableSupportType;CreatedDate;datetime
prServiceAvailableSupportType;LastUpdatedDate;datetime
prStoreBankAccCodesForPayment;CreatedDate;datetime
prStoreBankAccCodesForPayment;LastUpdatedDate;datetime
prStoreBankPOSAccounts;CreatedDate;datetime
prStoreBankPOSAccounts;LastUpdatedDate;datetime
prStoreBankPOSGLAccs;CreatedDate;datetime
prStoreBankPOSGLAccs;LastUpdatedDate;datetime
prStoreCapacity;CreatedDate;datetime
prStoreCapacity;LastUpdatedDate;datetime
prStoreCashAcc;CreatedDate;datetime
prStoreCashAcc;LastUpdatedDate;datetime
prStoreCustomerGLAccount;CreatedDate;datetime
prStoreCustomerGLAccount;LastUpdatedDate;datetime
prStoreProperties;CreatedDate;datetime
prStoreProperties;LastRemodelDate;date
prStoreProperties;LastUpdatedDate;datetime
prStoreSpecialDay;CreatedDate;datetime
prStoreSpecialDay;LastUpdatedDate;datetime
prStoreSpecialDay;SpecialDayDate;smalldatetime
prStoreStatus;CreatedDate;datetime
prStoreStatus;LastUpdatedDate;datetime
prStoreStatus;OperationDate;date
prStoreVendor;CreatedDate;datetime
prStoreVendor;LastUpdatedDate;datetime
prStoreWorkingHours;CreatedDate;datetime
prStoreWorkingHours;LastUpdatedDate;datetime
prSubCurrAcc;AgreementDate;date
prSubCurrAcc;CreatedDate;datetime
prSubCurrAcc;LastUpdatedDate;datetime
prSubCurrAccAttribute;CreatedDate;datetime
prSubCurrAccAttribute;LastUpdatedDate;datetime
prSubCurrAccDefault;CreatedDate;datetime
prSubCurrAccDefault;LastUpdatedDate;datetime
prSubCurrAccOnlineBank;CreatedDate;datetime
prSubCurrAccOnlineBank;LastUpdatedDate;datetime
prSubCurrAccSalesperson;CreatedDate;datetime
prSubCurrAccSalesperson;LastUpdatedDate;datetime
prSubCurrAccSalesperson;StartDate;date
prTechnicalResponsibleAvailableRequisition;CreatedDate;datetime
prTechnicalResponsibleAvailableRequisition;LastUpdatedDate;datetime
prTimePeriodDay;CreatedDate;datetime
prTimePeriodDay;LastUpdatedDate;datetime
prTransferPlanResultViewCustomization;CreatedDate;datetime
prTransferPlanResultViewCustomization;LastUpdatedDate;datetime
prTransferPlanRuleScript;CreatedDate;datetime
prTransferPlanRuleScript;LastUpdatedDate;datetime
prTransferPlanTemplateParameterValue;CreatedDate;datetime
prTransferPlanTemplateParameterValue;LastUpdatedDate;datetime
prUBBDeclaredItems;CreatedDate;datetime
prUBBDeclaredItems;LastUpdatedDate;datetime
prUniFreeTenderTypeMapping;CreatedDate;datetime
prUniFreeTenderTypeMapping;LastUpdatedDate;datetime
prUTSDeclaredItems;CreatedDate;datetime
prUTSDeclaredItems;LastUpdatedDate;datetime
prV3ToMdmIDMap;CreatedDate;datetime
prV3ToMdmIDMap;LastUpdatedDate;datetime
prVatGLAccs;CreatedDate;datetime
prVatGLAccs;LastUpdatedDate;datetime
prVehicleDrivers;CreatedDate;datetime
prVehicleDrivers;LastUpdatedDate;datetime
prVendorPaymentPlanGrAtt;CreatedDate;datetime
prVendorPaymentPlanGrAtt;LastUpdatedDate;datetime
prWarehouseChannelTemplateContent;CreatedDate;datetime
prWarehouseChannelTemplateContent;LastUpdatedDate;datetime
prWarehouseMapLocation;CreatedDate;datetime
prWarehouseMapLocation;LastUpdatedDate;datetime
prWarehousePostalAddress;CreatedDate;datetime
prWarehousePostalAddress;LastUpdatedDate;datetime
prWarehouseProcessFlowRules;CreatedDate;datetime
prWarehouseProcessFlowRules;LastUpdatedDate;datetime
prWarehouseResponsibilityArea;CreatedDate;datetime
prWarehouseResponsibilityArea;LastUpdatedDate;datetime
prWithHoldingTaxAvailableDovRates;CreatedDate;datetime
prWithHoldingTaxAvailableDovRates;LastUpdatedDate;datetime
prWorkPlaceATAttribute;CreatedDate;datetime
prWorkPlaceATAttribute;LastUpdatedDate;datetime
prWorkPlaceFTAttribute;CreatedDate;datetime
prWorkPlaceFTAttribute;LastUpdatedDate;datetime
prWorkPlaceGLAccs;CreatedDate;datetime
prWorkPlaceGLAccs;LastUpdatedDate;datetime
prWorkPlaceOptimalEmployment;CreatedDate;datetime
prWorkPlaceOptimalEmployment;LastUpdatedDate;datetime
prWorkPlaceSecondment;CancelDate;date
prWorkPlaceSecondment;CreatedDate;datetime
prWorkPlaceSecondment;EndDate;date
prWorkPlaceSecondment;LastUpdatedDate;datetime
prWorkPlaceSecondment;StartDate;date
prWorkplaceSGKLogonInfo;CreatedDate;datetime
prWorkplaceSGKLogonInfo;LastUpdatedDate;datetime
rpAirConnUSB;CreatedDate;datetime
rpAirConnUSB;LastUpdatedDate;datetime
rpAirConnUSB_CashIn;CreatedDate;datetime
rpAirConnUSB_CashIn;LastUpdatedDate;datetime
rpAirConnUSB_CashOut;CreatedDate;datetime
rpAirConnUSB_CashOut;LastUpdatedDate;datetime
rpAirConnUSB_Correction;CreatedDate;datetime
rpAirConnUSB_Correction;LastUpdatedDate;datetime
rpAirConnUSB_GetControlTapeHeader;createdAtUtc;datetime
rpAirConnUSB_GetControlTapeHeader;CreatedDate;datetime
rpAirConnUSB_GetControlTapeHeader;LastUpdatedDate;datetime
rpAirConnUSB_GetControlTapeHeader;shiftCloseAtUtc;datetime
rpAirConnUSB_GetControlTapeHeader;shiftOpenAtUtc;datetime
rpAirConnUSB_GetControlTapeLine;createdAtUtc;datetime
rpAirConnUSB_GetControlTapeLine;CreatedDate;datetime
rpAirConnUSB_GetControlTapeLine;LastUpdatedDate;datetime
rpAirConnUSB_GetInfo;CreatedDate;datetime
rpAirConnUSB_GetInfo;last_online_time;datetime
rpAirConnUSB_GetInfo;LastUpdatedDate;datetime
rpAirConnUSB_GetInfo;not_after;datetime
rpAirConnUSB_GetInfo;not_before;datetime
rpAirConnUSB_GetInfo;oldest_document_time;datetime
rpAirConnUSB_Invoice;CreatedDate;datetime
rpAirConnUSB_Invoice;LastUpdatedDate;datetime
rpAirConnUSB_PeriodicZReport;createdAtUtc;datetime
rpAirConnUSB_PeriodicZReport;CreatedDate;datetime
rpAirConnUSB_PeriodicZReport;LastUpdatedDate;datetime
rpAirConnUSB_PeriodicZReport;Request_From;datetime
rpAirConnUSB_PeriodicZReport;Request_To;datetime
rpAirConnUSB_PeriodicZReport;shiftCloseAtUtc;datetime
rpAirConnUSB_PeriodicZReport;shiftOpenAtUtc;datetime
rpAirConnUSB_XReport;createdAtUtc;datetime
rpAirConnUSB_XReport;CreatedDate;datetime
rpAirConnUSB_XReport;LastUpdatedDate;datetime
rpAirConnUSB_XReport;shiftCloseAtUtc;datetime
rpAirConnUSB_XReport;shiftOpenAtUtc;datetime
rpAirConnUSB_ZReport;createdAtUtc;datetime
rpAirConnUSB_ZReport;CreatedDate;datetime
rpAirConnUSB_ZReport;LastUpdatedDate;datetime
rpAirConnUSB_ZReport;shiftCloseAtUtc;datetime
rpAirConnUSB_ZReport;shiftOpenAtUtc;datetime
rpBrowsedProduct;CreatedDate;datetime
rpBrowsedProduct;LastUpdatedDate;datetime
rpCompareTransactionHeader;CreatedDate;datetime
rpCompareTransactionHeader;LastUpdatedDate;datetime
rpCompareTransactionHeader;OperationDate;date
rpCompareTransactionLine;CreatedDate;datetime
rpCompareTransactionLine;LastUpdatedDate;datetime
rpCompareTransactionSourceFiles;CreatedDate;datetime
rpCompareTransactionSourceFiles;LastUpdatedDate;datetime
rpCompareTransactionTargetFiles;CreatedDate;datetime
rpCompareTransactionTargetFiles;LastUpdatedDate;datetime
rpEArchiveIntegratorControl;CreatedDate;datetime
rpEArchiveIntegratorControl;InvoiceDate;date
rpEArchiveIntegratorControl;LastUpdatedDate;datetime
rpEuromessageCampaignConversations;CreatedDate;datetime
rpEuromessageCampaignConversations;LastUpdatedDate;datetime
rpEuromessageCampaignDeliveryStatusDetail;CreatedDate;datetime
rpEuromessageCampaignDeliveryStatusDetail;LAST_CHANGE_TIME;datetime
rpEuromessageCampaignDeliveryStatusDetail;LastUpdatedDate;datetime
rpEuromessageCampaigns;CreatedDate;datetime
rpEuromessageCampaigns;CreationDate;datetime
rpEuromessageCampaigns;DeliveryFinish;datetime
rpEuromessageCampaigns;DeliveryStart;datetime
rpEuromessageCampaigns;LastUpdatedDate;datetime
rpEuromessageUnsubscriberDetails;CreatedDate;datetime
rpEuromessageUnsubscriberDetails;LastUpdatedDate;datetime
rpEuromessageUnsubscriberDetails;UnsubscribeTime;datetime
rpExportData;CreatedDate;datetime
rpExportData;LastUpdatedDate;datetime
rpExternalItemFileHeader;CreatedDate;datetime
rpExternalItemFileHeader;DocumentDate;date
rpExternalItemFileHeader;LastUpdatedDate;datetime
rpExternalItemFileLine;CreatedDate;datetime
rpExternalItemFileLine;LastUpdatedDate;datetime
rpGrandLedgerDetail;CreatedDate;datetime
rpGrandLedgerDetail;LastUpdatedDate;datetime
rpGrandLedgerDetail;LedgerEntryDate;date
rpGrandLedgerTotals;CreatedDate;datetime
rpGrandLedgerTotals;LastUpdatedDate;datetime
rpJournalLedgerDetail;CreatedDate;datetime
rpJournalLedgerDetail;LastUpdatedDate;datetime
rpJournalLedgerDetail;LedgerEntryDate;date
rpMobildevIVTContactReconciliation;CreatedAt;datetime
rpMobildevIVTContactReconciliation;CreatedDate;datetime
rpMobildevIVTContactReconciliation;LastUpdatedDate;datetime
rpMobildevIVTContactReconciliation;logAt;datetime
rpMobildevIVTContactReconciliation;updatedAt;datetime
rpMobildevIVTEmailReconciliation;CreatedAt;datetime
rpMobildevIVTEmailReconciliation;CreatedDate;datetime
rpMobildevIVTEmailReconciliation;LastUpdatedDate;datetime
rpMobilDevIVTLiteEmailReconciliation;createdAt;datetime
rpMobilDevIVTLiteEmailReconciliation;CreatedDate;datetime
rpMobilDevIVTLiteEmailReconciliation;detail_createdAt;datetime
rpMobilDevIVTLiteEmailReconciliation;detail_permissiondate;datetime
rpMobilDevIVTLiteEmailReconciliation;detail_updatedAt;datetime
rpMobilDevIVTLiteEmailReconciliation;LastUpdatedDate;datetime
rpMobilDevIVTLiteEmailReconciliation;updatedAt;datetime
rpMobilDevIVTLiteMsisdnReconciliation;createdAt;datetime
rpMobilDevIVTLiteMsisdnReconciliation;CreatedDate;datetime
rpMobilDevIVTLiteMsisdnReconciliation;detail_createdat;datetime
rpMobilDevIVTLiteMsisdnReconciliation;detail_permissiondate;datetime
rpMobilDevIVTLiteMsisdnReconciliation;detail_updatedAt;datetime
rpMobilDevIVTLiteMsisdnReconciliation;LastUpdatedDate;datetime
rpMobilDevIVTLiteMsisdnReconciliation;updatedAt;datetime
rpMobildevIVTReconciliation;CreatedDate;datetime
rpMobildevIVTReconciliation;LastUpdatedDate;datetime
rpMobildevIVTReconciliation;UpdatedAt;datetime
rpNegativeInventoryStockID;CreatedDate;datetime
rpNegativeInventoryStockID;LastUpdatedDate;datetime
rpOnlineBankFuturePosIncomeByOperationAndDueDate;CreatedDate;datetime
rpOnlineBankFuturePosIncomeByOperationAndDueDate;LastUpdatedDate;datetime
rpOnlineBankFuturePosIncomeByOperationAndDueDate;PaymentDate;date
rpOnlineBankFuturePosIncomeByOperationAndDueDate;ValorDate;date
rpOnlineBankFuturePosIncomeByReturnDate;CreatedDate;datetime
rpOnlineBankFuturePosIncomeByReturnDate;LastUpdatedDate;datetime
rpOnlineBankFuturePosIncomeByReturnDate;PaymentDate;date
rpOnlineBankFuturePosIncomeByReturnDate;ValorDate;date
rpOrderDeliveryAssignmentCollectedItems;CreatedDate;datetime
rpOrderDeliveryAssignmentCollectedItems;LastUpdatedDate;datetime
rpOrderDeliveryAssignmentCollectedItems;OperationDate;date
rpOrderDeliveryAssignmentCollectedItems;OrderDeliveryDate;date
rpProductDatamatrixLabel;CreatedDate;datetime
rpProductDatamatrixLabel;LastUpdatedDate;datetime
rpProductDatamatrixLabel;ListDate;date
rpProductLabel;CreatedDate;datetime
rpProductLabel;LastUpdatedDate;datetime
rpProductLabel;ListDate;date
rpProductLabelRollNumber;CreatedDate;datetime
rpProductLabelRollNumber;LastUpdatedDate;datetime
rpProposalLineConfirmationHistory;ConfirmedDate;smalldatetime
rpProposalLineConfirmationHistory;CreatedDate;datetime
rpProposalLineConfirmationHistory;LastUpdatedDate;datetime
rpPurchaseRequisitionConfirmationHistory;ConfirmedDate;smalldatetime
rpPurchaseRequisitionConfirmationHistory;CreatedDate;datetime
rpPurchaseRequisitionConfirmationHistory;LastUpdatedDate;datetime
rpPurchaseRequisitionProposalConfirmationHistory;ConfirmedDate;smalldatetime
rpPurchaseRequisitionProposalConfirmationHistory;CreatedDate;datetime
rpPurchaseRequisitionProposalConfirmationHistory;LastUpdatedDate;datetime
rpRegisteredEmailForPayrollSendEvidence;CreatedDate;datetime
rpRegisteredEmailForPayrollSendEvidence;EvidenceDate;date
rpRegisteredEmailForPayrollSendEvidence;LastUpdatedDate;datetime
rpRegisteredEmailForPayrollSendStatus;CreatedDate;datetime
rpRegisteredEmailForPayrollSendStatus;LastUpdatedDate;datetime
rpSelectedProduct;CreatedDate;datetime
rpSelectedProduct;LastUpdatedDate;datetime
rpSelectedProductInvoice;CreatedDate;datetime
rpSelectedProductInvoice;LastUpdatedDate;datetime
rpSelectedProductOrder;CreatedDate;datetime
rpSelectedProductOrder;LastUpdatedDate;datetime
rpSubjectToEInvoice;CreatedDate;datetime
rpSubjectToEInvoice;LastUpdatedDate;datetime
rpTransferApproved;CreatedDate;datetime
rpTransferApproved;LastUpdatedDate;datetime
rpTuratelACLBlackListReconciliation;CreatedDate;datetime
rpTuratelACLBlackListReconciliation;InsertDate;datetime
rpTuratelACLBlackListReconciliation;LastUpdatedDate;datetime
rpTuratelACLBlackListReconciliation;RecordDate;datetime
rpTuratelACLWhiteListReconciliation;CreatedDate;datetime
rpTuratelACLWhiteListReconciliation;InsertDate;datetime
rpTuratelACLWhiteListReconciliation;LastUpdatedDate;datetime
rpTuratelACLWhiteListReconciliation;RecordDate;datetime
rpTuratelADMAuthorizationReconciliation;CreatedDate;datetime
rpTuratelADMAuthorizationReconciliation;LastUpdatedDate;datetime
rpTuratelADMCommunicationReconciliation;CreatedDate;datetime
rpTuratelADMCommunicationReconciliation;LastUpdatedDate;datetime
SqlMonth;CreatedDate;datetime
SqlMonth;LastUpdatedDate;datetime
srCashSerialNumber;CreatedDate;datetime
srCashSerialNumber;LastUpdatedDate;datetime
srChequesSerialNumber;CreatedDate;datetime
srChequesSerialNumber;LastUpdatedDate;datetime
srCurrAccListNumber;CreatedDate;datetime
srCurrAccListNumber;LastUpdatedDate;datetime
srCustomerConversationFormNumber;CreatedDate;datetime
srCustomerConversationFormNumber;LastUpdatedDate;datetime
srDistanceSaleBankPaymentNumber;CreatedDate;datetime
srDistanceSaleBankPaymentNumber;LastUpdatedDate;datetime
srEArchiveSerialNumber;CreatedDate;datetime
srEArchiveSerialNumber;LastUpdatedDate;datetime
srEInvoiceSerialNumber;CreatedDate;datetime
srEInvoiceSerialNumber;LastUpdatedDate;datetime
srEShipmentSerialNumber;CreatedDate;datetime
srEShipmentSerialNumber;LastUpdatedDate;datetime
srExpenseInvoiceDocumentNumber;CreatedDate;datetime
srExpenseInvoiceDocumentNumber;LastUpdatedDate;datetime
srFormNumberCommunication;OperationDate;date
srOnlineInstallmentBankPayment;CreatedDate;datetime
srOnlineInstallmentBankPayment;LastUpdatedDate;datetime
srOpticalProtocolNumber;CreatedDate;datetime
srOpticalProtocolNumber;LastUpdatedDate;datetime
srPayrollDocumentNumber;CreatedDate;datetime
srPayrollDocumentNumber;LastUpdatedDate;datetime
srRefNumberJournal;JournalDate;date
srSerialNumber;CreatedDate;datetime
srSerialNumber;LastUpdatedDate;datetime
stProductFirstIncomingDate;FirstIncomingDate;date
stProductFirstIncomingDate;LastIncomingDate;date
tpAgentContractDeservedDebit;CreatedDate;datetime
tpAgentContractDeservedDebit;LastUpdatedDate;datetime
tpAgentContractVehicleDebit;CreatedDate;datetime
tpAgentContractVehicleDebit;LastUpdatedDate;datetime
tpAgentContractVisitFrequencyDebit;CreatedDate;datetime
tpAgentContractVisitFrequencyDebit;LastUpdatedDate;datetime
tpAgentPerformanceBonusDebit;CreatedDate;datetime
tpAgentPerformanceBonusDebit;LastUpdatedDate;datetime
tpAgentPerformanceDebit;CreatedDate;datetime
tpAgentPerformanceDebit;LastUpdatedDate;datetime
tpAgentReservationActualPax;CreatedDate;datetime
tpAgentReservationActualPax;LastUpdatedDate;datetime
tpAgentReservationReasonForNotShopping;CreatedDate;datetime
tpAgentReservationReasonForNotShopping;LastUpdatedDate;datetime
tpAllocationATAttribute;CreatedDate;datetime
tpAllocationATAttribute;LastUpdatedDate;datetime
tpAllocationITAttribute;CreatedDate;datetime
tpAllocationITAttribute;LastUpdatedDate;datetime
tpBadDebtLawyerHistory;CreatedDate;datetime
tpBadDebtLawyerHistory;LastUpdatedDate;datetime
tpBankATAttribute;CreatedDate;datetime
tpBankATAttribute;LastUpdatedDate;datetime
tpBankCreditATAttribute;CreatedDate;datetime
tpBankCreditATAttribute;LastUpdatedDate;datetime
tpBankCreditFTAttribute;CreatedDate;datetime
tpBankCreditFTAttribute;LastUpdatedDate;datetime
tpBankCreditRelatedCheques;CreatedDate;datetime
tpBankCreditRelatedCheques;LastUpdatedDate;datetime
tpBankCreditRelatedExportFiles;CreatedDate;datetime
tpBankCreditRelatedExportFiles;LastUpdatedDate;datetime
tpBankCreditRotativeInterestRates;CreatedDate;datetime
tpBankCreditRotativeInterestRates;JournalDate;date
tpBankCreditRotativeInterestRates;LastUpdatedDate;datetime
tpBankFTAttribute;CreatedDate;datetime
tpBankFTAttribute;LastUpdatedDate;datetime
tpBankHeaderOnlineBankIntegration;CreatedDate;datetime
tpBankHeaderOnlineBankIntegration;LastUpdatedDate;datetime
tpBankMT940;CreatedDate;datetime
tpBankMT940;LastUpdatedDate;datetime
tpBankPaymentInstructionATAttribute;CreatedDate;datetime
tpBankPaymentInstructionATAttribute;LastUpdatedDate;datetime
tpBankPaymentInstructionFTAttribute;CreatedDate;datetime
tpBankPaymentInstructionFTAttribute;LastUpdatedDate;datetime
tpBankPaymentListATAttribute;CreatedDate;datetime
tpBankPaymentListATAttribute;LastUpdatedDate;datetime
tpBankPaymentListFTAttribute;CreatedDate;datetime
tpBankPaymentListFTAttribute;LastUpdatedDate;datetime
tpBulutTahsilatCreditCardPayment;CreatedDate;datetime
tpBulutTahsilatCreditCardPayment;LastUpdatedDate;datetime
tpCanceledEArchiveInvoice;CreatedDate;datetime
tpCanceledEArchiveInvoice;InvoiceDate;date
tpCanceledEArchiveInvoice;LastUpdatedDate;datetime
tpCanceledUTSDeclaration;CancelledDate;datetime
tpCanceledUTSDeclaration;DocumentDate;date
tpCashATAttribute;CreatedDate;datetime
tpCashATAttribute;LastUpdatedDate;datetime
tpCashFTAttribute;CreatedDate;datetime
tpCashFTAttribute;LastUpdatedDate;datetime
tpCashRegisterInfo;CreatedDate;datetime
tpCashRegisterInfo;DocumentDate;date
tpCashRegisterInfo;LastUpdatedDate;datetime
tpChequeATAttribute;CreatedDate;datetime
tpChequeATAttribute;LastUpdatedDate;datetime
tpChequeFTAttribute;CreatedDate;datetime
tpChequeFTAttribute;LastUpdatedDate;datetime
tpCompanyCreditCardPaymentDueDate;CreatedDate;datetime
tpCompanyCreditCardPaymentDueDate;DueDate;date
tpCompanyCreditCardPaymentDueDate;LastUpdatedDate;datetime
tpContractATAttribute;CreatedDate;datetime
tpContractATAttribute;LastUpdatedDate;datetime
tpContractITAttribute;CreatedDate;datetime
tpContractITAttribute;LastUpdatedDate;datetime
tpCreditCardBulutTahsilatVPOSReturn;CreatedDate;datetime
tpCreditCardBulutTahsilatVPOSReturn;LastUpdatedDate;datetime
tpCreditCardPaymentATAttribute;CreatedDate;datetime
tpCreditCardPaymentATAttribute;LastUpdatedDate;datetime
tpCreditCardPaymentDueDate;CalculatedDueDate;date
tpCreditCardPaymentDueDate;CreatedDate;datetime
tpCreditCardPaymentDueDate;Duedate;date
tpCreditCardPaymentDueDate;LastUpdatedDate;datetime
tpCreditCardPaymentFTAttribute;CreatedDate;datetime
tpCreditCardPaymentFTAttribute;LastUpdatedDate;datetime
tpCreditCardPaymentHeaderOnlineBankIntegration;CreatedDate;datetime
tpCreditCardPaymentHeaderOnlineBankIntegration;LastUpdatedDate;datetime
tpCurrAccBookATAttribute;CreatedDate;datetime
tpCurrAccBookATAttribute;LastUpdatedDate;datetime
tpCurrAccBookFTAttribute;CreatedDate;datetime
tpCurrAccBookFTAttribute;LastUpdatedDate;datetime
tpCustomerOnlinePaymentCorrelations;CreatedDate;datetime
tpCustomerOnlinePaymentCorrelations;LastUpdatedDate;datetime
tpCustomerOnlinePaymentCorrelations;OperationDate;datetime
tpDebitATAttribute;CreatedDate;datetime
tpDebitATAttribute;LastUpdatedDate;datetime
tpDebitFTAttribute;CreatedDate;datetime
tpDebitFTAttribute;LastUpdatedDate;datetime
tpDeletedFiscalInvoice;CreatedDate;datetime
tpDeletedFiscalInvoice;InvoiceDate;date
tpDeletedFiscalInvoice;LastUpdatedDate;datetime
tpDispOrderHeaderExtension;CreatedDate;datetime
tpDispOrderHeaderExtension;LastUpdatedDate;datetime
tpDistanceSaleBankPayment;CreatedDate;datetime
tpDistanceSaleBankPayment;LastUpdatedDate;datetime
tpEArchieveIntegratorInfo;CreatedDate;datetime
tpEArchieveIntegratorInfo;LastUpdatedDate;datetime
tpEArchiveInvoiceConfirmation;ConfirmationDate;datetime
tpEArchiveInvoiceConfirmation;CreatedDate;datetime
tpEArchiveInvoiceConfirmation;LastUpdatedDate;datetime
tpExpenseAccrualATAttribute;CreatedDate;datetime
tpExpenseAccrualATAttribute;LastUpdatedDate;datetime
tpExpenseAccrualFTAttribute;CreatedDate;datetime
tpExpenseAccrualFTAttribute;LastUpdatedDate;datetime
tpExpenseInvoiceConfirmation;ConfirmedDate;smalldatetime
tpExpenseInvoiceConfirmation;CreatedDate;datetime
tpExpenseInvoiceConfirmation;LastUpdatedDate;datetime
tpExpenseSlipATAttribute;CreatedDate;datetime
tpExpenseSlipATAttribute;LastUpdatedDate;datetime
tpExpenseSlipFTAttribute;CreatedDate;datetime
tpExpenseSlipFTAttribute;LastUpdatedDate;datetime
tpExpenseSlipTaxLine;CreatedDate;datetime
tpExpenseSlipTaxLine;LastUpdatedDate;datetime
tpExportSaleRealisition;CreatedDate;datetime
tpExportSaleRealisition;CustomsReleaseDate;date
tpExportSaleRealisition;LastUpdatedDate;datetime
tpExportSaleRealisition;RealisedDate;datetime
tpExportSaleRealisition;RealisitionDate;date
tpGiftCardPaymentATAttribute;CreatedDate;datetime
tpGiftCardPaymentATAttribute;LastUpdatedDate;datetime
tpGiftCardPaymentFTAttribute;CreatedDate;datetime
tpGiftCardPaymentFTAttribute;LastUpdatedDate;datetime
tpInnerCustomsTransferImportInvoiceLine;CreatedDate;datetime
tpInnerCustomsTransferImportInvoiceLine;LastUpdatedDate;datetime
tpInnerHeaderExtension;CreatedDate;datetime
tpInnerHeaderExtension;LastUpdatedDate;datetime
tpInnerITAttribute;CreatedDate;datetime
tpInnerITAttribute;LastUpdatedDate;datetime
tpInnerLineDocument;CreatedDate;datetime
tpInnerLineDocument;DueDate;date
tpInnerLineDocument;LastUpdatedDate;datetime
tpInnerLinePurchaseInvoiceLine;CreatedDate;datetime
tpInnerLinePurchaseInvoiceLine;LastUpdatedDate;datetime
tpInnerOrderITAttribute;CreatedDate;datetime
tpInnerOrderITAttribute;LastUpdatedDate;datetime
tpInnerTransportModeDetail;CreatedDate;datetime
tpInnerTransportModeDetail;LastUpdatedDate;datetime
tpInnerVehicleDrivers;CreatedDate;datetime
tpInnerVehicleDrivers;LastUpdatedDate;datetime
tpInStockDeclarationInfo;CreatedDate;datetime
tpInStockDeclarationInfo;DeclarationDate;date
tpInStockDeclarationInfo;LastUpdatedDate;datetime
tpInvoiceadditionalDeliveryProcessesDistance;CreatedDate;datetime
tpInvoiceadditionalDeliveryProcessesDistance;LastUpdatedDate;datetime
tpInvoiceATAttribute;CreatedDate;datetime
tpInvoiceATAttribute;LastUpdatedDate;datetime
tpInvoiceCancelDBSBankIntegration;CancelDate;date
tpInvoiceCancelDBSBankIntegration;CreatedDate;datetime
tpInvoiceCancelDBSBankIntegration;LastUpdatedDate;datetime
tpInvoiceDiscountOffer;CreatedDate;datetime
tpInvoiceDiscountOffer;LastUpdatedDate;datetime
tpInvoiceDiscountOfferContributor;CreatedDate;datetime
tpInvoiceDiscountOfferContributor;LastUpdatedDate;datetime
tpInvoiceEArchieveXML;CreatedDate;datetime
tpInvoiceEArchieveXML;LastUpdatedDate;datetime
tpInvoiceEInvoiceXML;CreatedDate;datetime
tpInvoiceEInvoiceXML;LastUpdatedDate;datetime
tpInvoiceExchangeDifferencePaidCheque;CreatedDate;datetime
tpInvoiceExchangeDifferencePaidCheque;LastUpdatedDate;datetime
tpInvoiceFTAttribute;CreatedDate;datetime
tpInvoiceFTAttribute;LastUpdatedDate;datetime
tpInvoiceHeaderExtension;ATAttributeUpdatedDate;date
tpInvoiceHeaderExtension;CreatedDate;datetime
tpInvoiceHeaderExtension;DocumentDate;date
tpInvoiceHeaderExtension;LastUpdatedDate;datetime
tpInvoiceHeaderSalesPerson;CreatedDate;datetime
tpInvoiceHeaderSalesPerson;LastUpdatedDate;datetime
tpInvoiceITAttribute;CreatedDate;datetime
tpInvoiceITAttribute;LastUpdatedDate;datetime
tpInvoiceLineAgentPerformance;CreatedDate;datetime
tpInvoiceLineAgentPerformance;LastUpdatedDate;datetime
tpInvoiceLineExpenseAccrual;CreatedDate;datetime
tpInvoiceLineExpenseAccrual;LastUpdatedDate;datetime
tpInvoiceLineExtension;CreatedDate;datetime
tpInvoiceLineExtension;LastUpdatedDate;datetime
tpInvoiceLineOpticalProductInfo;CreatedDate;datetime
tpInvoiceLineOpticalProductInfo;LastUpdatedDate;datetime
tpInvoiceLinePickingDetails;CreatedDate;datetime
tpInvoiceLinePickingDetails;LastUpdatedDate;datetime
tpInvoiceLinePickingDetails;PickingDate;date
tpInvoiceOpticalContribution;CreatedDate;datetime
tpInvoiceOpticalContribution;LastUpdatedDate;datetime
tpInvoicePassportAndBoardingInfo;BirthDay;date
tpInvoicePassportAndBoardingInfo;CreatedDate;datetime
tpInvoicePassportAndBoardingInfo;FlightDate;date
tpInvoicePassportAndBoardingInfo;LastUpdatedDate;datetime
tpInvoicePostalAddress;CreatedDate;datetime
tpInvoicePostalAddress;LastUpdatedDate;datetime
tpInvoiceSaleReturnHistory;CreatedDate;datetime
tpInvoiceSaleReturnHistory;LastUpdatedDate;datetime
tpInvoiceSGKExtensions;CreatedDate;datetime
tpInvoiceSGKExtensions;EndDate;date
tpInvoiceSGKExtensions;LastUpdatedDate;datetime
tpInvoiceSGKExtensions;StartDate;date
tpInvoiceSourceInfo;CreatedDate;datetime
tpInvoiceSourceInfo;LastUpdatedDate;datetime
tpInvoiceTransportModeDetail;CreatedDate;datetime
tpInvoiceTransportModeDetail;LastUpdatedDate;datetime
tpInvoiceUBLExtensions;CreatedDate;datetime
tpInvoiceUBLExtensions;LastUpdatedDate;datetime
tpInvoiceUnAcceptableExpenseLine;CreatedDate;datetime
tpInvoiceUnAcceptableExpenseLine;LastUpdatedDate;datetime
tpJournalATAttribute;CreatedDate;datetime
tpJournalATAttribute;LastUpdatedDate;datetime
tpJournalFTAttribute;CreatedDate;datetime
tpJournalFTAttribute;LastUpdatedDate;datetime
tpJournalIntegrationStatus;CreatedDate;datetime
tpJournalIntegrationStatus;LastUpdatedDate;datetime
tpJournalLineExtension;CreatedDate;datetime
tpJournalLineExtension;LastUpdatedDate;datetime
tpJournalTaxIncurred;CreatedDate;datetime
tpJournalTaxIncurred;LastUpdatedDate;datetime
tpJournalZNum;CreatedDate;datetime
tpJournalZNum;InvoiceDate;date
tpJournalZNum;LastUpdatedDate;datetime
tpJournalZNumDetail;CreatedDate;datetime
tpJournalZNumDetail;LastUpdatedDate;datetime
tpOnlineBankPosPaymentList;CreatedDate;datetime
tpOnlineBankPosPaymentList;LastUpdatedDate;datetime
tpOnlineInstallmentBankPayment;CreatedDate;datetime
tpOnlineInstallmentBankPayment;LastUpdatedDate;datetime
tpOrderATAttribute;CreatedDate;datetime
tpOrderATAttribute;LastUpdatedDate;datetime
tpOrderCancelDetail;CancelDate;smalldatetime
tpOrderCancelDetail;CreatedDate;datetime
tpOrderCancelDetail;LastUpdatedDate;datetime
tpOrderCancelDetailHeader;CreatedDate;datetime
tpOrderCancelDetailHeader;LastUpdatedDate;datetime
tpOrderCanceled;CreatedDate;datetime
tpOrderCanceled;LastUpdatedDate;datetime
tpOrderCancelReturnTransactions;CreatedDate;datetime
tpOrderCancelReturnTransactions;LastUpdatedDate;datetime
tpOrderCashRegisterInfo;CreatedDate;datetime
tpOrderCashRegisterInfo;DocumentDate;date
tpOrderCashRegisterInfo;LastUpdatedDate;datetime
tpOrderContractContext;CreatedDate;datetime
tpOrderContractContext;LastUpdatedDate;datetime
tpOrderDeliveryDetail;CreatedDate;datetime
tpOrderDeliveryDetail;LastUpdatedDate;datetime
tpOrderDeliveryDetail;OrderDeliveryDate;smalldatetime
tpOrderDiscountOffer;CreatedDate;datetime
tpOrderDiscountOffer;LastUpdatedDate;datetime
tpOrderDiscountOfferContributor;CreatedDate;datetime
tpOrderDiscountOfferContributor;LastUpdatedDate;datetime
tpOrderDistanceSalesCorrelations;CreatedDate;datetime
tpOrderDistanceSalesCorrelations;LastUpdatedDate;datetime
tpOrderDistanceSalesCorrelations;OperationDate;datetime
tpOrderDistanceSalesSMS;CreatedDate;datetime
tpOrderDistanceSalesSMS;LastUpdatedDate;datetime
tpOrderDistanceSalesSMS;LinkExpireDate;datetime
tpOrderFTAttribute;CreatedDate;datetime
tpOrderFTAttribute;LastUpdatedDate;datetime
tpOrderHeaderExtension;CreatedDate;datetime
tpOrderHeaderExtension;LastUpdatedDate;datetime
tpOrderITAttribute;CreatedDate;datetime
tpOrderITAttribute;LastUpdatedDate;datetime
tpOrderLineExtension;CreatedDate;datetime
tpOrderLineExtension;DeliveryAssignDate;date
tpOrderLineExtension;LastUpdatedDate;datetime
tpOrderLineSerialNumber;CreatedDate;datetime
tpOrderLineSerialNumber;LastUpdatedDate;datetime
tpOrderOpticalProductCustomProcess;CreatedDate;datetime
tpOrderOpticalProductCustomProcess;LastUpdatedDate;datetime
tpOrderOTAttribute;CreatedDate;datetime
tpOrderOTAttribute;LastUpdatedDate;datetime
tpOrderPostalAddress;CreatedDate;datetime
tpOrderPostalAddress;LastUpdatedDate;datetime
tpOrdersViaInternetInfo;CreatedDate;datetime
tpOrdersViaInternetInfo;LastUpdatedDate;datetime
tpOrdersViaInternetInfo;PaymentDate;date
tpOrdersViaInternetInfo;SendDate;date
tpOtherPaymentATAttribute;CreatedDate;datetime
tpOtherPaymentATAttribute;LastUpdatedDate;datetime
tpOtherPaymentFTAttribute;CreatedDate;datetime
tpOtherPaymentFTAttribute;LastUpdatedDate;datetime
tpOutStockDeclarationInfo;CreatedDate;datetime
tpOutStockDeclarationInfo;DeclarationDate;date
tpOutStockDeclarationInfo;LastUpdatedDate;datetime
tpPaymentATAttribute;CreatedDate;datetime
tpPaymentATAttribute;LastUpdatedDate;datetime
tpPaymentBadDebtLawyer;CreatedDate;datetime
tpPaymentBadDebtLawyer;LastUpdatedDate;datetime
tpPaymentBulutTahsilatMapping;CreatedDate;datetime
tpPaymentBulutTahsilatMapping;LastUpdatedDate;datetime
tpPaymentFTAttribute;CreatedDate;datetime
tpPaymentFTAttribute;LastUpdatedDate;datetime
tpPaymentPaynetMapping;CreatedDate;datetime
tpPaymentPaynetMapping;LastUpdatedDate;datetime
tpPaymentRegisterInfo;CreatedDate;datetime
tpPaymentRegisterInfo;DocumentDate;date
tpPaymentRegisterInfo;LastUpdatedDate;datetime
tpPaymentReturn;CreatedDate;datetime
tpPaymentReturn;LastUpdatedDate;datetime
tpPaynetCreditCardPayment;CreatedDate;datetime
tpPaynetCreditCardPayment;LastUpdatedDate;datetime
tpPickingFromSectionTransfer;CreatedDate;datetime
tpPickingFromSectionTransfer;LastUpdatedDate;datetime
tpProposalATAttribute;CreatedDate;datetime
tpProposalATAttribute;LastUpdatedDate;datetime
tpProposalDiscountOffer;CreatedDate;datetime
tpProposalDiscountOffer;LastUpdatedDate;datetime
tpProposalDiscountOfferContributor;CreatedDate;datetime
tpProposalDiscountOfferContributor;LastUpdatedDate;datetime
tpProposalFTAttribute;CreatedDate;datetime
tpProposalFTAttribute;LastUpdatedDate;datetime
tpProposalITAttribute;CreatedDate;datetime
tpProposalITAttribute;LastUpdatedDate;datetime
tpProposalLineConfirmation;ConfirmedDate;smalldatetime
tpProposalLineConfirmation;CreatedDate;datetime
tpProposalLineConfirmation;LastUpdatedDate;datetime
tpProposalLineConfirmationStatus;CreatedDate;datetime
tpProposalLineConfirmationStatus;LastUpdatedDate;datetime
tpProposalLineRevision;CreatedDate;datetime
tpProposalLineRevision;LastUpdatedDate;datetime
tpPurchaseRequisitionATAttribute;CreatedDate;datetime
tpPurchaseRequisitionATAttribute;LastUpdatedDate;datetime
tpPurchaseRequisitionClosedByInventory;CreatedDate;datetime
tpPurchaseRequisitionClosedByInventory;LastUpdatedDate;datetime
tpPurchaseRequisitionConfirmation;ConfirmedDate;smalldatetime
tpPurchaseRequisitionConfirmation;CreatedDate;datetime
tpPurchaseRequisitionConfirmation;LastUpdatedDate;datetime
tpPurchaseRequisitionItemAttributeInfo;CreatedDate;datetime
tpPurchaseRequisitionItemAttributeInfo;LastUpdatedDate;datetime
tpPurchaseRequisitionItemInfo;CreatedDate;datetime
tpPurchaseRequisitionItemInfo;LastUpdatedDate;datetime
tpPurchaseRequisitionProposal;AverageDueDate;date
tpPurchaseRequisitionProposal;ClosedDate;smalldatetime
tpPurchaseRequisitionProposal;CreatedDate;datetime
tpPurchaseRequisitionProposal;DeliveryDate;smalldatetime
tpPurchaseRequisitionProposal;ExpiredDate;date
tpPurchaseRequisitionProposal;LastUpdatedDate;datetime
tpPurchaseRequisitionProposal;ProposalDate;date
tpPurchaseRequisitionProposalATAttribute;CreatedDate;datetime
tpPurchaseRequisitionProposalATAttribute;LastUpdatedDate;datetime
tpPurchaseRequisitionProposalConfirmation;ConfirmedDate;smalldatetime
tpPurchaseRequisitionProposalConfirmation;CreatedDate;datetime
tpPurchaseRequisitionProposalConfirmation;LastUpdatedDate;datetime
tpPurchaseRequisitionProposalRevision;CreatedDate;datetime
tpPurchaseRequisitionProposalRevision;LastUpdatedDate;datetime
tpPurchaseRequisitionReceiveInfo;CreatedDate;datetime
tpPurchaseRequisitionReceiveInfo;LastUpdatedDate;datetime
tpPurchaseRequisitionReceiveInfo;ReceivedDate;smalldatetime
tpPurchaseRequisitionRevision;CreatedDate;datetime
tpPurchaseRequisitionRevision;LastUpdatedDate;datetime
tpPurchaseRequisitionTechnicalNotes;CreatedDate;datetime
tpPurchaseRequisitionTechnicalNotes;LastUpdatedDate;datetime
tpPurchaseRequisitionTechnicalNotes;OperationDate;smalldatetime
tpPurchaseRequisitionTrace;CreatedDate;datetime
tpPurchaseRequisitionTrace;LastUpdatedDate;datetime
tpPurchaseRequisitionTrace;OperationDate;datetime
tpSalesViaInternetInfo;CreatedDate;datetime
tpSalesViaInternetInfo;LastUpdatedDate;datetime
tpSalesViaInternetInfo;PaymentDate;date
tpSalesViaInternetInfo;SendDate;date
tpShipmentHeaderExtension;CreatedDate;datetime
tpShipmentHeaderExtension;LastUpdatedDate;datetime
tpShipmentITAttribute;CreatedDate;datetime
tpShipmentITAttribute;LastUpdatedDate;datetime
tpShipmentLinePickingDetails;CreatedDate;datetime
tpShipmentLinePickingDetails;LastUpdatedDate;datetime
tpShipmentLinePickingDetails;PickingDate;date
tpShipmentReturn;CreatedDate;datetime
tpShipmentReturn;LastUpdatedDate;datetime
tpShipmentTransportModeDetail;CreatedDate;datetime
tpShipmentTransportModeDetail;LastUpdatedDate;datetime
tpShipmentUBLExtensions;CreatedDate;datetime
tpShipmentUBLExtensions;LastUpdatedDate;datetime
tpShipmentVehicleDrivers;CreatedDate;datetime
tpShipmentVehicleDrivers;LastUpdatedDate;datetime
tpSMSPoolLineExtension;CreatedDate;datetime
tpSMSPoolLineExtension;LastUpdatedDate;datetime
tpStockCross;CreatedDate;datetime
tpStockCross;LastUpdatedDate;datetime
tpStockITAttribute;CreatedDate;datetime
tpStockITAttribute;LastUpdatedDate;datetime
tpSupportRequestConfirmation;ConfirmationDate;date
tpSupportRequestConfirmation;CreatedDate;datetime
tpSupportRequestConfirmation;LastUpdatedDate;datetime
tpSupportRequestConfirmation;OperationDate;date
tpSupportRequestConfirmation;RejectDate;date
tpSupportRequestDecisionLetter;CreatedDate;datetime
tpSupportRequestDecisionLetter;LastUpdatedDate;datetime
tpSupportRequestInformation;CreatedDate;datetime
tpSupportRequestInformation;LastUpdatedDate;datetime
tpSupportResolve;CreatedDate;datetime
tpSupportResolve;LastUpdatedDate;datetime
tpSupportResolve;OperationDate;date
tpSupportResolveMaterial;CreatedDate;datetime
tpSupportResolveMaterial;LastUpdatedDate;datetime
tpSupportStatusHistory;CreatedDate;datetime
tpSupportStatusHistory;LastUpdatedDate;datetime
tpSupportStatusHistory;OperationDate;smalldatetime
tpTransferPlanATAttribute;CreatedDate;datetime
tpTransferPlanATAttribute;LastUpdatedDate;datetime
tpTransferPlanITAttribute;CreatedDate;datetime
tpTransferPlanITAttribute;LastUpdatedDate;datetime
tpTsmCashRegisterInfo;CreatedDate;datetime
tpTsmCashRegisterInfo;DocumentDate;date
tpTsmCashRegisterInfo;LastUpdatedDate;datetime
tpVehicleLoadingDriver;CreatedDate;datetime
tpVehicleLoadingDriver;LastUpdatedDate;datetime
tpVehicleLoadingLineDeliveryStatus;CreatedDate;datetime
tpVehicleLoadingLineDeliveryStatus;LastUpdatedDate;datetime
tpVehicleLoadingRoundsman;CreatedDate;datetime
tpVehicleLoadingRoundsman;LastUpdatedDate;datetime
tpVirementATAttribute;CreatedDate;datetime
tpVirementATAttribute;LastUpdatedDate;datetime
tpVirementFTAttribute;CreatedDate;datetime
tpVirementFTAttribute;LastUpdatedDate;datetime
trAdjustCostBankLine;CreatedDate;datetime
trAdjustCostBankLine;LastUpdatedDate;datetime
trAdjustCostExpenseInvoiceLine;CreatedDate;datetime
trAdjustCostExpenseInvoiceLine;LastUpdatedDate;datetime
trAdjustCostExpenseSlipLine;CreatedDate;datetime
trAdjustCostExpenseSlipLine;LastUpdatedDate;datetime
trAdjustCostHeader;CreatedDate;datetime
trAdjustCostHeader;JournalDate;date
trAdjustCostHeader;LastUpdatedDate;datetime
trAdjustCostHeader;OperationDate;date
trAdjustCostInner;CreatedDate;datetime
trAdjustCostInner;LastUpdatedDate;datetime
trAdjustCostInnerLine;CreatedDate;datetime
trAdjustCostInnerLine;LastUpdatedDate;datetime
trAdjustCostInventory;CreatedDate;datetime
trAdjustCostInventory;LastUpdatedDate;datetime
trAdjustCostInventoryLine;CreatedDate;datetime
trAdjustCostInventoryLine;LastUpdatedDate;datetime
trAdjustCostInvoice;CreatedDate;datetime
trAdjustCostInvoice;LastUpdatedDate;datetime
trAdjustCostInvoiceLine;CreatedDate;datetime
trAdjustCostInvoiceLine;LastUpdatedDate;datetime
trAdjustCostOrder;CreatedDate;datetime
trAdjustCostOrder;LastUpdatedDate;datetime
trAdjustCostOrderLine;CreatedDate;datetime
trAdjustCostOrderLine;LastUpdatedDate;datetime
trAgentContractDeservedLine;CreatedDate;datetime
trAgentContractDeservedLine;LastUpdatedDate;datetime
trAgentContractHeader;CreatedDate;datetime
trAgentContractHeader;FirstDate;date
trAgentContractHeader;LastDate;date
trAgentContractHeader;LastUpdatedDate;datetime
trAgentContractPeriodicalLine;CreatedDate;datetime
trAgentContractPeriodicalLine;LastUpdatedDate;datetime
trAgentContractSpecialLine;CreatedDate;datetime
trAgentContractSpecialLine;LastUpdatedDate;datetime
trAgentContractStandartLine;CreatedDate;datetime
trAgentContractStandartLine;LastUpdatedDate;datetime
trAgentContractVehicle;CreatedDate;datetime
trAgentContractVehicle;LastUpdatedDate;datetime
trAgentContractVisitFrequencyLine;CreatedDate;datetime
trAgentContractVisitFrequencyLine;LastUpdatedDate;datetime
trAgentPerformanceBonusHeader;CreatedDate;datetime
trAgentPerformanceBonusHeader;FirstDate;date
trAgentPerformanceBonusHeader;LastDate;date
trAgentPerformanceBonusHeader;LastUpdatedDate;datetime
trAgentPerformanceBonusLine;CreatedDate;datetime
trAgentPerformanceBonusLine;LastUpdatedDate;datetime
trAgentReservationHeader;CreatedDate;datetime
trAgentReservationHeader;LastUpdatedDate;datetime
trAgentReservationHeader;OperationDate;date
trAgentReservationSalesPerson;CreatedDate;datetime
trAgentReservationSalesPerson;LastUpdatedDate;datetime
trAgentReservationVehicleDetail;CreatedDate;datetime
trAgentReservationVehicleDetail;LastUpdatedDate;datetime
trAllocation;ConfirmedDate;datetime
trAllocation;CreatedDate;datetime
trAllocation;LastUpdatedDate;datetime
trAllocation;OperationDate;date
trAllocationChannel;CreatedDate;datetime
trAllocationChannel;LastUpdatedDate;datetime
trAllocationData_DefR1;CreatedDate;datetime
trAllocationData_DefR1;FirstIncomingDate;date
trAllocationData_DefR1;LastIncomingDate;date
trAllocationData_DefR1;LastUpdatedDate;datetime
trAllocationData_DefR2;CreatedDate;datetime
trAllocationData_DefR2;LastUpdatedDate;datetime
trAllocationData_M;CreatedDate;datetime
trAllocationData_M;LastUpdatedDate;datetime
trAllocationParameterValue;CreatedDate;datetime
trAllocationParameterValue;LastUpdatedDate;datetime
trAllocationProduct;CreatedDate;datetime
trAllocationProduct;LastUpdatedDate;datetime
trAllocationProductQty;CreatedDate;datetime
trAllocationProductQty;LastUpdatedDate;datetime
trBadDebtLetter;CreatedDate;datetime
trBadDebtLetter;LastUpdatedDate;datetime
trBadDebtLetterPrint;CreatedDate;datetime
trBadDebtLetterPrint;LastUpdatedDate;datetime
trBadDebtTransAddExpenseDebits;CreatedDate;datetime
trBadDebtTransAddExpenseDebits;LastUpdatedDate;datetime
trBadDebtTransHeader;CreatedDate;datetime
trBadDebtTransHeader;LastUpdatedDate;datetime
trBadDebtTransHeader;OperationDate;date
trBadDebtTransLine;CreatedDate;datetime
trBadDebtTransLine;DocumentDate;date
trBadDebtTransLine;JournalDate;date
trBadDebtTransLine;LastUpdatedDate;datetime
trBadDebtTransLineAddExpense;CreatedDate;datetime
trBadDebtTransLineAddExpense;LastUpdatedDate;datetime
trBadDebtTransLineAddExpense;OperationDate;date
trBadDebtTransLineInstalment;CreatedDate;datetime
trBadDebtTransLineInstalment;DueDate;date
trBadDebtTransLineInstalment;LastUpdatedDate;datetime
trBadDebtTransLineResult;CreatedDate;datetime
trBadDebtTransLineResult;LastUpdatedDate;datetime
trBadDebtTransLineResult;ResultDate;date
trBankCreditHeader;ClosedDate;smalldatetime
trBankCreditHeader;ConfirmedDate;datetime
trBankCreditHeader;CreatedDate;datetime
trBankCreditHeader;DocumentDate;date
trBankCreditHeader;JournalDate;date
trBankCreditHeader;LastUpdatedDate;datetime
trBankCreditLine;CreatedDate;datetime
trBankCreditLine;DueDate;date
trBankCreditLine;JournalDate;date
trBankCreditLine;LastUpdatedDate;datetime
trBankCreditLineCurrency;CreatedDate;datetime
trBankCreditLineCurrency;LastUpdatedDate;datetime
trBankCreditPaymentPlan;CreatedDate;datetime
trBankCreditPaymentPlan;LastUpdatedDate;datetime
trBankCreditPaymentPlan;PaymentDate;date
trBankHeader;CreatedDate;datetime
trBankHeader;DocumentDate;date
trBankHeader;JournalDate;date
trBankHeader;LastUpdatedDate;datetime
trBankLine;CreatedDate;datetime
trBankLine;DueDate;date
trBankLine;LastUpdatedDate;datetime
trBankLineAdditionalCharge;CreatedDate;datetime
trBankLineAdditionalCharge;LastUpdatedDate;datetime
trBankLineCostCenterRates;CreatedDate;datetime
trBankLineCostCenterRates;LastUpdatedDate;datetime
trBankLineCurrency;CreatedDate;datetime
trBankLineCurrency;LastUpdatedDate;datetime
trBankPaymentInstructionHeader;ConfirmedDate;datetime
trBankPaymentInstructionHeader;CreatedDate;datetime
trBankPaymentInstructionHeader;DocumentDate;date
trBankPaymentInstructionHeader;LastUpdatedDate;datetime
trBankPaymentInstructionLine;CreatedDate;datetime
trBankPaymentInstructionLine;DueDate;date
trBankPaymentInstructionLine;LastUpdatedDate;datetime
trBankPaymentListHeader;CreatedDate;datetime
trBankPaymentListHeader;DocumentDate;date
trBankPaymentListHeader;LastUpdatedDate;datetime
trBankPaymentListLine;CancelDate;date
trBankPaymentListLine;ConfirmedDate;date
trBankPaymentListLine;CreatedDate;datetime
trBankPaymentListLine;DueDate;date
trBankPaymentListLine;LastUpdatedDate;datetime
trBankPaymentListLine;PaymentDate;date
trBudget;CreatedDate;datetime
trBudget;LastUpdatedDate;datetime
trCashHeader;CreatedDate;datetime
trCashHeader;DocumentDate;date
trCashHeader;JournalDate;date
trCashHeader;LastUpdatedDate;datetime
trCashLine;CreatedDate;datetime
trCashLine;LastUpdatedDate;datetime
trCashLineCostCenterRates;CreatedDate;datetime
trCashLineCostCenterRates;LastUpdatedDate;datetime
trCashLineCurrency;CreatedDate;datetime
trCashLineCurrency;LastUpdatedDate;datetime
trChequeHeader;CreatedDate;datetime
trChequeHeader;DocumentDate;date
trChequeHeader;JournalDate;date
trChequeHeader;LastUpdatedDate;datetime
trChequeLine;CreatedDate;datetime
trChequeLine;LastUpdatedDate;datetime
trChequeLineCurrency;CreatedDate;datetime
trChequeLineCurrency;LastUpdatedDate;datetime
trContract;CancelDate;smalldatetime
trContract;ClosedDate;smalldatetime
trContract;CreatedDate;datetime
trContract;DocumentDate;date
trContract;EndDate;date
trContract;LastUpdatedDate;datetime
trContract;StartDate;date
trContractProduct;CreatedDate;datetime
trContractProduct;LastUpdatedDate;datetime
trCostCenterDistributions;CreatedDate;datetime
trCostCenterDistributions;LastUpdatedDate;datetime
trCostOfGoodsSoldHeader;CreatedDate;datetime
trCostOfGoodsSoldHeader;JournalDate;date
trCostOfGoodsSoldHeader;LastUpdatedDate;datetime
trCostOfGoodsSoldLine;CreatedDate;datetime
trCostOfGoodsSoldLine;LastUpdatedDate;datetime
trCountrySpecialDay;CreatedDate;datetime
trCountrySpecialDay;LastUpdatedDate;datetime
trCountrySpecialDay;SpecialDayDate;date
trCreditCardPaymentHeader;CreatedDate;datetime
trCreditCardPaymentHeader;JournalDate;date
trCreditCardPaymentHeader;LastUpdatedDate;datetime
trCreditCardPaymentHeader;PaymentDate;date
trCreditCardPaymentLine;CreatedDate;datetime
trCreditCardPaymentLine;InstallmentStartDate;date
trCreditCardPaymentLine;LastUpdatedDate;datetime
trCreditCardPaymentLineCurrency;CreatedDate;datetime
trCreditCardPaymentLineCurrency;LastUpdatedDate;datetime
trCurrAccBook;CreatedDate;datetime
trCurrAccBook;DocumentDate;date
trCurrAccBook;DueDate;date
trCurrAccBook;LastUpdatedDate;datetime
trCurrAccBookCurrency;CreatedDate;datetime
trCurrAccBookCurrency;LastUpdatedDate;datetime
trCurrAccReconciliationEMailNotification;CreatedDate;datetime
trCurrAccReconciliationEMailNotification;EMailLinkExpireDate;datetime
trCurrAccReconciliationEMailNotification;EmailSendDate;datetime
trCurrAccReconciliationEMailNotification;LastUpdatedDate;datetime
trCurrAccReconciliationReport;ClosedDate;smalldatetime
trCurrAccReconciliationReport;ConfirmedDate;datetime
trCurrAccReconciliationReport;CreatedDate;datetime
trCurrAccReconciliationReport;EndDate;datetime
trCurrAccReconciliationReport;LastUpdatedDate;datetime
trCurrAccReconciliationReport;OperationDate;datetime
trCurrAccReconciliationReport;RejectDate;datetime
trCurrAccReconciliationReport;StartDate;datetime
trCurrAccReconciliationReportConfirmation;ConfirmedDate;datetime
trCurrAccReconciliationReportConfirmation;CreatedDate;datetime
trCurrAccReconciliationReportConfirmation;LastUpdatedDate;datetime
trCurrAccReconciliationReportConfirmation;RejectDate;datetime
trDebitHeader;CreatedDate;datetime
trDebitHeader;DocumentDate;date
trDebitHeader;JournalDate;date
trDebitHeader;LastUpdatedDate;datetime
trDebitLine;CreatedDate;datetime
trDebitLine;DueDate;date
trDebitLine;LastUpdatedDate;datetime
trDebitLineCurrency;CreatedDate;datetime
trDebitLineCurrency;LastUpdatedDate;datetime
trDepartmentReceiptHeader;CreatedDate;datetime
trDepartmentReceiptHeader;DocumentDate;date
trDepartmentReceiptHeader;LastUpdatedDate;datetime
trDepartmentReceiptLine;CreatedDate;datetime
trDepartmentReceiptLine;LastUpdatedDate;datetime
trDispOrderHeader;ConfirmedDate;datetime
trDispOrderHeader;CreatedDate;datetime
trDispOrderHeader;DispOrderDate;date
trDispOrderHeader;LastUpdatedDate;datetime
trDispOrderHeader;ShippingDate;date
trDispOrderLine;CreatedDate;datetime
trDispOrderLine;LastUpdatedDate;datetime
trDispOrderLine;OrderDeliveryDate;smalldatetime
trEmployeeDebit;CreatedDate;datetime
trEmployeeDebit;DocumentDate;date
trEmployeeDebit;DueDate;date
trEmployeeDebit;JournalDate;date
trEmployeeDebit;LastUpdatedDate;datetime
trEmployeeDebitCurrency;CreatedDate;datetime
trEmployeeDebitCurrency;LastUpdatedDate;datetime
trEndOfPeriodInventory;CreatedDate;datetime
trEndOfPeriodInventory;LastUpdatedDate;datetime
trEndOfPeriodInventory;OperationDate;date
trExchangeRateHeader;CreatedDate;datetime
trExchangeRateHeader;ExchangeDate;date
trExchangeRateHeader;LastUpdatedDate;datetime
trExchangeRateLine;CreatedDate;datetime
trExchangeRateLine;LastUpdatedDate;datetime
trExpenseAccrualHeader;CreatedDate;datetime
trExpenseAccrualHeader;DocumentDate;date
trExpenseAccrualHeader;EndDate;date
trExpenseAccrualHeader;LastUpdatedDate;datetime
trExpenseAccrualHeader;StartDate;date
trExpenseAccrualInflationAdjustmentLine;CreatedDate;datetime
trExpenseAccrualInflationAdjustmentLine;JournalDate;date
trExpenseAccrualInflationAdjustmentLine;LastUpdatedDate;datetime
trExpenseAccrualLine;CreatedDate;datetime
trExpenseAccrualLine;DueDate;date
trExpenseAccrualLine;JournalDate;date
trExpenseAccrualLine;LastUpdatedDate;datetime
trExpenseAccrualLineCostCenterRates;CreatedDate;datetime
trExpenseAccrualLineCostCenterRates;LastUpdatedDate;datetime
trExpenseAccrualLineCurrency;CreatedDate;datetime
trExpenseAccrualLineCurrency;LastUpdatedDate;datetime
trExpenseSlipHeader;AverageDueDate;date
trExpenseSlipHeader;CreatedDate;datetime
trExpenseSlipHeader;DocumentDate;date
trExpenseSlipHeader;JournalDate;date
trExpenseSlipHeader;LastUpdatedDate;datetime
trExpenseSlipLine;CreatedDate;datetime
trExpenseSlipLine;DocumentDate;date
trExpenseSlipLine;DueDate;date
trExpenseSlipLine;LastUpdatedDate;datetime
trExpenseSlipLineCostCenterRates;CreatedDate;datetime
trExpenseSlipLineCostCenterRates;LastUpdatedDate;datetime
trExpenseSlipLineCurrency;CreatedDate;datetime
trExpenseSlipLineCurrency;LastUpdatedDate;datetime
trFixedAssetBookHeader;CreatedDate;datetime
trFixedAssetBookHeader;LastUpdatedDate;datetime
trFixedAssetBookLine;CreatedDate;datetime
trFixedAssetBookLine;JournalDate;date
trFixedAssetBookLine;LastUpdatedDate;datetime
trFixedAssetBookLinePeriodDepreciationExpenseDetail;CreatedDate;datetime
trFixedAssetBookLinePeriodDepreciationExpenseDetail;LastUpdatedDate;datetime
trForthcomingItems;CreatedDate;datetime
trForthcomingItems;LastUpdatedDate;datetime
trForthcomingItems;TransactionDate;date
trForthcomingOrders;CreatedDate;datetime
trForthcomingOrders;LastUpdatedDate;datetime
trForthcomingOrders;TransactionDate;date
trGiftCardPaymentHeader;CreatedDate;datetime
trGiftCardPaymentHeader;JournalDate;date
trGiftCardPaymentHeader;LastUpdatedDate;datetime
trGiftCardPaymentHeader;PaymentDate;date
trGiftCardPaymentLine;CreatedDate;datetime
trGiftCardPaymentLine;LastUpdatedDate;datetime
trGiftCardPaymentLineCurrency;CreatedDate;datetime
trGiftCardPaymentLineCurrency;LastUpdatedDate;datetime
trIncentiveHeader;ConfirmedDate;datetime
trIncentiveHeader;CreatedDate;datetime
trIncentiveHeader;LastUpdatedDate;datetime
trIncentiveHeader;OperationDate;date
trIncentiveLine;CreatedDate;datetime
trIncentiveLine;LastUpdatedDate;datetime
trInnerHeader;CreatedDate;datetime
trInnerHeader;JournalDate;date
trInnerHeader;LastUpdatedDate;datetime
trInnerHeader;OperationDate;date
trInnerHeader;TransferApprovedDate;date
trInnerLine;CreatedDate;datetime
trInnerLine;ExpiryDate;date
trInnerLine;LastUpdatedDate;datetime
trInnerLine;ManufactureDate;date
trInnerLineBOM;CreatedDate;datetime
trInnerLineBOM;LastUpdatedDate;datetime
trInnerLineCostCenterRates;CreatedDate;datetime
trInnerLineCostCenterRates;LastUpdatedDate;datetime
trInnerLineGiftCard;CreatedDate;datetime
trInnerLineGiftCard;LastUpdatedDate;datetime
trInnerLineInventoryTransfer;CreatedDate;datetime
trInnerLineInventoryTransfer;LastUpdatedDate;datetime
trInnerLineSum;CreatedDate;datetime
trInnerLineSum;LastUpdatedDate;datetime
trInnerLineSumDetail;CreatedDate;datetime
trInnerLineSumDetail;LastUpdatedDate;datetime
trInnerOrderHeader;CreatedDate;datetime
trInnerOrderHeader;InnerOrderDate;date
trInnerOrderHeader;LastUpdatedDate;datetime
trInnerOrderLine;CancelDate;smalldatetime
trInnerOrderLine;ClosedDate;smalldatetime
trInnerOrderLine;CreatedDate;datetime
trInnerOrderLine;DeliveryDate;smalldatetime
trInnerOrderLine;LastUpdatedDate;datetime
trInnerOrderLineSum;CreatedDate;datetime
trInnerOrderLineSum;LastUpdatedDate;datetime
trInnerOrderLineSumDetail;CreatedDate;datetime
trInnerOrderLineSumDetail;LastUpdatedDate;datetime
trInvoiceHeader;AverageDueDate;date
trInvoiceHeader;CreatedDate;datetime
trInvoiceHeader;InvoiceDate;date
trInvoiceHeader;JournalDate;date
trInvoiceHeader;LastUpdatedDate;datetime
trInvoiceHeader;OperationDate;date
trInvoiceLine;CreatedDate;datetime
trInvoiceLine;DeliveryDate;smalldatetime
trInvoiceLine;ExpiryDate;date
trInvoiceLine;LastUpdatedDate;datetime
trInvoiceLine;ManufactureDate;date
trInvoiceLine;OrderDeliveryDate;smalldatetime
trInvoiceLine;PlannedDateOfLading;date
trInvoiceLineBOM;CreatedDate;datetime
trInvoiceLineBOM;LastUpdatedDate;datetime
trInvoiceLineCostCenterRates;CreatedDate;datetime
trInvoiceLineCostCenterRates;LastUpdatedDate;datetime
trInvoiceLineCurrency;CreatedDate;datetime
trInvoiceLineCurrency;LastUpdatedDate;datetime
trInvoiceLineGiftCard;CreatedDate;datetime
trInvoiceLineGiftCard;LastUpdatedDate;datetime
trInvoiceLineLinkedProduct;CreatedDate;datetime
trInvoiceLineLinkedProduct;LastUpdatedDate;datetime
trInvoiceLineReportedSales;CreatedDate;datetime
trInvoiceLineReportedSales;LastUpdatedDate;datetime
trInvoiceLineSubsequentDeliveryOrders;CreatedDate;datetime
trInvoiceLineSubsequentDeliveryOrders;LastUpdatedDate;datetime
trInvoiceLineSum;CreatedDate;datetime
trInvoiceLineSum;LastUpdatedDate;datetime
trInvoiceLineSumDetail;CreatedDate;datetime
trInvoiceLineSumDetail;LastUpdatedDate;datetime
trItemTestHeader;CreatedDate;datetime
trItemTestHeader;LastUpdatedDate;datetime
trItemTestHeader;TestDate;date
trItemTestLine;CreatedDate;datetime
trItemTestLine;LastUpdatedDate;datetime
trItemTestLine;ResultDate;date
trJournalHeader;CreatedDate;datetime
trJournalHeader;JournalDate;date
trJournalHeader;LastUpdatedDate;datetime
trJournalInflationAdjustmentHeader;CreatedDate;datetime
trJournalInflationAdjustmentHeader;JournalDate;date
trJournalInflationAdjustmentHeader;LastUpdatedDate;datetime
trJournalInflationAdjustmentLine;CreatedDate;datetime
trJournalInflationAdjustmentLine;LastUpdatedDate;datetime
trJournalLedgerEntryNumber;CreatedDate;datetime
trJournalLedgerEntryNumber;LastUpdatedDate;datetime
trJournalLedgerEntryNumber;LedgerEntryDate;date
trJournalLine;CreatedDate;datetime
trJournalLine;DocumentDate;date
trJournalLine;DueDate;date
trJournalLine;LastUpdatedDate;datetime
trJournalLineCostCenterRates;CreatedDate;datetime
trJournalLineCostCenterRates;LastUpdatedDate;datetime
trJournalLineCurrency;CreatedDate;datetime
trJournalLineCurrency;LastUpdatedDate;datetime
trMessageBox;CreatedDate;date
trMessageBox;DeadLine;date
trMessageBox;ReadDate;date
trOrderAdvancePayments;CreatedDate;datetime
trOrderAdvancePayments;LastUpdatedDate;datetime
trOrderAdvancePayments;PaymentDate;date
trOrderAsnHeader;ActualDateOfArrival;date
trOrderAsnHeader;CreatedDate;datetime
trOrderAsnHeader;DateOfLading;date
trOrderAsnHeader;DateOfNationalization;date
trOrderAsnHeader;EstimatedDateOfArrival;date
trOrderAsnHeader;LastUpdatedDate;datetime
trOrderAsnHeader;OrderAsnDate;date
trOrderAsnLine;CreatedDate;datetime
trOrderAsnLine;LastUpdatedDate;datetime
trOrderAsnLine;OrderDeliveryDate;smalldatetime
trOrderAsnLineSum;CreatedDate;datetime
trOrderAsnLineSum;LastUpdatedDate;datetime
trOrderAsnLineSumDetail;CreatedDate;datetime
trOrderAsnLineSumDetail;LastUpdatedDate;datetime
trOrderAuditorSurvey;CreatedDate;datetime
trOrderAuditorSurvey;LastUpdatedDate;datetime
trOrderHeader;AverageDueDate;date
trOrderHeader;CreatedDate;datetime
trOrderHeader;CreditableConfirmedDate;datetime
trOrderHeader;LastUpdatedDate;datetime
trOrderHeader;OrderDate;date
trOrderLine;CancelDate;smalldatetime
trOrderLine;ClosedDate;smalldatetime
trOrderLine;CreatedDate;datetime
trOrderLine;DeliveryDate;smalldatetime
trOrderLine;LastUpdatedDate;datetime
trOrderLine;PlannedDateOfLading;date
trOrderLineBOM;CreatedDate;datetime
trOrderLineBOM;LastUpdatedDate;datetime
trOrderLineCurrency;CreatedDate;datetime
trOrderLineCurrency;LastUpdatedDate;datetime
trOrderLineLinkedProduct;CreatedDate;datetime
trOrderLineLinkedProduct;LastUpdatedDate;datetime
trOrderLineSum;CreatedDate;datetime
trOrderLineSum;LastUpdatedDate;datetime
trOrderLineSumDetail;CreatedDate;datetime
trOrderLineSumDetail;LastUpdatedDate;datetime
trOrderOpticalProduct;CreatedDate;datetime
trOrderOpticalProduct;LastUpdatedDate;datetime
trOrderOpticalProduct;PrescriptionDate;date
trOrderOpticalProduct;PrescriptionSpecifiedDate;date
trOrderOpticalProductLine;CreatedDate;datetime
trOrderOpticalProductLine;LastUpdatedDate;datetime
trOrderPaymentPlan;CreatedDate;datetime
trOrderPaymentPlan;DueDate;date
trOrderPaymentPlan;LastUpdatedDate;datetime
trOrderSurvey;CreatedDate;datetime
trOrderSurvey;LastUpdatedDate;datetime
trOtherPaymentHeader;CreatedDate;datetime
trOtherPaymentHeader;JournalDate;date
trOtherPaymentHeader;LastUpdatedDate;datetime
trOtherPaymentHeader;PaymentDate;date
trOtherPaymentLine;CreatedDate;datetime
trOtherPaymentLine;LastUpdatedDate;datetime
trOtherPaymentLineCurrency;CreatedDate;datetime
trOtherPaymentLineCurrency;LastUpdatedDate;datetime
trPaymentHeader;CreatedDate;datetime
trPaymentHeader;DocumentDate;date
trPaymentHeader;DueDate;date
trPaymentHeader;JournalDate;date
trPaymentHeader;LastUpdatedDate;datetime
trPaymentLine;CreatedDate;datetime
trPaymentLine;LastUpdatedDate;datetime
trPaymentLineCurrency;CreatedDate;datetime
trPaymentLineCurrency;LastUpdatedDate;datetime
trPayrollHeader;CreatedDate;datetime
trPayrollHeader;JournalDate;date
trPayrollHeader;LastUpdatedDate;datetime
trPayrollLine;CreatedDate;datetime
trPayrollLine;LastUpdatedDate;datetime
trPayrollLineDeduction;CreatedDate;datetime
trPayrollLineDeduction;LastUpdatedDate;datetime
trPayrollLineGarnishment;CreatedDate;datetime
trPayrollLineGarnishment;LastUpdatedDate;datetime
trPayrollLineTally;CreatedDate;datetime
trPayrollLineTally;LastUpdatedDate;datetime
trPayrollTerminationSeveranceDetail;CreatedDate;datetime
trPayrollTerminationSeveranceDetail;LastUpdatedDate;datetime
trPickingHeader;ConfirmedDate;datetime
trPickingHeader;CreatedDate;datetime
trPickingHeader;LastUpdatedDate;datetime
trPickingHeader;PickingDate;date
trPickingLine;CreatedDate;datetime
trPickingLine;LastUpdatedDate;datetime
trPickingLine;OrderDeliveryDate;smalldatetime
trPriceListHeader;ConfirmedDate;datetime
trPriceListHeader;CreatedDate;datetime
trPriceListHeader;LastUpdatedDate;datetime
trPriceListHeader;PriceListDate;date
trPriceListHeader;ValidDate;date
trPriceListLine;CreatedDate;datetime
trPriceListLine;DisableDate;date
trPriceListLine;LastUpdatedDate;datetime
trPriceListLine;ValidDate;date
trProposalHeader;AverageDueDate;date
trProposalHeader;CancelDate;smalldatetime
trProposalHeader;ConfirmedDate;datetime
trProposalHeader;CreatedDate;datetime
trProposalHeader;ExpiredDate;date
trProposalHeader;LastUpdatedDate;datetime
trProposalHeader;ProposalDate;date
trProposalLine;CreatedDate;datetime
trProposalLine;DeliveryDate;smalldatetime
trProposalLine;LastUpdatedDate;datetime
trProposalLine;PlannedDateOfLading;date
trProposalLineCurrency;CreatedDate;datetime
trProposalLineCurrency;LastUpdatedDate;datetime
trProposalLineSum;CreatedDate;datetime
trProposalLineSum;LastUpdatedDate;datetime
trProposalLineSumDetail;CreatedDate;datetime
trProposalLineSumDetail;LastUpdatedDate;datetime
trPurchaseRequisitionConfirmationEMailNotification;CreatedDate;datetime
trPurchaseRequisitionConfirmationEMailNotification;EMailSendDate;datetime
trPurchaseRequisitionConfirmationEMailNotification;LastUpdatedDate;datetime
trPurchaseRequisitionHeader;CreatedDate;datetime
trPurchaseRequisitionHeader;LastUpdatedDate;datetime
trPurchaseRequisitionHeader;PurchaseRequisitionDate;date
trPurchaseRequisitionLine;ClosedDate;smalldatetime
trPurchaseRequisitionLine;CreatedDate;datetime
trPurchaseRequisitionLine;LastUpdatedDate;datetime
trPurchaseRequisitionLine;NeedByDate;date
trPurchaseRequisitionProposalConfirmationEMailNotification;CreatedDate;datetime
trPurchaseRequisitionProposalConfirmationEMailNotification;EMailSendDate;datetime
trPurchaseRequisitionProposalConfirmationEMailNotification;LastUpdatedDate;datetime
trPurchaseRequisitionProposalConfirmationEMailNotificationDetail;CreatedDate;datetime
trPurchaseRequisitionProposalConfirmationEMailNotificationDetail;LastUpdatedDate;datetime
trReportedSaleHeader;CreatedDate;datetime
trReportedSaleHeader;LastUpdatedDate;datetime
trReportedSaleHeader;OperationDate;date
trReportedSaleLine;CreatedDate;datetime
trReportedSaleLine;LastUpdatedDate;datetime
trReserveHeader;CreatedDate;datetime
trReserveHeader;LastUpdatedDate;datetime
trReserveHeader;ReserveDate;date
trReserveLine;CreatedDate;datetime
trReserveLine;LastUpdatedDate;datetime
trReserveLine;OrderDeliveryDate;smalldatetime
trReserveTransfer;CreatedDate;datetime
trReserveTransfer;LastUpdatedDate;datetime
trReserveTransfer;OperationDate;date
trReturnedForthcomingItems;CreatedDate;datetime
trReturnedForthcomingItems;LastUpdatedDate;datetime
trReturnedForthcomingItems;TransactionDate;date
trSalesPlan;ConfirmedDate;datetime
trSalesPlan;CreatedDate;datetime
trSalesPlan;LastUpdatedDate;datetime
trSalesPlan;PlanDate;date
trSalesPlanChannel;CreatedDate;datetime
trSalesPlanChannel;LastUpdatedDate;datetime
trSalesPlanProduct;CreatedDate;datetime
trSalesPlanProduct;LastUpdatedDate;datetime
trSalesPlanProductQty;CreatedDate;datetime
trSalesPlanProductQty;LastUpdatedDate;datetime
trShipmentHeader;CreatedDate;datetime
trShipmentHeader;JournalDate;date
trShipmentHeader;LastUpdatedDate;datetime
trShipmentHeader;OperationDate;date
trShipmentHeader;ShippingDate;date
trShipmentHeader;TransferApprovedDate;date
trShipmentLine;CreatedDate;datetime
trShipmentLine;ExpiryDate;date
trShipmentLine;LastUpdatedDate;datetime
trShipmentLine;ManufactureDate;date
trShipmentLine;OrderDeliveryDate;smalldatetime
trShipmentLineBOM;CreatedDate;datetime
trShipmentLineBOM;LastUpdatedDate;datetime
trShipmentLineGiftCard;CreatedDate;datetime
trShipmentLineGiftCard;LastUpdatedDate;datetime
trShipmentLineSum;CreatedDate;datetime
trShipmentLineSum;LastUpdatedDate;datetime
trShipmentLineSumDetail;CreatedDate;datetime
trShipmentLineSumDetail;LastUpdatedDate;datetime
trSMSPoolHeader;CreatedDate;datetime
trSMSPoolHeader;LastUpdatedDate;datetime
trSMSPoolHeader;SendDate;datetime
trSMSPoolLine;CreatedDate;datetime
trSMSPoolLine;DeliveryDate;date
trSMSPoolLine;LastUpdatedDate;datetime
trSMSPoolMessage;CreatedDate;datetime
trSMSPoolMessage;LastUpdatedDate;datetime
trStock;CreatedDate;datetime
trStock;DocumentDate;date
trStock;ExpiryDate;date
trStock;LastUpdatedDate;datetime
trStock;ManufactureDate;date
trStock;OperationDate;date
trStoreVisitors;CreatedDate;datetime
trStoreVisitors;CurrentDate;date
trStoreVisitors;LastUpdatedDate;datetime
trSupportRequestHeader;ConfirmedDate;datetime
trSupportRequestHeader;CreatedDate;datetime
trSupportRequestHeader;DeliveryDate;date
trSupportRequestHeader;LastUpdatedDate;datetime
trSupportRequestHeader;RequestDate;date
trSupportRequestLine;CreatedDate;datetime
trSupportRequestLine;LastUpdatedDate;datetime
trSupportRequestSurvey;CreatedDate;datetime
trSupportRequestSurvey;LastUpdatedDate;datetime
trSurveyAnswerHeader;CreatedDate;datetime
trSurveyAnswerHeader;DocumentDate;date
trSurveyAnswerHeader;LastUpdatedDate;datetime
trSurveyAnswerLine;AnswerDate;date
trSurveyAnswerLine;CreatedDate;datetime
trSurveyAnswerLine;LastUpdatedDate;datetime
trTaxIncurredHeader;CreatedDate;datetime
trTaxIncurredHeader;DocumentDate;date
trTaxIncurredHeader;LastUpdatedDate;datetime
trTaxIncurredLine;CreatedDate;datetime
trTaxIncurredLine;LastUpdatedDate;datetime
trTFRSInvoiceAdjustment;CreatedDate;datetime
trTFRSInvoiceAdjustment;LastUpdatedDate;datetime
trTransferPlan;ConfirmedDate;datetime
trTransferPlan;CreatedDate;datetime
trTransferPlan;LastUpdatedDate;datetime
trTransferPlan;OperationDate;date
trTransferPlanChannel;CreatedDate;datetime
trTransferPlanChannel;LastUpdatedDate;datetime
trTransferPlanData_DefT1;CreatedDate;datetime
trTransferPlanData_DefT1;FirstIncomingDate;date
trTransferPlanData_DefT1;FirstIncomingDateAsColor;date
trTransferPlanData_DefT1;LastIncomingDate;date
trTransferPlanData_DefT1;LastIncomingDateAsColor;date
trTransferPlanData_DefT1;LastUpdatedDate;datetime
trTransferPlanData_DefT2;CreatedDate;datetime
trTransferPlanData_DefT2;LastIncomingDate;date
trTransferPlanData_DefT2;LastUpdatedDate;datetime
trTransferPlanData_DefT3;CreatedDate;datetime
trTransferPlanData_DefT3;LastUpdatedDate;datetime
trTransferPlanData_ITR;CreatedDate;datetime
trTransferPlanData_ITR;FirstIncomingDate;date
trTransferPlanData_ITR;LastIncomingDate;date
trTransferPlanData_ITR;LastUpdatedDate;datetime
trTransferPlanParameterValue;CreatedDate;datetime
trTransferPlanParameterValue;LastUpdatedDate;datetime
trTransferPlanProduct;CreatedDate;datetime
trTransferPlanProduct;LastUpdatedDate;datetime
trTransferPlanProductQty;CreatedDate;datetime
trTransferPlanProductQty;LastUpdatedDate;datetime
trVehicleLoadingHeader;CreatedDate;datetime
trVehicleLoadingHeader;LastUpdatedDate;datetime
trVehicleLoadingHeader;VehicleLoadingDate;date
trVehicleLoadingLine;CreatedDate;datetime
trVehicleLoadingLine;LastUpdatedDate;datetime
trVehicleUnLoadingHeader;CreatedDate;datetime
trVehicleUnLoadingHeader;LastUpdatedDate;datetime
trVehicleUnLoadingHeader;VehicleUnLoadingDate;date
trVehicleUnLoadingLine;CreatedDate;datetime
trVehicleUnLoadingLine;LastUpdatedDate;datetime
trVendorPriceListHeader;ConfirmedDate;datetime
trVendorPriceListHeader;CreatedDate;datetime
trVendorPriceListHeader;LastUpdatedDate;datetime
trVendorPriceListHeader;ValidDate;date
trVendorPriceListHeader;VendorPriceListDate;date
trVendorPriceListLine;CreatedDate;datetime
trVendorPriceListLine;DisableDate;date
trVendorPriceListLine;LastUpdatedDate;datetime
trVirementHeader;CreatedDate;datetime
trVirementHeader;DocumentDate;date
trVirementHeader;JournalDate;date
trVirementHeader;LastUpdatedDate;datetime
trVirementLine;CreatedDate;datetime
trVirementLine;LastUpdatedDate;datetime
zpBasefyCheckOutCancellation;CreatedDate;datetime
zpBasefyCheckOutCancellation;LastUpdatedDate;datetime
zpBasefyCheckOutNew;CreatedDate;datetime
zpBasefyCheckOutNew;LastUpdatedDate;datetime
zpBasefyCheckOutRefundOrder;CreatedDate;datetime
zpBasefyCheckOutRefundOrder;LastUpdatedDate;datetime
zpBasefyCheckOutUsePoint;CreatedDate;datetime
zpBasefyCheckOutUsePoint;LastUpdatedDate;datetime
zpBasefyUserSearch;CreatedDate;datetime
zpBasefyUserSearch;LastUpdatedDate;datetime
zpBulutTahsilatCreditCardPaymentEventLog;CreatedDate;datetime
zpBulutTahsilatCreditCardPaymentEventLog;LastUpdatedDate;datetime
zpBulutTahsilatCreditCardPaymentEventLog;OperationDate;datetime
zpBulutTahsilatCreditCardVPOSCancelPaymentList;CreatedDate;datetime
zpBulutTahsilatCreditCardVPOSCancelPaymentList;LastUpdatedDate;datetime
zpBulutTahsilatCreditCardVPOSCancelPaymentList;TransactionDate;datetime
zpBulutTahsilatCreditCardVPOSPaymentList;CreatedDate;datetime
zpBulutTahsilatCreditCardVPOSPaymentList;LastUpdatedDate;datetime
zpBulutTahsilatCreditCardVPOSPaymentList;TransactionDate;datetime
zpBulutTahsilatCreditCardVPOSReturnLog;CreatedDate;datetime
zpBulutTahsilatCreditCardVPOSReturnLog;LastUpdatedDate;datetime
zpBulutTahsilatCreditCardVPOSReturnLog;OperationDate;datetime
zpBulutTahsilatCustomerOnlinePayment;CreatedDate;datetime
zpBulutTahsilatCustomerOnlinePayment;LastUpdatedDate;datetime
zpBulutTahsilatCustomerOnlinePayment;OperationDate;datetime
zpBulutTahsilatDistanceSalePayment;CreatedDate;datetime
zpBulutTahsilatDistanceSalePayment;LastUpdatedDate;datetime
zpBulutTahsilatDistanceSalePayment;OperationDate;datetime
zpBulutTahsilatRetailCustomerBankPaymentList;CreatedDate;datetime
zpBulutTahsilatRetailCustomerBankPaymentList;LastUpdatedDate;datetime
zpBulutTahsilatRetailCustomerBankPaymentList;PaymentDate;datetime
zpChippinProcessedPayment;CreatedDate;datetime
zpChippinProcessedPayment;LastUpdatedDate;datetime
zpChippinServiceLog;CreatedDate;datetime
zpChippinServiceLog;LastUpdatedDate;datetime
zpCOMOCancelPayment;CreatedDate;datetime
zpCOMOCancelPayment;LastUpdatedDate;datetime
zpCOMOCancelPurchase;CreatedDate;datetime
zpCOMOCancelPurchase;LastUpdatedDate;datetime
zpCOMODoPayment;CreatedDate;datetime
zpCOMODoPayment;LastUpdatedDate;datetime
zpCOMOGetBenefits;CreatedDate;datetime
zpCOMOGetBenefits;LastUpdatedDate;datetime
zpCOMOGetMemberDetails;CreatedDate;datetime
zpCOMOGetMemberDetails;LastUpdatedDate;datetime
zpCOMOSubmitPurchase;CreatedDate;datetime
zpCOMOSubmitPurchase;LastUpdatedDate;datetime
zpCOMOVoidPurchase;CreatedDate;datetime
zpCOMOVoidPurchase;LastUpdatedDate;datetime
zpCustomsShoppingHistory;CreatedDate;datetime
zpCustomsShoppingHistory;LastUpdatedDate;datetime
zpDmsCancelByProduct;CancellationDate;date
zpDmsCancelByProduct;CreatedDate;datetime
zpDmsCancelByProduct;LastUpdatedDate;datetime
zpDmsCancelByProductItem;CreatedDate;datetime
zpDmsCancelByProductItem;LastUpdatedDate;datetime
zpDmsCancelByProductPayment;CreatedDate;datetime
zpDmsCancelByProductPayment;LastUpdatedDate;datetime
zpDmsGetCustomerByCode;CreatedDate;datetime
zpDmsGetCustomerByCode;LastUpdatedDate;datetime
zpDmsGetCustomerByCodeBenefits;CreatedDate;datetime
zpDmsGetCustomerByCodeBenefits;LastUpdatedDate;datetime
zpDmsRegisterPaidCheck;CreatedDate;datetime
zpDmsRegisterPaidCheck;LastUpdatedDate;datetime
zpDmsRegisterPaidCheck;TransactionDate;date
zpDmsRegisterPaidCheckItem;CreatedDate;datetime
zpDmsRegisterPaidCheckItem;LastUpdatedDate;datetime
zpDmsRegisterPaidCheckItemCampaign;CreatedDate;datetime
zpDmsRegisterPaidCheckItemCampaign;LastUpdatedDate;datetime
zpDmsRegisterPaidCheckItemProduct;CreatedDate;datetime
zpDmsRegisterPaidCheckItemProduct;LastUpdatedDate;datetime
zpDmsRegisterPaidCheckPayment;CreatedDate;datetime
zpDmsRegisterPaidCheckPayment;LastUpdatedDate;datetime
zpFastPayProcessedPayment;CreatedDate;datetime
zpFastPayProcessedPayment;LastUpdatedDate;datetime
zpFinanceCompanyServiceLog;CreatedDate;datetime
zpFinanceCompanyServiceLog;LastUpdatedDate;datetime
zpGetirCarsiOrderInfo;CreatedDate;datetime
zpGetirCarsiOrderInfo;LastUpdatedDate;datetime
zpGetirCarsiServiceLog;CreatedDate;datetime
zpGetirCarsiServiceLog;LastUpdatedDate;datetime
zpGlobalBlueConfiguration;CreatedDate;datetime
zpGlobalBlueConfiguration;LastUpdatedDate;datetime
zpGlobalBlueLastDocument;CreatedDate;datetime
zpGlobalBlueLastDocument;LastUpdatedDate;datetime
zpGlobalBlueServiceLog;CreatedDate;datetime
zpGlobalBlueServiceLog;LastUpdatedDate;datetime
zpGlobalBlueTransaction;CreatedDate;datetime
zpGlobalBlueTransaction;LastUpdatedDate;datetime
zpGlobalBlueWebServiceLog;CreatedDate;datetime
zpGlobalBlueWebServiceLog;LastUpdatedDate;datetime
zpHopiCancelCoinTransaction;CreatedDate;datetime
zpHopiCancelCoinTransaction;LastUpdatedDate;datetime
zpHopiCancelReturnTransaction;CreatedDate;datetime
zpHopiCancelReturnTransaction;LastUpdatedDate;datetime
zpHopiCompleteCoinTransaction;CreatedDate;datetime
zpHopiCompleteCoinTransaction;LastUpdatedDate;datetime
zpHopiCompleteReturnTransaction;CreatedDate;datetime
zpHopiCompleteReturnTransaction;LastUpdatedDate;datetime
zpHopiGetHopiUserInfo;CreatedDate;datetime
zpHopiGetHopiUserInfo;LastUpdatedDate;datetime
zpHopiGetHopiUserInfoCampaignSummary;CreatedDate;datetime
zpHopiGetHopiUserInfoCampaignSummary;LastUpdatedDate;datetime
zpHopiNotifyCheckOut;CreatedDate;datetime
zpHopiNotifyCheckOut;DateTime;datetime
zpHopiNotifyCheckOut;LastUpdatedDate;datetime
zpHopiNotifyCheckOutCampaignFreePaymentDetail;CreatedDate;datetime
zpHopiNotifyCheckOutCampaignFreePaymentDetail;LastUpdatedDate;datetime
zpHopiNotifyCheckOutPaymentDetail;CreatedDate;datetime
zpHopiNotifyCheckOutPaymentDetail;LastUpdatedDate;datetime
zpHopiNotifyCheckOutSubTotalDetail;CreatedDate;datetime
zpHopiNotifyCheckOutSubTotalDetail;LastUpdatedDate;datetime
zpHopiNotifyCheckOutTransactionInfo;CreatedDate;datetime
zpHopiNotifyCheckOutTransactionInfo;LastUpdatedDate;datetime
zpHopiNotifyCheckOutUsedCampaignDetail;CreatedDate;datetime
zpHopiNotifyCheckOutUsedCampaignDetail;LastUpdatedDate;datetime
zpHopiNotifyCheckOutUsedCoinDetail;CreatedDate;datetime
zpHopiNotifyCheckOutUsedCoinDetail;LastUpdatedDate;datetime
zpHopiPayProcessedPayment;CreatedDate;datetime
zpHopiPayProcessedPayment;LastUpdatedDate;datetime
zpHopiPayServiceLog;CreatedDate;datetime
zpHopiPayServiceLog;LastUpdatedDate;datetime
zpHopiRefundCoin;CreatedDate;datetime
zpHopiRefundCoin;LastUpdatedDate;datetime
zpHopiServiceLog;CreatedDate;datetime
zpHopiServiceLog;LastUpdatedDate;datetime
zpHopiStartCoinTransaction;CreatedDate;datetime
zpHopiStartCoinTransaction;LastUpdatedDate;datetime
zpHopiStartReturnTransaction;CreatedDate;datetime
zpHopiStartReturnTransaction;LastUpdatedDate;datetime
zpHopiStartReturnTransactionReturnCampaignDetail;CreatedDate;datetime
zpHopiStartReturnTransactionReturnCampaignDetail;LastUpdatedDate;datetime
zpHopiStartReturnTransactionReturnTransactionInfo;CreatedDate;datetime
zpHopiStartReturnTransactionReturnTransactionInfo;LastUpdatedDate;datetime
zpHopiUsedCampaignDetailAmountDetail;CreatedDate;datetime
zpHopiUsedCampaignDetailAmountDetail;LastUpdatedDate;datetime
zpHopiUsedCampaignDetailBenefitDetail;CreatedDate;datetime
zpHopiUsedCampaignDetailBenefitDetail;LastUpdatedDate;datetime
zpIyzicoProcessedPayment;CreatedDate;datetime
zpIyzicoProcessedPayment;LastUpdatedDate;datetime
zpJoyRefundTransaction;CreatedDate;datetime
zpJoyRefundTransaction;LastUpdatedDate;datetime
zpMacellanSuperappTransactionInfo;CreatedDate;datetime
zpMacellanSuperappTransactionInfo;LastUpdatedDate;datetime
zpMobilDevWebHookService;CreatedDate;datetime
zpMobilDevWebHookService;LastUpdatedDate;datetime
zpMobilDevWebHookService;ProcessedTime;datetime
zpMobilDevWebHookServiceLog;CreatedDate;datetime
zpMobilDevWebHookServiceLog;LastUpdatedDate;datetime
zpN2AnimaProcessedPayment;CreatedDate;datetime
zpN2AnimaProcessedPayment;LastUpdatedDate;datetime
zpOnlineBankCreditCardPaymentTransaction;CreatedDate;datetime
zpOnlineBankCreditCardPaymentTransaction;LastUpdatedDate;datetime
zpOnlineBankCreditCardPaymentTransaction;TransactionDate;date
zpOnlineBankServiceErrorLog;CreatedDate;datetime
zpOnlineBankServiceErrorLog;LastUpdatedDate;datetime
zpOnlineBankServiceErrorLog;OperationDate;datetime
zpOnlineBankServiceFinrotaErrorLog;CreatedDate;datetime
zpOnlineBankServiceFinrotaErrorLog;LastUpdatedDate;datetime
zpOnlineBankServiceFinrotaErrorLog;OperationDate;datetime
zpOnlineBankServiceFinrotaLog;CreatedDate;datetime
zpOnlineBankServiceFinrotaLog;LastUpdatedDate;datetime
zpOnlineBankServiceLog;CreatedDate;datetime
zpOnlineBankServiceLog;LastUpdatedDate;datetime
zpOnlineBankTransaction;CreatedDate;datetime
zpOnlineBankTransaction;DocumentDate;date
zpOnlineBankTransaction;DueDate;date
zpOnlineBankTransaction;LastUpdatedDate;datetime
zpOnlineBankTransactionFinrota;TransactionDate;datetime
zpOnlineDBSWebServiceLog;CreatedDate;datetime
zpOnlineDBSWebServiceLog;LastUpdatedDate;datetime
zpParoAlisverisBaslat;CreatedDate;datetime
zpParoAlisverisBaslat;LastUpdatedDate;datetime
zpParoAlisverisBitti;CreatedDate;datetime
zpParoAlisverisBitti;LastUpdatedDate;datetime
zpParoAlisverisIptal;CreatedDate;datetime
zpParoAlisverisIptal;LastUpdatedDate;datetime
zpParoDuyuru;CreatedDate;datetime
zpParoDuyuru;LastUpdatedDate;datetime
zpParoIndirim;CreatedDate;datetime
zpParoIndirim;LastUpdatedDate;datetime
zpParoPuan;CreatedDate;datetime
zpParoPuan;LastUpdatedDate;datetime
zpParoPuanIade;CreatedDate;datetime
zpParoPuanIade;LastUpdatedDate;datetime
zpParoUyeKontrol;CreatedDate;datetime
zpParoUyeKontrol;LastUpdatedDate;datetime
zpPaynetCreditCardPaymentEventLog;CreatedDate;datetime
zpPaynetCreditCardPaymentEventLog;LastUpdatedDate;datetime
zpPaynetCreditCardPaymentEventLog;OperationDate;datetime
zpPaynetCreditCardPaymentLine;CreatedDate;datetime
zpPaynetCreditCardPaymentLine;InstallmentStartDate;date
zpPaynetCreditCardPaymentLine;LastUpdatedDate;datetime
zpPaynetDistanceSalePayment;CreatedDate;datetime
zpPaynetDistanceSalePayment;LastUpdatedDate;datetime
zpPaynetDistanceSalePayment;OperationDate;datetime
zpPaynetDistanceSalePaymentURL;CreatedDate;datetime
zpPaynetDistanceSalePaymentURL;LastUpdatedDate;datetime
zpPaynetProcessedPayment;CreatedDate;datetime
zpPaynetProcessedPayment;LastUpdatedDate;datetime
zpTaxFreePointServiceLog;CreatedDate;datetime
zpTaxFreePointServiceLog;LastUpdatedDate;datetime
zpTaxFreeZoneTransaction;CreatedDate;datetime
zpTaxFreeZoneTransaction;LastUpdatedDate;datetime
zpTuratelWebHookServiceLog;CreatedDate;datetime
zpTuratelWebHookServiceLog;LastUpdatedDate;datetime
zpUmicoTransaction;CreatedDate;datetime
zpUmicoTransaction;LastUpdatedDate;datetime
zpUTSForthcomingShipment;CreatedDate;datetime
zpUTSForthcomingShipment;LastUpdatedDate;datetime
zpUTSForthcomingShipment;SKT;date
zpUTSForthcomingShipment;URT;date
zpUTSWebServisHareketBildirim;CreatedDate;datetime
zpUTSWebServisHareketBildirim;LastUpdatedDate;datetime
zpUTSWebServisHareketBildirim;OperationDate;datetime
zpUTSWebServisIptalBildirim;CreatedDate;datetime
zpUTSWebServisIptalBildirim;LastUpdatedDate;datetime
zpUTSWebServisIptalBildirim;OperationDate;datetime
zpUTSWebServisResponseLog;CreatedDate;datetime
zpUTSWebServisResponseLog;LastUpdatedDate;datetime
zpUTSWebServisResponseLog;OperationDate;datetime
zpVodafoneAcService;CreatedDate;datetime
zpVodafoneAcService;LastUpdatedDate;datetime
zpWeArePlanetTaxFreeServiceLog;CreatedDate;datetime
zpWeArePlanetTaxFreeServiceLog;LastUpdatedDate;datetime
zpWeArePlanetTaxFreeTransaction;CreatedDate;datetime
zpWeArePlanetTaxFreeTransaction;LastUpdatedDate;datetime
ztyedekspler;create_date;datetime
ztyedekspler;modify_date;datetime
