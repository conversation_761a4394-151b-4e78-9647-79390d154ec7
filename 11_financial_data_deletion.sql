-- BANK VƏ MALIYYƏ VERİLƏRİNİ SİLMƏ PROSEDURU

CREATE PROCEDURE DeleteFinancialDataBatch
    @CutoffDate DATE = '2025-01-01',
    @BatchSize INT = 500  -- Maliyyə veriləri ü<PERSON>ü<PERSON> k<PERSON>çik batch
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsDeleted INT = 1;
    DECLARE @TotalDeleted INT = 0;
    
    PRINT '🏦 BANK VƏ MALIYYƏ VERİLƏRİNİN SİLMƏSİ BAŞLADI: ' + CAST(@CutoffDate AS VARCHAR(10));
    PRINT 'Batch size: ' + CAST(@BatchSize AS VARCHAR(10));
    PRINT '';
    
    BEGIN TRY
        -- =============================================================================
        -- ADDIM 1: TP EXTENSION TABLE-LAR (ƏN DƏRİN CHILD-LAR)
        -- =============================================================================
        
        PRINT '🔧 ADDIM 1: Extension table-ları siliniyor...';
        
        -- Bank extension table-ları
        DELETE FROM tpBankATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankHeaderOnlineBankIntegration WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankHeaderOnlineBankIntegration silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankMT940 WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankMT940 silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOnlineBankPosPaymentList WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOnlineBankPosPaymentList silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankCreditATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankCreditATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankCreditFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankCreditFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankCreditRelatedCheques WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankCreditRelatedCheques silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankCreditRelatedExportFiles WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankCreditRelatedExportFiles silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankCreditRotativeInterestRates WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankCreditRotativeInterestRates silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Cash extension table-ları
        DELETE FROM tpCashATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCashATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCashFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCashFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Cheque extension table-ları
        DELETE FROM tpChequeATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpChequeATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpChequeFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpChequeFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceExchangeDifferencePaidCheque WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceExchangeDifferencePaidCheque silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Credit Card extension table-ları
        DELETE FROM tpCreditCardPaymentATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCreditCardPaymentATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCreditCardPaymentHeaderOnlineBankIntegration WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCreditCardPaymentHeaderOnlineBankIntegration silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBulutTahsilatCreditCardPayment WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBulutTahsilatCreditCardPayment silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCompanyCreditCardPaymentDueDate WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCompanyCreditCardPaymentDueDate silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCreditCardBulutTahsilatVPOSReturn WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCreditCardBulutTahsilatVPOSReturn silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCreditCardPaymentDueDate WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCreditCardPaymentDueDate silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpCreditCardPaymentFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpCreditCardPaymentFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpPaynetCreditCardPayment WHERE CreatedDate < @CutoffDate;
        PRINT 'tpPaynetCreditCardPayment silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Debit extension table-ları
        DELETE FROM tpAgentContractDeservedDebit WHERE CreatedDate < @CutoffDate;
        PRINT 'tpAgentContractDeservedDebit silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpAgentContractVehicleDebit WHERE CreatedDate < @CutoffDate;
        PRINT 'tpAgentContractVehicleDebit silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpAgentContractVisitFrequencyDebit WHERE CreatedDate < @CutoffDate;
        PRINT 'tpAgentContractVisitFrequencyDebit silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpAgentPerformanceBonusDebit WHERE CreatedDate < @CutoffDate;
        PRINT 'tpAgentPerformanceBonusDebit silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpAgentPerformanceDebit WHERE CreatedDate < @CutoffDate;
        PRINT 'tpAgentPerformanceDebit silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpDebitATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpDebitATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpDebitFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpDebitFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInnerLineDocument WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInnerLineDocument silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Journal extension table-ları
        DELETE FROM tpJournalATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpJournalATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpJournalTaxIncurred WHERE CreatedDate < @CutoffDate;
        PRINT 'tpJournalTaxIncurred silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpJournalFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpJournalFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment extension table-ları
        DELETE FROM tpPaymentATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpPaymentATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpPaymentReturn WHERE CreatedDate < @CutoffDate;
        PRINT 'tpPaymentReturn silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpPaymentFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpPaymentFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Bank Payment Instruction extension table-ları
        DELETE FROM tpBankPaymentInstructionATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankPaymentInstructionATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankPaymentInstructionFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankPaymentInstructionFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Bank Payment List extension table-ları
        DELETE FROM tpBankPaymentListATAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankPaymentListATAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpBankPaymentListFTAttribute WHERE CreatedDate < @CutoffDate;
        PRINT 'tpBankPaymentListFTAttribute silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '✅ ADDIM 1 TAMAMLANDI: Extension table-lar silindi';
        PRINT '';
        
        -- =============================================================================
        -- ADDIM 2: DETAIL VƏ CURRENCY TABLE-LAR
        -- =============================================================================
        
        PRINT '🔧 ADDIM 2: Detail və currency table-ları siliniyor...';
        
        -- Bank detail table-ları
        DELETE FROM trBankLineAdditionalCharge WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankLineAdditionalCharge silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankLineCostCenterRates WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankLineCostCenterRates silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankCreditLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankCreditLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankCreditPaymentPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankCreditPaymentPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Cash detail table-ları
        DELETE FROM trCashLineCostCenterRates WHERE CreatedDate < @CutoffDate;
        PRINT 'trCashLineCostCenterRates silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trCashLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trCashLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Cheque detail table-ları
        DELETE FROM trChequeLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trChequeLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Credit Card detail table-ları
        DELETE FROM trCreditCardPaymentLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trCreditCardPaymentLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Debit detail table-ları
        DELETE FROM trDebitLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trDebitLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Journal detail table-ları
        DELETE FROM trJournalInflationAdjustmentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalInflationAdjustmentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalInflationAdjustmentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalInflationAdjustmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalLedgerEntryNumber WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalLedgerEntryNumber silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalLineCostCenterRates WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalLineCostCenterRates silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment detail table-ları (artıq silindi amma yoxlayaq)
        DELETE FROM trPaymentLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '✅ ADDIM 2 TAMAMLANDI: Detail və currency table-lar silindi';
        PRINT '';
        
        -- =============================================================================
        -- ADDIM 3: LINE TABLE-LAR BATCH SİLMƏ
        -- =============================================================================
        
        PRINT '🔧 ADDIM 3: Line table-ları batch silmə ilə siliniyor...';
        
        -- trBankLine batch silmə
        SET @TotalDeleted = 0;
        SET @RowsDeleted = 1;
        WHILE @RowsDeleted > 0
        BEGIN
            BEGIN TRANSACTION;
            DELETE TOP (@BatchSize) FROM trBankLine WHERE CreatedDate < @CutoffDate;
            SET @RowsDeleted = @@ROWCOUNT;
            SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
            COMMIT TRANSACTION;
            
            PRINT 'trBankLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
            
            IF @RowsDeleted > 0
                WAITFOR DELAY '00:00:00.200'; -- 200ms fasilə
        END;
        
        -- Digər line table-ları
        DELETE FROM trBankCreditLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankCreditLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trCashLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trCashLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trChequeLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trChequeLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trCreditCardPaymentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trCreditCardPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trDebitLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trDebitLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankPaymentInstructionLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankPaymentInstructionLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankPaymentListLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankPaymentListLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '✅ ADDIM 3 TAMAMLANDI: Line table-lar silindi';
        PRINT '';
        
        -- =============================================================================
        -- ADDIM 4: HEADER TABLE-LAR
        -- =============================================================================
        
        PRINT '🔧 ADDIM 4: Header table-ları siliniyor...';
        
        DELETE FROM trBankHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankCreditHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankCreditHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trCashHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trCashHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trChequeHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trChequeHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trCreditCardPaymentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trCreditCardPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trDebitHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trDebitHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trJournalHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trJournalHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankPaymentInstructionHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankPaymentInstructionHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trBankPaymentListHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trBankPaymentListHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '✅ ADDIM 4 TAMAMLANDI: Header table-lar silindi';
        PRINT '';
        
        PRINT '🎯 BANK VƏ MALIYYƏ VERİLƏRİNİN SİLMƏSİ TAMAMLANDI!';
        
    END TRY
    BEGIN CATCH
        PRINT 'XƏTA BAŞ VERDİ!';
        PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
        PRINT 'Xəta sətiri: ' + CAST(ERROR_LINE() AS VARCHAR(10));
        THROW;
    END CATCH
END;
