-- BANK VƏ MALIYYƏ VERİLƏRİNİ YOXLAMA

-- =============================================================================
-- BANK VƏ MALIYYƏ VERİLƏRİNİN MİQDARI
-- =============================================================================

PRINT '🏦 BANK VƏ MALIYYƏ VERİLƏRİNİN YOXLANMASI BAŞLADI...';
PRINT '';

-- BANK VERİLƏRİ
SELECT 'BANK VERİLƏRİ' as category, 
       'trBankHeader' as table_name,
       COUNT(*) as total_records,
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete,
       MIN(CreatedDate) as oldest_date,
       MAX(CreatedDate) as newest_date
FROM trBankHeader
UNION ALL
SELECT 'BANK VERİLƏRİ', 'trBankLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankLine
UNION ALL
SELECT 'BANK VERİLƏRİ', 'trBankCreditHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankCreditHeader
UNION ALL
SELECT 'BANK VERİLƏRİ', 'trBankCreditLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankCreditLine
UNION ALL

-- NAĞD PUL VERİLƏRİ
SELECT 'NAĞD PUL VERİLƏRİ', 'trCashHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trCashHeader
UNION ALL
SELECT 'NAĞD PUL VERİLƏRİ', 'trCashLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trCashLine
UNION ALL

-- ÇEK VERİLƏRİ
SELECT 'ÇEK VERİLƏRİ', 'trChequeHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trChequeHeader
UNION ALL
SELECT 'ÇEK VERİLƏRİ', 'trChequeLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trChequeLine
UNION ALL

-- KREDİT KARTI VERİLƏRİ
SELECT 'KREDİT KARTI VERİLƏRİ', 'trCreditCardPaymentHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trCreditCardPaymentHeader
UNION ALL
SELECT 'KREDİT KARTI VERİLƏRİ', 'trCreditCardPaymentLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trCreditCardPaymentLine
UNION ALL

-- BORC VERİLƏRİ
SELECT 'BORC VERİLƏRİ', 'trDebitHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trDebitHeader
UNION ALL
SELECT 'BORC VERİLƏRİ', 'trDebitLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trDebitLine
UNION ALL

-- JOURNAL VERİLƏRİ
SELECT 'JOURNAL VERİLƏRİ', 'trJournalHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trJournalHeader
UNION ALL
SELECT 'JOURNAL VERİLƏRİ', 'trJournalLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trJournalLine
UNION ALL

-- BANK ÖDƏMƏ TƏLİMATLARI
SELECT 'BANK ÖDƏMƏ TƏLİMATLARI', 'trBankPaymentInstructionHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankPaymentInstructionHeader
UNION ALL
SELECT 'BANK ÖDƏMƏ TƏLİMATLARI', 'trBankPaymentInstructionLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankPaymentInstructionLine
UNION ALL

-- BANK ÖDƏMƏ SİYAHILARI
SELECT 'BANK ÖDƏMƏ SİYAHILARI', 'trBankPaymentListHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankPaymentListHeader
UNION ALL
SELECT 'BANK ÖDƏMƏ SİYAHILARI', 'trBankPaymentListLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trBankPaymentListLine;

PRINT '';
PRINT '=============================================================================';
PRINT '';

-- ÜMUMI XÜLASƏ
SELECT 
    'ÜMUMI XÜLASƏ' as info,
    SUM(CASE WHEN table_name LIKE '%Bank%' THEN records_to_delete ELSE 0 END) as bank_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Cash%' THEN records_to_delete ELSE 0 END) as cash_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Cheque%' THEN records_to_delete ELSE 0 END) as cheque_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Credit%' THEN records_to_delete ELSE 0 END) as credit_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Debit%' THEN records_to_delete ELSE 0 END) as debit_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Journal%' THEN records_to_delete ELSE 0 END) as journal_records_to_delete,
    SUM(records_to_delete) as total_financial_records_to_delete
FROM (
    SELECT 'trBankHeader' as table_name, COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete FROM trBankHeader
    UNION ALL SELECT 'trBankLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankLine
    UNION ALL SELECT 'trBankCreditHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankCreditHeader
    UNION ALL SELECT 'trBankCreditLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankCreditLine
    UNION ALL SELECT 'trCashHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trCashHeader
    UNION ALL SELECT 'trCashLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trCashLine
    UNION ALL SELECT 'trChequeHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trChequeHeader
    UNION ALL SELECT 'trChequeLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trChequeLine
    UNION ALL SELECT 'trCreditCardPaymentHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trCreditCardPaymentHeader
    UNION ALL SELECT 'trCreditCardPaymentLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trCreditCardPaymentLine
    UNION ALL SELECT 'trDebitHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trDebitHeader
    UNION ALL SELECT 'trDebitLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trDebitLine
    UNION ALL SELECT 'trJournalHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trJournalHeader
    UNION ALL SELECT 'trJournalLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trJournalLine
    UNION ALL SELECT 'trBankPaymentInstructionHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankPaymentInstructionHeader
    UNION ALL SELECT 'trBankPaymentInstructionLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankPaymentInstructionLine
    UNION ALL SELECT 'trBankPaymentListHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankPaymentListHeader
    UNION ALL SELECT 'trBankPaymentListLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trBankPaymentListLine
) t;

PRINT '';
PRINT '🚨 DİQQƏT: Bu maliyyə veriləridir! Silmədən əvvəl:';
PRINT '   1. MÜTLƏQ backup yaradın';
PRINT '   2. Mühasibat departamenti ilə məsləhətləşin';
PRINT '   3. Test environment-də sınayın';
PRINT '   4. Vergi qanunvericiliyinə uyğunluğunu yoxlayın';
PRINT '';
PRINT '🏦 BANK VƏ MALIYYƏ VERİLƏRİNİN YOXLANMASI TAMAMLANDI!';
