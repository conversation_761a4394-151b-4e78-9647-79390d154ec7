-- MASTER DATA SAXLANILMASI NƏTİCƏLƏRİNİN YOXLANMASI
-- Transaction verilər silindikdən sonra master data-nın vəziyyətini yoxlayır

-- =============================================================================
-- MASTER DATA YOXLAMASI (SAXLANILMALI)
-- =============================================================================

PRINT '🔍 MASTER DATA YOXLAMASI (SAXLANILMALI):';
PRINT '';

SELECT 
    'MASTER DATA' as category,
    'cdCurrAcc (Müştərilər)' as table_name,
    COUNT(*) as record_count,
    'SAXLANILMALI' as expected_status
FROM cdCurrAcc
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdCurrAcc')

UNION ALL

SELECT 
    'MASTER DATA',
    'cdItem (Məhsullar)',
    COUNT(*),
    'SAXLANILMALI'
FROM cdItem
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdItem')

UNION ALL

SELECT 
    'MASTER DATA',
    'cdGLAcc (Hesab Planı)',
    COUNT(*),
    'SAXLANILMALI'
FROM cdGLAcc
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdGLAcc')

UNION ALL

SELECT 
    'MASTER DATA',
    'cdBank (Bank məlumatları)',
    COUNT(*),
    'SAXLANILMALI'
FROM cdBank
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdBank')

UNION ALL

SELECT 
    'MASTER DATA',
    'cdAccountant (Mühasib)',
    COUNT(*),
    'SAXLANILMALI'
FROM cdAccountant
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdAccountant')

UNION ALL

SELECT 
    'MASTER DATA',
    'bsCompany (Şirkət)',
    COUNT(*),
    'SAXLANILMALI'
FROM bsCompany
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bsCompany')

UNION ALL

SELECT 
    'MASTER DATA',
    'bsOffice (Ofis)',
    COUNT(*),
    'SAXLANILMALI'
FROM bsOffice
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bsOffice')

UNION ALL

SELECT 
    'MASTER DATA',
    'dfCompanyDefault (Şirkət Default-ları)',
    COUNT(*),
    'SAXLANILMALI'
FROM dfCompanyDefault
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dfCompanyDefault');

PRINT '';
PRINT '=============================================================================';
PRINT '';

-- =============================================================================
-- TRANSACTION DATA YOXLAMASI (SİLİNMƏLİ)
-- =============================================================================

PRINT '🗑️ TRANSACTION DATA YOXLAMASI (SİLİNMƏLİ):';
PRINT '';

SELECT 
    'TRANSACTION DATA' as category,
    'trInvoiceHeader (Faktura)' as table_name,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END as status
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trInvoiceLine (Faktura Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceLine')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trOrderHeader (Sifariş)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trOrderLine (Sifariş Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trOrderLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderLine')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trPaymentHeader (Ödəniş)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trPaymentHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trPaymentLine (Ödəniş Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trPaymentLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentLine')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trBankHeader (Bank)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trBankHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trBankLine (Bank Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trBankLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankLine')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trCashHeader (Nağd)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trCashHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trCashLine (Nağd Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trCashLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashLine')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trJournalHeader (Journal)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trJournalHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalHeader')

UNION ALL

SELECT 
    'TRANSACTION DATA',
    'trJournalLine (Journal Sətirləri)',
    COUNT(*),
    CASE WHEN COUNT(*) = 0 THEN '✅ SİLİNDİ' ELSE '❌ HƏLƏ VAR' END
FROM trJournalLine
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalLine');

PRINT '';
PRINT '=============================================================================';
PRINT '';

-- =============================================================================
-- ÜMUMI XÜLASƏ
-- =============================================================================

PRINT '📊 ÜMUMI XÜLASƏ:';
PRINT '';

DECLARE @MasterDataCount INT = 0;
DECLARE @TransactionDataCount INT = 0;

-- Master data sayı
SELECT @MasterDataCount = 
    ISNULL((SELECT COUNT(*) FROM cdCurrAcc), 0) +
    ISNULL((SELECT COUNT(*) FROM cdItem), 0) +
    ISNULL((SELECT COUNT(*) FROM cdGLAcc), 0) +
    ISNULL((SELECT COUNT(*) FROM cdBank), 0) +
    ISNULL((SELECT COUNT(*) FROM bsCompany), 0);

-- Transaction data sayı
SELECT @TransactionDataCount = 
    ISNULL((SELECT COUNT(*) FROM trInvoiceHeader), 0) +
    ISNULL((SELECT COUNT(*) FROM trOrderHeader), 0) +
    ISNULL((SELECT COUNT(*) FROM trPaymentHeader), 0) +
    ISNULL((SELECT COUNT(*) FROM trBankHeader), 0) +
    ISNULL((SELECT COUNT(*) FROM trCashHeader), 0) +
    ISNULL((SELECT COUNT(*) FROM trJournalHeader), 0);

SELECT 
    'XÜLASƏ' as info,
    @MasterDataCount as master_data_records,
    @TransactionDataCount as transaction_data_records,
    CASE 
        WHEN @TransactionDataCount = 0 THEN '✅ TRANSACTION VERİLƏR SİLİNDİ'
        ELSE '❌ TRANSACTION VERİLƏR HƏLƏ VAR'
    END as transaction_status,
    CASE 
        WHEN @MasterDataCount > 0 THEN '✅ MASTER DATA SAXLANILDI'
        ELSE '❌ MASTER DATA SİLİNİB'
    END as master_status;

PRINT '';
PRINT '🎯 NƏTİCƏ:';
PRINT '   Master Data Records: ' + CAST(@MasterDataCount AS VARCHAR(10));
PRINT '   Transaction Data Records: ' + CAST(@TransactionDataCount AS VARCHAR(10));
PRINT '';

IF @TransactionDataCount = 0 AND @MasterDataCount > 0
BEGIN
    PRINT '🎉 ƏMƏLIYYAT UĞURLU!';
    PRINT '   ✅ Transaction verilər silindi';
    PRINT '   ✅ Master data saxlanıldı';
END
ELSE
BEGIN
    PRINT '⚠️ DİQQƏT!';
    IF @TransactionDataCount > 0
        PRINT '   ❌ Bəzi transaction verilər hələ də var';
    IF @MasterDataCount = 0
        PRINT '   ❌ Master data silinib (bu səhvdir!)';
END

PRINT '';
PRINT '📋 SONRAKI ADDIMLAR:';
PRINT '   1. Database performansını test edin';
PRINT '   2. Backup-ın düzgün olduğunu yoxlayın';
PRINT '   3. İstifadəçilərə məlumat verin';
PRINT '   4. Sistem log-larını yoxlayın';
