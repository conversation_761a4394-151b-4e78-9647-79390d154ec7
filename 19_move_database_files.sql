-- DATABASE FAYLLARINI KÖÇÜRMƏK
-- C:\SQL Data ModaTrend\ → E:\sqldata\ köçürmə

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

PRINT '📋 Database köçürmə əməliyyatı başlayır...';
PRINT '';

-- Backup yaradın
BACKUP DATABASE [Test_DB] 
TO DISK = 'C:\Backup\Test_DB_before_move_2025.bak'
WITH FORMAT, INIT;

PRINT '✅ Backup yaradıldı: C:\Backup\Test_DB_before_move_2025.bak';
PRINT '';

-- =============================================================================
-- MÖVCUD FAYL YERLƏRINI YOXLAMAQ
-- =============================================================================

PRINT '📍 Mövcud fayl yerləri:';

SELECT 
    name as logical_name,
    physical_name as current_location,
    size * 8 / 1024 as size_mb,
    type_desc
FROM sys.master_files 
WHERE database_id = DB_ID('Test_DB');

PRINT '';

-- =============================================================================
-- DATABASE-İ DETACH ETMƏK
-- =============================================================================

PRINT '🔄 Database detach edilir...';

USE master;
GO

-- Aktiv connection-ları bağla
ALTER DATABASE [Test_DB] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
GO

-- Database-i detach et
EXEC sp_detach_db 'Test_DB';
GO

PRINT '✅ Test_DB detach edildi';
PRINT '';

-- =============================================================================
-- FAYL KÖÇÜRMƏ TƏLİMATLARI
-- =============================================================================

PRINT '📁 İNDİ WINDOWS EXPLORER-DƏ BU ADDIMLAR EDİN:';
PRINT '';
PRINT '1. E:\sqldata\ qovluğunu yaradın (əgər yoxdursa)';
PRINT '';
PRINT '2. Bu faylları köçürün:';
PRINT '   FROM: C:\SQL Data ModaTrend\Test_DB_Data.mdf';
PRINT '   TO:   E:\sqldata\Test_DB_Data.mdf';
PRINT '';
PRINT '   FROM: C:\SQL Data ModaTrend\Test_DB_Log.ldf';
PRINT '   TO:   E:\sqldata\Test_DB_Log.ldf';
PRINT '';
PRINT '3. Faylların köçürüldüyünü təsdiq edin';
PRINT '';
PRINT '4. Sonra aşağıdakı script-i icra edin:';
PRINT '';

-- =============================================================================
-- ATTACH SCRIPT-İ (KÖÇÜRMƏDƏN SONRA İCRA EDİN)
-- =============================================================================

PRINT '-- KÖÇÜRMƏDƏN SONRA BU SCRIPT-İ İCRA EDİN:';
PRINT '';
PRINT 'USE master;';
PRINT 'GO';
PRINT '';
PRINT '-- Yeni yerdən database-i attach edin';
PRINT 'CREATE DATABASE [Test_DB] ON';
PRINT '    (FILENAME = ''E:\sqldata\Test_DB_Data.mdf''),';
PRINT '    (FILENAME = ''E:\sqldata\Test_DB_Log.ldf'')';
PRINT 'FOR ATTACH;';
PRINT 'GO';
PRINT '';
PRINT '-- Multi-user mode-a qaytar';
PRINT 'ALTER DATABASE [Test_DB] SET MULTI_USER;';
PRINT 'GO';
PRINT '';
PRINT '-- Yoxlayın';
PRINT 'SELECT';
PRINT '    name as logical_name,';
PRINT '    physical_name as new_location,';
PRINT '    size * 8 / 1024 as size_mb,';
PRINT '    type_desc';
PRINT 'FROM sys.master_files';
PRINT 'WHERE database_id = DB_ID(''Test_DB'');';
PRINT '';
PRINT 'PRINT ''✅ Database uğurla E:\sqldata\ köçürüldü!'';';

PRINT '';
PRINT '⚠️ VACIB QEYDLƏR:';
PRINT '   - E:\sqldata\ qovluğunun mövcud olduğundan əmin olun';
PRINT '   - SQL Server service-nin E:\ disk-inə yazma icazəsi olmalıdır';
PRINT '   - Faylları köçürərkən SQL Server Management Studio-nu bağlayın';
PRINT '   - Köçürmə tamamlandıqdan sonra köhnə faylları silin';
PRINT '';
PRINT '🎯 SONRAKI ADDIM:';
PRINT '   Faylları Windows Explorer-də köçürün və attach script-ini icra edin';
