-- SATIŞ MİQDARLARI VƏ ƏLAQƏDAR VERİLƏRİ YOXLAMA

-- =============================================================================
-- SATIŞ MİQDARLARI VƏ ƏLAQƏDAR VERİLƏRİN YOXLANMASI
-- =============================================================================

PRINT '📊 SATIŞ MİQDARLARI VƏ ƏLAQƏDAR VERİLƏRİN YOXLANMASI BAŞLADI...';
PRINT '';

-- SATIŞ PLANLARI VƏ MİQDARLAR
SELECT 'SATIŞ PLANLARI' as category, 
       'trSalesPlan' as table_name,
       COUNT(*) as total_records,
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete,
       MIN(CreatedDate) as oldest_date,
       MAX(CreatedDate) as newest_date
FROM trSalesPlan
UNION ALL
SELECT 'SATIŞ PLANLARI', 'trSalesPlanProduct', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trSalesPlanProduct
UNION ALL
SELECT 'SATIŞ PLANLARI', 'trSalesPlanChannel', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trSalesPlanChannel
UNION ALL
SELECT 'SATIŞ PLANLARI', 'trSalesPlanProductQty', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trSalesPlanProductQty
UNION ALL

-- STOK VƏ ANBAR VERİLƏRİ
SELECT 'STOK VERİLƏRİ', 'trStock', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trStock
UNION ALL

-- REZERV VERİLƏRİ
SELECT 'REZERV VERİLƏRİ', 'trReserveHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trReserveHeader
UNION ALL
SELECT 'REZERV VERİLƏRİ', 'trReserveLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trReserveLine
UNION ALL
SELECT 'REZERV VERİLƏRİ', 'trReserveTransfer', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trReserveTransfer
UNION ALL

-- YIĞIM VERİLƏRİ (PICKING)
SELECT 'YIĞIM VERİLƏRİ', 'trPickingHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trPickingHeader
UNION ALL
SELECT 'YIĞIM VERİLƏRİ', 'trPickingLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trPickingLine
UNION ALL

-- DAXILI SIFARIŞLƏR
SELECT 'DAXILI SIFARIŞLƏR', 'trInnerOrderHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trInnerOrderHeader
UNION ALL
SELECT 'DAXILI SIFARIŞLƏR', 'trInnerOrderLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trInnerOrderLine
UNION ALL

-- DAXILI HƏRƏKƏTLƏR
SELECT 'DAXILI HƏRƏKƏTLƏR', 'trInnerHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trInnerHeader
UNION ALL
SELECT 'DAXILI HƏRƏKƏTLƏR', 'trInnerLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trInnerLine
UNION ALL

-- GÖNDƏRMƏ SİFARİŞLƏRİ
SELECT 'GÖNDƏRMƏ SİFARİŞLƏRİ', 'trDispOrderHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trDispOrderHeader
UNION ALL
SELECT 'GÖNDƏRMƏ SİFARİŞLƏRİ', 'trDispOrderLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trDispOrderLine
UNION ALL

-- NƏQLIYYAT VERİLƏRİ
SELECT 'NƏQLIYYAT VERİLƏRİ', 'trVehicleLoadingHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trVehicleLoadingHeader
UNION ALL
SELECT 'NƏQLIYYAT VERİLƏRİ', 'trVehicleLoadingLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trVehicleLoadingLine
UNION ALL
SELECT 'NƏQLIYYAT VERİLƏRİ', 'trVehicleUnLoadingHeader', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trVehicleUnLoadingHeader
UNION ALL
SELECT 'NƏQLIYYAT VERİLƏRİ', 'trVehicleUnLoadingLine', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trVehicleUnLoadingLine
UNION ALL

-- TRANSFER PLANLARI
SELECT 'TRANSFER PLANLARI', 'trTransferPlan', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trTransferPlan
UNION ALL
SELECT 'TRANSFER PLANLARI', 'trTransferPlanProduct', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trTransferPlanProduct
UNION ALL
SELECT 'TRANSFER PLANLARI', 'trTransferPlanChannel', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trTransferPlanChannel
UNION ALL
SELECT 'TRANSFER PLANLARI', 'trTransferPlanProductQty', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trTransferPlanProductQty
UNION ALL

-- PAYLANMA VERİLƏRİ
SELECT 'PAYLANMA VERİLƏRİ', 'trAllocation', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trAllocation
UNION ALL
SELECT 'PAYLANMA VERİLƏRİ', 'trAllocationProduct', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trAllocationProduct
UNION ALL
SELECT 'PAYLANMA VERİLƏRİ', 'trAllocationChannel', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trAllocationChannel
UNION ALL
SELECT 'PAYLANMA VERİLƏRİ', 'trAllocationProductQty', COUNT(*), 
       COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
       MIN(CreatedDate), MAX(CreatedDate)
FROM trAllocationProductQty;

PRINT '';
PRINT '=============================================================================';
PRINT '';

-- ÜMUMI XÜLASƏ
SELECT 
    'ÜMUMI XÜLASƏ' as info,
    SUM(CASE WHEN table_name LIKE '%Sales%' OR table_name LIKE '%Plan%' THEN records_to_delete ELSE 0 END) as sales_plan_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Stock%' THEN records_to_delete ELSE 0 END) as stock_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Reserve%' THEN records_to_delete ELSE 0 END) as reserve_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Picking%' THEN records_to_delete ELSE 0 END) as picking_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Inner%' THEN records_to_delete ELSE 0 END) as inner_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Vehicle%' THEN records_to_delete ELSE 0 END) as vehicle_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Transfer%' THEN records_to_delete ELSE 0 END) as transfer_records_to_delete,
    SUM(CASE WHEN table_name LIKE '%Allocation%' THEN records_to_delete ELSE 0 END) as allocation_records_to_delete,
    SUM(records_to_delete) as total_quantity_related_records_to_delete
FROM (
    SELECT 'trSalesPlan' as table_name, COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete FROM trSalesPlan
    UNION ALL SELECT 'trSalesPlanProduct', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trSalesPlanProduct
    UNION ALL SELECT 'trSalesPlanChannel', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trSalesPlanChannel
    UNION ALL SELECT 'trSalesPlanProductQty', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trSalesPlanProductQty
    UNION ALL SELECT 'trStock', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trStock
    UNION ALL SELECT 'trReserveHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trReserveHeader
    UNION ALL SELECT 'trReserveLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trReserveLine
    UNION ALL SELECT 'trReserveTransfer', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trReserveTransfer
    UNION ALL SELECT 'trPickingHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trPickingHeader
    UNION ALL SELECT 'trPickingLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trPickingLine
    UNION ALL SELECT 'trInnerOrderHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trInnerOrderHeader
    UNION ALL SELECT 'trInnerOrderLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trInnerOrderLine
    UNION ALL SELECT 'trInnerHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trInnerHeader
    UNION ALL SELECT 'trInnerLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trInnerLine
    UNION ALL SELECT 'trDispOrderHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trDispOrderHeader
    UNION ALL SELECT 'trDispOrderLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trDispOrderLine
    UNION ALL SELECT 'trVehicleLoadingHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trVehicleLoadingHeader
    UNION ALL SELECT 'trVehicleLoadingLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trVehicleLoadingLine
    UNION ALL SELECT 'trVehicleUnLoadingHeader', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trVehicleUnLoadingHeader
    UNION ALL SELECT 'trVehicleUnLoadingLine', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trVehicleUnLoadingLine
    UNION ALL SELECT 'trTransferPlan', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trTransferPlan
    UNION ALL SELECT 'trTransferPlanProduct', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trTransferPlanProduct
    UNION ALL SELECT 'trTransferPlanChannel', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trTransferPlanChannel
    UNION ALL SELECT 'trTransferPlanProductQty', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trTransferPlanProductQty
    UNION ALL SELECT 'trAllocation', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trAllocation
    UNION ALL SELECT 'trAllocationProduct', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trAllocationProduct
    UNION ALL SELECT 'trAllocationChannel', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trAllocationChannel
    UNION ALL SELECT 'trAllocationProductQty', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) FROM trAllocationProductQty
) t;

PRINT '';
PRINT '🚨 DİQQƏT: Bu anbar və stok veriləridir!';
PRINT '   - Stok veriləri cari stok balansını təsir edə bilər';
PRINT '   - Rezerv veriləri aktiv sifarişləri təsir edə bilər';
PRINT '   - Transfer planları gələcək əməliyyatları təsir edə bilər';
PRINT '';
PRINT '📊 SATIŞ MİQDARLARI VƏ ƏLAQƏDAR VERİLƏRİN YOXLANMASI TAMAMLANDI!';
