-- Sizin ERP sistemində böyük table-lar üçün batch silmə skripti
-- Performance optimallaşdırılmış və monitoring ilə

-- =============================================================================
-- 1. BATCH SİLMƏ PROSEDURU (AUDIT TABLE-LARI ÜÇÜN)
-- =============================================================================

DELIMITER //

CREATE PROCEDURE BatchDeleteAuditData(
    IN cutoff_date DATE,
    IN batch_size INT DEFAULT 1000,
    IN sleep_seconds DECIMAL(3,1) DEFAULT 0.1
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    DECLARE table_name VARCHAR(100);
    DECLARE start_time DATETIME DEFAULT NOW();
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'BATCH SİLMƏ XƏTASI!' as error_message, table_name as failed_table;
        RESIGNAL;
    END;
    
    SELECT 'BATCH SİLMƏ BAŞLADI' as status, start_time as start_time;
    
    -- =============================================================================
    -- AUDIT TRACE TABLE-LARI (ƏN BÖYÜK VƏ ƏN ÇOX İSTİFADƏ OLUNAN)
    -- =============================================================================
    
    -- auAllocationTrace
    SET table_name = 'auAllocationTrace';
    SET total_deleted = 0;
    
    batch_loop_1: LOOP
        START TRANSACTION;
        DELETE FROM auAllocationTrace 
        WHERE OperationDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_1;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auInvoiceTrace
    SET table_name = 'auInvoiceTrace';
    SET total_deleted = 0;
    
    batch_loop_2: LOOP
        START TRANSACTION;
        DELETE FROM auInvoiceTrace 
        WHERE OperationDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_2;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auShipmentTrace
    SET table_name = 'auShipmentTrace';
    SET total_deleted = 0;
    
    batch_loop_3: LOOP
        START TRANSACTION;
        DELETE FROM auShipmentTrace 
        WHERE OperationDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_3;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auRetailInvoiceLineChangeTrace
    SET table_name = 'auRetailInvoiceLineChangeTrace';
    SET total_deleted = 0;
    
    batch_loop_4: LOOP
        START TRANSACTION;
        DELETE FROM auRetailInvoiceLineChangeTrace 
        WHERE OperationDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_4;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auInvoiceReprintTrace
    SET table_name = 'auInvoiceReprintTrace';
    SET total_deleted = 0;
    
    batch_loop_5: LOOP
        START TRANSACTION;
        DELETE FROM auInvoiceReprintTrace 
        WHERE OperationDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_5;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auPaymentReprintTrace
    SET table_name = 'auPaymentReprintTrace';
    SET total_deleted = 0;
    
    batch_loop_6: LOOP
        START TRANSACTION;
        DELETE FROM auPaymentReprintTrace 
        WHERE DocumentDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_6;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    SELECT 
        'AUDIT TRACE TABLE-LARI SİLİNDİ' as final_status,
        TIMEDIFF(NOW(), start_time) as total_duration;
        
END //

DELIMITER ;

-- =============================================================================
-- 2. SERVICE LOG TABLE-LARI ÜÇÜN BATCH SİLMƏ
-- =============================================================================

DELIMITER //

CREATE PROCEDURE BatchDeleteServiceLogs(
    IN cutoff_date DATE,
    IN batch_size INT DEFAULT 2000,
    IN sleep_seconds DECIMAL(3,1) DEFAULT 0.05
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    DECLARE table_name VARCHAR(100);
    DECLARE start_time DATETIME DEFAULT NOW();
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'SERVICE LOG SİLMƏ XƏTASI!' as error_message, table_name as failed_table;
        RESIGNAL;
    END;
    
    SELECT 'SERVICE LOG SİLMƏ BAŞLADI' as status, start_time as start_time;
    
    -- auBasefyServiceLog
    SET table_name = 'auBasefyServiceLog';
    SET total_deleted = 0;
    
    service_loop_1: LOOP
        START TRANSACTION;
        DELETE FROM auBasefyServiceLog 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE service_loop_1;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auCustomsServiceLog
    SET table_name = 'auCustomsServiceLog';
    SET total_deleted = 0;
    
    service_loop_2: LOOP
        START TRANSACTION;
        DELETE FROM auCustomsServiceLog 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE service_loop_2;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auFastPayServiceLog
    SET table_name = 'auFastPayServiceLog';
    SET total_deleted = 0;
    
    service_loop_3: LOOP
        START TRANSACTION;
        DELETE FROM auFastPayServiceLog 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE service_loop_3;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- auIyzicoServiceLog
    SET table_name = 'auIyzicoServiceLog';
    SET total_deleted = 0;
    
    service_loop_4: LOOP
        START TRANSACTION;
        DELETE FROM auIyzicoServiceLog 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - ', total_deleted, ' record') as progress;
        
        IF affected_rows = 0 THEN
            LEAVE service_loop_4;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    SELECT 
        'SERVICE LOG TABLE-LARI SİLİNDİ' as final_status,
        TIMEDIFF(NOW(), start_time) as total_duration;
        
END //

DELIMITER ;

-- =============================================================================
-- 3. MASTER BATCH SİLMƏ PROSEDURU
-- =============================================================================

DELIMITER //

CREATE PROCEDURE MasterBatchDelete(
    IN cutoff_date DATE,
    IN include_service_logs BOOLEAN DEFAULT TRUE,
    IN include_trace_data BOOLEAN DEFAULT TRUE,
    IN batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE start_time DATETIME DEFAULT NOW();
    
    SELECT 
        'MASTER BATCH SİLMƏ BAŞLADI' as status,
        cutoff_date as cutoff_date,
        include_service_logs as service_logs,
        include_trace_data as trace_data,
        batch_size as batch_size,
        start_time as start_time;
    
    -- Trace data silmək
    IF include_trace_data THEN
        CALL BatchDeleteAuditData(cutoff_date, batch_size, 0.1);
    END IF;
    
    -- Service logs silmək
    IF include_service_logs THEN
        CALL BatchDeleteServiceLogs(cutoff_date, batch_size * 2, 0.05);
    END IF;
    
    SELECT 
        'MASTER BATCH SİLMƏ TAMAMLANDI' as final_status,
        TIMEDIFF(NOW(), start_time) as total_duration,
        NOW() as end_time;
        
END //

DELIMITER ;

-- =============================================================================
-- 4. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

-- Yalnız audit trace data silmək
-- CALL BatchDeleteAuditData('2025-01-01', 1000, 0.1);

-- Yalnız service logs silmək  
-- CALL BatchDeleteServiceLogs('2025-01-01', 2000, 0.05);

-- Hər şeyi silmək
-- CALL MasterBatchDelete('2025-01-01', TRUE, TRUE, 1000);

-- Yalnız trace data silmək
-- CALL MasterBatchDelete('2025-01-01', FALSE, TRUE, 1000);

-- =============================================================================
-- 5. MONITORING QUERY-LƏRİ
-- =============================================================================

-- Prosesin gedişatını izləmək üçün:
SELECT 
    'CURRENT STATUS' as info,
    COUNT(*) as active_connections,
    COUNT(CASE WHEN COMMAND != 'Sleep' THEN 1 END) as active_queries,
    COUNT(CASE WHEN STATE LIKE '%delete%' THEN 1 END) as delete_operations
FROM INFORMATION_SCHEMA.PROCESSLIST
WHERE DB = DATABASE();

-- Table ölçülərini yoxlamaq:
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    ROUND((data_free / 1024 / 1024), 2) AS 'Free (MB)'
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND table_name LIKE 'au%'
ORDER BY (data_length + index_length) DESC
LIMIT 10;

-- =============================================================================
-- 6. PERFORMANCE OPTIMALLAŞDIRMASI
-- =============================================================================

-- Bu index-ləri yaradın (əgər yoxdursa):
-- CREATE INDEX idx_auAllocationTrace_OperationDate ON auAllocationTrace(OperationDate);
-- CREATE INDEX idx_auInvoiceTrace_OperationDate ON auInvoiceTrace(OperationDate);
-- CREATE INDEX idx_auShipmentTrace_OperationDate ON auShipmentTrace(OperationDate);
-- CREATE INDEX idx_auBasefyServiceLog_CreatedDate ON auBasefyServiceLog(CreatedDate);

-- Statistikaları yeniləyin:
-- ANALYZE TABLE auAllocationTrace, auInvoiceTrace, auShipmentTrace, auBasefyServiceLog;
