-- BANK VƏ MALIYYƏ VERİLƏRİNİ SİLMƏ İCRA ETMƏK

-- =============================================================================
-- ÇOX VACİB XƏBƏRDARLIQ!
-- =============================================================================

/*
🚨🚨🚨 ÇOX VACİB XƏBƏRDARLIQ! 🚨🚨🚨

Bu skript BANK VƏ MALIYYƏ VERİLƏRİNİ siləcək!

MÜTLƏQ ADDIMLAR:
1. ✅ Database-in TAM backup-ını yaradın
2. ✅ Mühasibat departamenti ilə məsləhətləşin  
3. ✅ Vergi qanunvericiliyinə uyğunluğunu yoxlayın
4. ✅ Test environment-də sınayın
5. ✅ Maliyyə audit-i keçirin

Bu veriləri silmək:
- Vergi problemləri yarada bilər
- Mühasibat balansını poza bilər  
- Audit problemləri yarada bilər
- Qanuni problemlər yarada bilər

YALNIZ TAMAMILƏ ƏMİN OLDUQDA İCRA EDİN!
*/

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [Test_DB] 
-- TO DISK = 'C:\Backup\financial_backup_2025.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- ƏVVƏL YOXLAMA EDİN
-- =============================================================================

-- Əvvəl bu faylı icra edin ki, nə qədər veri silinəcəyini görəsiniz:
-- 10_check_financial_data.sql

-- =============================================================================
-- MALIYYƏ VERİLƏRİNİ SİLMƏ
-- =============================================================================

-- Kiçik veri üçün (< 50,000 record):
EXEC DeleteFinancialDataBatch @CutoffDate = '2025-01-01', @BatchSize = 500;

-- Böyük veri üçün (> 50,000 record):
-- EXEC DeleteFinancialDataBatch @CutoffDate = '2025-01-01', @BatchSize = 200;

-- Çox böyük veri üçün (> 500,000 record):
-- EXEC DeleteFinancialDataBatch @CutoffDate = '2025-01-01', @BatchSize = 100;

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT 'BANK VERİLƏRİ' as category,
       COUNT(*) as remaining_records
FROM trBankHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'BANK LINE VERİLƏRİ',
       COUNT(*)
FROM trBankLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'NAĞD PUL VERİLƏRİ',
       COUNT(*)
FROM trCashHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'ÇEK VERİLƏRİ',
       COUNT(*)
FROM trChequeHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'KREDİT KARTI VERİLƏRİ',
       COUNT(*)
FROM trCreditCardPaymentHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'BORC VERİLƏRİ',
       COUNT(*)
FROM trDebitHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'JOURNAL VERİLƏRİ',
       COUNT(*)
FROM trJournalHeader 
WHERE CreatedDate < '2025-01-01';

-- Ümumi xülasə:
SELECT 
    'ÜMUMI NƏTİCƏ' as info,
    COUNT(*) as total_remaining_financial_records
FROM (
    SELECT 1 FROM trBankHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trBankLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCashHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCashLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trChequeHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trChequeLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCreditCardPaymentHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCreditCardPaymentLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trDebitHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trDebitLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trJournalHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trJournalLine WHERE CreatedDate < '2025-01-01'
) t;

-- =============================================================================
-- ƏGƏR PROBLEM OLARSA
-- =============================================================================

/*
ƏGƏR XƏTA OLARSA:

1. Foreign Key Constraint xətası:
   - Hansı table-ın silinmədiyini yoxlayın
   - Həmin table üçün ayrıca extension table-ları əlavə edin

2. Timeout xətası:
   - Batch size-ı azaldın (100-200)
   - Fasilə müddətini artırın

3. Lock xətası:
   - Başqa istifadəçilərin sistemdən çıxmasını təmin edin
   - Maintenance window-da icra edin

4. Disk space xətası:
   - Transaction log-u təmizləyin
   - Backup-ları köhnə yerə köçürün

BACKUP-dan RESTORE ETMƏK:
RESTORE DATABASE [Test_DB] 
FROM DISK = 'C:\Backup\financial_backup_2025.bak'
WITH REPLACE;
*/

PRINT '';
PRINT '🏦 BANK VƏ MALIYYƏ VERİLƏRİNİN SİLMƏSİ TAMAMLANDI!';
PRINT '';
PRINT '🔍 NƏTİCƏLƏRİ YOXLAYIN:';
PRINT '   - Qalan record sayını yoxlayın';
PRINT '   - Mühasibat balansını yoxlayın';
PRINT '   - Sistem performansını test edin';
PRINT '';
PRINT '⚠️  UNUTMAYIN:';
PRINT '   - Mühasibat departamentinə məlumat verin';
PRINT '   - Audit log-larını yoxlayın';
PRINT '   - Backup-ın düzgün olduğunu təsdiq edin';
