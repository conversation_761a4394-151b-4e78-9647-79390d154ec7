-- TAM DÜZƏLDİLMİŞ BATCH SİLMƏ PROSEDURU (Bütün Extension Table-lar daxil)

CREATE PROCEDURE DeleteSalesDataCompleteFixed
    @CutoffDate DATE = '2025-01-01',
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsDeleted INT = 1;
    DECLARE @TotalDeleted INT = 0;
    
    PRINT 'Tam düzəldilmiş batch silmə başladı: ' + CAST(@CutoffDate AS VARCHAR(10)) + ', Batch size: ' + CAST(@BatchSize AS VARCHAR(10));
    
    BEGIN TRY
        -- =============================================================================
        -- ADDIM 1: BÜTÜN EXTENSION VƏ TP TABLE-LAR (ƏN DƏRİN CHILD-LAR)
        -- =============================================================================
        
        -- Invoice ilə əlaqəli bütün tp table-lar
        DELETE FROM tpInvoiceDiscountOffer WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceDiscountOffer silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceDiscountOfferContributor WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceDiscountOfferContributor silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceLineExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLineExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceLinePickingDetails WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLinePickingDetails silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceHeaderSalesPerson WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceHeaderSalesPerson silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceUBLExtensions WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceUBLExtensions silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoicePassportAndBoardingInfo WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoicePassportAndBoardingInfo silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceOpticalContribution WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceOpticalContribution silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceLineOpticalProductInfo WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLineOpticalProductInfo silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceLineAgentPerformance WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLineAgentPerformance silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceLineExpenseAccrual WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLineExpenseAccrual silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order ilə əlaqəli tp table-lar
        DELETE FROM tpOrderDiscountOffer WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderDiscountOffer silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderDiscountOfferContributor WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderDiscountOfferContributor silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderLineExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderLineExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderDeliveryDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderDeliveryDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrdersViaInternetInfo WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrdersViaInternetInfo silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderCancelDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderCancelDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderLineSerialNumber WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderLineSerialNumber silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment ilə əlaqəli tp table-lar
        DELETE FROM tpShipmentHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpShipmentHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpShipmentLinePickingDetails WHERE CreatedDate < @CutoffDate;
        PRINT 'tpShipmentLinePickingDetails silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpShipmentUBLExtensions WHERE CreatedDate < @CutoffDate;
        PRINT 'tpShipmentUBLExtensions silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== BÜTÜN TP EXTENSION TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 2: DETAIL TABLE-LAR
        -- =============================================================================
        
        -- Invoice detail table-ları
        DELETE FROM trInvoiceLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineReportedSales WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineReportedSales silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineSubsequentDeliveryOrders WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSubsequentDeliveryOrders silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order detail table-ları
        DELETE FROM trOrderLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderOpticalProductLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProductLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment detail table-ları
        DELETE FROM trShipmentLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment detail table-ları
        DELETE FROM trPaymentLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Sales plan detail table-ları
        DELETE FROM trSalesPlanProductQty WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProductQty silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== DETAIL TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 3: SUM TABLE-LAR
        -- =============================================================================
        
        DELETE FROM trInvoiceLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== SUM TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 4: LINE TABLE-LAR BATCH SİLMƏ
        -- =============================================================================
        
        -- trInvoiceLine batch silmə (ən böyük table)
        SET @TotalDeleted = 0;
        SET @RowsDeleted = 1;
        WHILE @RowsDeleted > 0
        BEGIN
            BEGIN TRANSACTION;
            DELETE TOP (@BatchSize) FROM trInvoiceLine WHERE CreatedDate < @CutoffDate;
            SET @RowsDeleted = @@ROWCOUNT;
            SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
            COMMIT TRANSACTION;
            
            PRINT 'trInvoiceLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
            
            IF @RowsDeleted > 0
                WAITFOR DELAY '00:00:00.100'; -- 100ms fasilə
        END;
        
        -- Digər line table-ları
        DELETE FROM trOrderLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trPaymentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderAdvancePayments WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderAdvancePayments silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlanProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlanChannel WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanChannel silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderOpticalProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderPaymentPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderPaymentPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== LINE TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 5: HEADER TABLE-LAR
        -- =============================================================================
        
        DELETE FROM trInvoiceHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trPaymentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== HEADER TABLE-LAR SİLİNDİ ===';
        
        PRINT '🎯 TAM DÜZƏLDİLMİŞ BATCH SATIŞ SİLMƏ TAMAMLANDI!';
        
    END TRY
    BEGIN CATCH
        PRINT 'XƏTA BAŞ VERDİ!';
        PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
        PRINT 'Xəta sətiri: ' + CAST(ERROR_LINE() AS VARCHAR(10));
        THROW;
    END CATCH
END;
