-- Sadə və işləyən satış veriləri yoxlama skripti
-- 2025.01.01-ə qədər nə qədər veri silinəcəyini yoxlayır

-- =============================================================================
-- 1. DATABASE VƏ TABLE YOXLAMASI
-- =============================================================================

-- Hazırki database
SELECT DATABASE() as current_database;

-- Satış table-larının mövcudluğu
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND table_name IN (
    'trInvoiceHeader', 'trInvoiceLine', 
    'trOrderHeader', 'trOrderLine', 
    'trShipmentHeader', 'trShipmentLine',
    'trPaymentHeader', 'trPaymentLine'
)
ORDER BY table_rows DESC;

-- =============================================================================
-- 2. FAKTURA VERİLƏRİ ANALİZİ
-- =============================================================================

-- trInvoiceHeader yoxlaması
SELECT 'FAKTURA HEADER ANALİZİ' as info;

SELECT 
    COUNT(*) as total_invoices,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as invoices_to_delete,
    MIN(CreatedDate) as oldest_invoice,
    MAX(CreatedDate) as newest_invoice
FROM trInvoiceHeader;

-- trInvoiceLine yoxlaması  
SELECT 'FAKTURA LINE ANALİZİ' as info;

SELECT 
    COUNT(*) as total_invoice_lines,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as lines_to_delete,
    MIN(CreatedDate) as oldest_line,
    MAX(CreatedDate) as newest_line
FROM trInvoiceLine;

-- =============================================================================
-- 3. SİFARİŞ VERİLƏRİ ANALİZİ
-- =============================================================================

-- trOrderHeader yoxlaması
SELECT 'SİFARİŞ HEADER ANALİZİ' as info;

SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as orders_to_delete,
    MIN(CreatedDate) as oldest_order,
    MAX(CreatedDate) as newest_order
FROM trOrderHeader;

-- trOrderLine yoxlaması
SELECT 'SİFARİŞ LINE ANALİZİ' as info;

SELECT 
    COUNT(*) as total_order_lines,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as lines_to_delete,
    MIN(CreatedDate) as oldest_line,
    MAX(CreatedDate) as newest_line
FROM trOrderLine;

-- =============================================================================
-- 4. GÖNDƏRMƏ VERİLƏRİ ANALİZİ
-- =============================================================================

-- trShipmentHeader yoxlaması
SELECT 'GÖNDƏRMƏ HEADER ANALİZİ' as info;

SELECT 
    COUNT(*) as total_shipments,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as shipments_to_delete,
    MIN(CreatedDate) as oldest_shipment,
    MAX(CreatedDate) as newest_shipment
FROM trShipmentHeader;

-- trShipmentLine yoxlaması
SELECT 'GÖNDƏRMƏ LINE ANALİZİ' as info;

SELECT 
    COUNT(*) as total_shipment_lines,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as lines_to_delete,
    MIN(CreatedDate) as oldest_line,
    MAX(CreatedDate) as newest_line
FROM trShipmentLine;

-- =============================================================================
-- 5. ÜMUMI XÜLASƏ
-- =============================================================================

SELECT 'ÜMUMI XÜLASƏ' as info;

SELECT 
    'FAKTURA' as table_type,
    (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') as headers_to_delete,
    (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') as lines_to_delete
UNION ALL
SELECT 
    'SİFARİŞ',
    (SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trOrderLine WHERE CreatedDate < '2025-01-01')
UNION ALL
SELECT 
    'GÖNDƏRMƏ',
    (SELECT COUNT(*) FROM trShipmentHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trShipmentLine WHERE CreatedDate < '2025-01-01');

-- =============================================================================
-- 6. MALIYYƏ TƏSİRİ (əgər NetAmount sütunu varsa)
-- =============================================================================

SELECT 'MALIYYƏ TƏSİRİ' as info;

-- Faktura məbləği yoxlaması
SELECT 
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as invoices_to_delete,
    SUM(CASE WHEN CreatedDate < '2025-01-01' THEN COALESCE(NetAmount, 0) END) as total_amount_to_delete
FROM trInvoiceHeader
WHERE EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'trInvoiceHeader' 
    AND column_name = 'NetAmount'
);

-- =============================================================================
-- 7. SON SATIŞ FƏALİYYƏTİ
-- =============================================================================

SELECT 'SON SATIŞ FƏALİYYƏTİ' as info;

-- Son 30 gündə yaradılmış fakturaları yoxla
SELECT 
    'Son 30 gün' as period,
    COUNT(*) as new_invoices
FROM trInvoiceHeader
WHERE CreatedDate >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
UNION ALL
SELECT 
    'Son 7 gün',
    COUNT(*)
FROM trInvoiceHeader
WHERE CreatedDate >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
UNION ALL
SELECT 
    'Bugün',
    COUNT(*)
FROM trInvoiceHeader
WHERE DATE(CreatedDate) = CURDATE();

-- =============================================================================
-- 8. FOREIGN KEY CONSTRAINT-LƏR
-- =============================================================================

SELECT 'FOREIGN KEY CONSTRAINT-LƏR' as info;

SELECT 
    TABLE_NAME as child_table,
    COLUMN_NAME as child_column,
    REFERENCED_TABLE_NAME as parent_table,
    REFERENCED_COLUMN_NAME as parent_column
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL
AND (TABLE_NAME LIKE 'trInvoice%' OR TABLE_NAME LIKE 'trOrder%' OR TABLE_NAME LIKE 'trShipment%')
ORDER BY REFERENCED_TABLE_NAME, TABLE_NAME;

-- =============================================================================
-- 9. XƏBƏRDARLIQLAR
-- =============================================================================

SELECT 'XƏBƏRDARLIQLAR' as info;

SELECT 
    'DİQQƏT!' as warning_type,
    'Bu əməliyyat 2025.01.01-ə qədər BÜTÜN satışları siləcək!' as warning_message
UNION ALL
SELECT 
    'BACKUP',
    'Mütləq backup yaradın!'
UNION ALL
SELECT 
    'TEST',
    'Əvvəl test environment-də sınayın!'
UNION ALL
SELECT 
    'VAXT',
    'Bu əməliyyat 4-8 saat çəkə bilər!'
UNION ALL
SELECT 
    'İSTİFADƏÇİLƏR',
    'İstifadəçiləri xəbərdar edin!';

-- =============================================================================
-- 10. HAZIR KOMANDALAR
-- =============================================================================

SELECT 'HAZIR KOMANDALAR' as info;

SELECT 
    'Backup yaratmaq' as action,
    'mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine > backup.sql' as command
UNION ALL
SELECT 
    'Audit veriləri silmək',
    'CALL DeleteSalesAuditData(''2025-01-01'');'
UNION ALL
SELECT 
    'Əsas silmə əməliyyatı',
    'CALL BatchDeleteSalesData(''2025-01-01'', 1000, 0.2, 100);'
UNION ALL
SELECT 
    'Progress monitor',
    'SELECT * FROM sales_deletion_log ORDER BY deletion_time DESC LIMIT 10;'
UNION ALL
SELECT 
    'Təcili dayandırma',
    'CALL StopSalesDeletion();';

SELECT 'YOXLAMA TAMAMLANDI - NƏTİCƏLƏRİ ANALİZ EDİN!' as final_message;
