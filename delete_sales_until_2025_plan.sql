-- 2025.01.01-ə qədər BÜTÜN SATIŞLARI silmək üçün tam plan
-- Bu çox böyük bir əməliyyatdır - çox diqqətli olun!

-- =============================================================================
-- 1. ƏVVƏL BU QUERY-Nİ İCRA EDİN - NƏ QƏDƏR VERİ SİLİNƏCƏYİNİ GÖRÜN
-- =============================================================================

SELECT 
    '🔍 2025.01.01-Ə QƏDƏR SİLİNƏCƏK SATIŞ VERİLƏRİ' as section,
    '' as table_name,
    '' as total_records,
    '' as records_to_delete,
    '' as size_mb;

-- Faktura veriləri
SELECT 
    '📄 FAKTURA VERİLƏRİ' as section,
    'trInvoiceHeader' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) as records_to_delete,
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trInvoiceHeader')), 2) as size_mb
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1)

UNION ALL

SELECT 
    '📄 FAKTURA SƏTİRLƏRİ',
    'trInvoiceLine',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trInvoiceLine')), 2)
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM trInvoiceLine LIMIT 1)

UNION ALL

-- Sifariş veriləri
SELECT 
    '🛒 SİFARİŞ VERİLƏRİ',
    'trOrderHeader',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trOrderHeader')), 2)
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1)

UNION ALL

SELECT 
    '🛒 SİFARİŞ SƏTİRLƏRİ',
    'trOrderLine',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trOrderLine')), 2)
FROM trOrderLine
WHERE EXISTS (SELECT 1 FROM trOrderLine LIMIT 1)

UNION ALL

-- Göndərmə veriləri
SELECT 
    '🚚 GÖNDƏRMƏ VERİLƏRİ',
    'trShipmentHeader',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trShipmentHeader')), 2)
FROM trShipmentHeader
WHERE EXISTS (SELECT 1 FROM trShipmentHeader LIMIT 1)

UNION ALL

SELECT 
    '🚚 GÖNDƏRMƏ SƏTİRLƏRİ',
    'trShipmentLine',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trShipmentLine')), 2)
FROM trShipmentLine
WHERE EXISTS (SELECT 1 FROM trShipmentLine LIMIT 1)

UNION ALL

-- Ödəniş veriləri
SELECT 
    '💳 ÖDƏNİŞ VERİLƏRİ',
    'trPaymentHeader',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trPaymentHeader')), 2)
FROM trPaymentHeader
WHERE EXISTS (SELECT 1 FROM trPaymentHeader LIMIT 1);

-- =============================================================================
-- 2. MALIYYƏ TƏSİRİ ANALİZİ
-- =============================================================================

SELECT 
    '💰 MALIYYƏ TƏSİRİ ANALİZİ' as section,
    '' as info,
    '' as amount,
    '' as count;

-- Silinəcək fakturaların ümumi məbləği
SELECT 
    '💰 SİLİNƏCƏK FAKTURA MƏBLƏĞİ' as section,
    'Ümumi məbləğ' as info,
    CONCAT(FORMAT(COALESCE(SUM(CASE WHEN CreatedDate < '2025-01-01' THEN NetAmount END), 0), 2), ' AZN') as amount,
    CONCAT(COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' faktura') as count
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader WHERE NetAmount IS NOT NULL LIMIT 1);

-- Aylıq paylanma
SELECT 
    '📊 AYLIK PAYLANMA' as section,
    CONCAT(YEAR(CreatedDate), '-', LPAD(MONTH(CreatedDate), 2, '0')) as info,
    CONCAT(FORMAT(SUM(NetAmount), 2), ' AZN') as amount,
    CONCAT(COUNT(*), ' faktura') as count
FROM trInvoiceHeader
WHERE CreatedDate < '2025-01-01'
AND EXISTS (SELECT 1 FROM trInvoiceHeader WHERE NetAmount IS NOT NULL LIMIT 1)
GROUP BY YEAR(CreatedDate), MONTH(CreatedDate)
ORDER BY YEAR(CreatedDate) DESC, MONTH(CreatedDate) DESC
LIMIT 12;

-- =============================================================================
-- 3. BACKUP ÖLÇÜSÜ HESABLAMASI
-- =============================================================================

SELECT 
    '💾 BACKUP ÖLÇÜSÜ HESABLAMASI' as section,
    '' as info,
    '' as size_mb,
    '' as estimated_time;

SELECT 
    '💾 BACKUP ÖLÇÜSÜ' as section,
    'Ümumi satış table-ları' as info,
    CONCAT(
        ROUND(SUM((data_length + index_length) / 1024 / 1024), 2), ' MB'
    ) as size_mb,
    CONCAT(
        'Təxmini ', 
        ROUND(SUM((data_length + index_length) / 1024 / 1024) / 100, 0), 
        ' dəqiqə'
    ) as estimated_time
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND (table_name LIKE 'trInvoice%' OR table_name LIKE 'trOrder%' OR 
     table_name LIKE 'trShipment%' OR table_name LIKE 'trPayment%' OR
     table_name LIKE 'tpInvoice%' OR table_name LIKE 'tpOrder%' OR
     table_name LIKE 'tpShipment%');

-- =============================================================================
-- 4. ADDIM-ADDIM İCRA PLANI
-- =============================================================================

SELECT 
    '📋 ADDIM-ADDIM İCRA PLANI' as section,
    '' as step,
    '' as command,
    '' as description;

SELECT 
    '📋 ADDIM 1' as section,
    'Təhlükəsizlik yoxlaması' as step,
    'SOURCE sales_safety_checks.sql;' as command,
    'Risk qiymətləndirməsi' as description

UNION ALL

SELECT 
    '📋 ADDIM 2',
    'Backup yaratmaq',
    'mysqldump -u user -p db trInvoice* trOrder* trShipment* > backup.sql',
    'Tam backup (MÜTLƏQ!)'

UNION ALL

SELECT 
    '📋 ADDIM 3',
    'Test environment-də sınaq',
    'CALL BatchDeleteSalesData(''2025-01-01'', 100, 2.0, 3);',
    'Kiçik test batch'

UNION ALL

SELECT 
    '📋 ADDIM 4',
    'Audit veriləri silmək',
    'CALL DeleteSalesAuditData(''2025-01-01'');',
    'Təhlükəsiz başlanğıc'

UNION ALL

SELECT 
    '📋 ADDIM 5',
    'Əsas silmə əməliyyatı',
    'CALL BatchDeleteSalesData(''2025-01-01'', 2000, 0.1, 500);',
    'Böyük batch silmə'

UNION ALL

SELECT 
    '📋 ADDIM 6',
    'Yoxlama və təmizlik',
    'SOURCE sales_post_deletion_check.sql;',
    'Nəticə yoxlaması';

-- =============================================================================
-- 5. PERFORMANCE OPTIMALLAŞDIRMASI
-- =============================================================================

-- Bu index-ləri yaradın (əgər yoxdursa):
SELECT 
    '⚡ PERFORMANCE OPTIMALLAŞDIRMASI' as section,
    'Index yaratmaq' as step,
    'CREATE INDEX idx_trInvoiceHeader_CreatedDate ON trInvoiceHeader(CreatedDate);' as command,
    'Silmə sürətini artırır' as description

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Index yaratmaq',
    'CREATE INDEX idx_trInvoiceLine_CreatedDate ON trInvoiceLine(CreatedDate);',
    'Silmə sürətini artırır'

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Index yaratmaq',
    'CREATE INDEX idx_trOrderHeader_CreatedDate ON trOrderHeader(CreatedDate);',
    'Silmə sürətini artırır'

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Index yaratmaq',
    'CREATE INDEX idx_trOrderLine_CreatedDate ON trOrderLine(CreatedDate);',
    'Silmə sürətini artırır'

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Statistika yeniləmək',
    'ANALYZE TABLE trInvoiceHeader, trInvoiceLine, trOrderHeader, trOrderLine;',
    'Query optimizer üçün';

-- =============================================================================
-- 6. MONITORING VƏ EMERGENCY
-- =============================================================================

SELECT 
    '📊 MONITORING VƏ EMERGENCY' as section,
    '' as step,
    '' as command,
    '' as description;

SELECT 
    '📊 MONITORING' as section,
    'Progress izləmək' as step,
    'SELECT * FROM sales_deletion_log ORDER BY deletion_time DESC LIMIT 10;' as command,
    'Real-time progress' as description

UNION ALL

SELECT 
    '📊 MONITORING',
    'Server yükü yoxlamaq',
    'SHOW PROCESSLIST;',
    'Aktiv query-lər'

UNION ALL

SELECT 
    '🚨 EMERGENCY',
    'Təcili dayandırma',
    'CALL StopSalesDeletion();',
    'Bütün silmə proseslərini dayandırır'

UNION ALL

SELECT 
    '🚨 EMERGENCY',
    'Rollback (əgər mümkündürsə)',
    'mysql -u user -p db < backup.sql',
    'Backup-dan bərpa';

-- =============================================================================
-- 7. TƏXMINI VAXT HESABLAMASI
-- =============================================================================

SELECT 
    '⏱️ TƏXMINI VAXT HESABLAMASI' as section,
    '' as operation,
    '' as estimated_time,
    '' as note;

SELECT 
    '⏱️ VAXT HESABLAMASI' as section,
    'Backup yaratmaq' as operation,
    '30-60 dəqiqə' as estimated_time,
    'Table ölçüsünə görə' as note

UNION ALL

SELECT 
    '⏱️ VAXT',
    'Audit veriləri silmək',
    '10-20 dəqiqə',
    'Nisbətən sürətli'

UNION ALL

SELECT 
    '⏱️ VAXT',
    'trInvoiceLine silmək',
    '2-4 saat',
    'Ən böyük table'

UNION ALL

SELECT 
    '⏱️ VAXT',
    'Digər table-lar',
    '1-2 saat',
    'Orta ölçülü table-lar'

UNION ALL

SELECT 
    '⏱️ VAXT',
    'Ümumi əməliyyat',
    '4-8 saat',
    'Gecə saatlarında icra edin';

-- =============================================================================
-- 8. SON XƏBƏRDARLIQLAR
-- =============================================================================

SELECT 
    '⚠️ SON XƏBƏRDARLIQLAR' as section,
    '' as warning,
    '' as action,
    '' as importance;

SELECT 
    '⚠️ XƏBƏRDARLIQ' as section,
    'Bu əməliyyat geri alına bilməz!' as warning,
    'Mütləq backup yaradın!' as action,
    'KRİTİK!' as importance

UNION ALL

SELECT 
    '⚠️ XƏBƏRDARLIQ',
    '2025-ci ildən əvvəl BÜTÜN satışlar silinəcək!',
    'Business team-lə razılaşdırın!',
    'ÇOX VACİB!'

UNION ALL

SELECT 
    '⚠️ XƏBƏRDARLIQ',
    'Maliyyə hesabatları təsirlənəcək!',
    'Accounting department-ə məlumat verin!',
    'VACİB!'

UNION ALL

SELECT 
    '⚠️ XƏBƏRDARLIQ',
    'Əməliyyat 4-8 saat çəkə bilər!',
    'Off-peak saatlarda (gecə) icra edin!',
    'PERFORMANCE!'

UNION ALL

SELECT 
    '⚠️ XƏBƏRDARLIQ',
    'Database lock-ları ola bilər!',
    'İstifadəçiləri xəbərdar edin!',
    'İSTİFADƏÇİ TƏSİRİ!';

-- =============================================================================
-- 9. HAZIR KOMANDALAR
-- =============================================================================

/*
-- HAZIR KOMANDALAR (kopyalayıb istifadə edin):

-- 1. Təhlükəsizlik yoxlaması:
SOURCE sales_safety_checks.sql;

-- 2. Backup yaratmaq:
mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine trShipmentHeader trShipmentLine trPaymentHeader trPaymentLine > sales_backup_2025_01_01.sql

-- 3. Test silmə:
CALL BatchDeleteSalesData('2025-01-01', 100, 2.0, 3);

-- 4. Audit veriləri silmək:
CALL DeleteSalesAuditData('2025-01-01');

-- 5. Əsas silmə əməliyyatı:
CALL BatchDeleteSalesData('2025-01-01', 2000, 0.1, 500);

-- 6. Progress monitor:
SELECT * FROM sales_deletion_log ORDER BY deletion_time DESC LIMIT 20;

-- 7. Təcili dayandırma:
CALL StopSalesDeletion();
*/
