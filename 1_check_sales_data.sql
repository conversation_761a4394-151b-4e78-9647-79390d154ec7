-- SQL SERVER ÜÇÜN SATIŞ VERİLƏRİ YOXLAMASI
-- 2025-01-01-ə qədər nə qədər veri silinəcəyini yoxlayır

-- =============================================================================
-- SATIŞ VERİLƏRİ STATİSTİKASI
-- =============================================================================

PRINT '=== SATIŞ VERİLƏRİ STATİSTİKASI ===';

-- Faktura veriləri
SELECT COUNT(*) as total_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_invoice_lines 
FROM trInvoiceLine 
WHERE CreatedDate < '2025-01-01';

-- <PERSON><PERSON><PERSON> veriləri
SELECT COUNT(*) as total_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_order_lines 
FROM trOrderLine 
WHERE CreatedDate < '2025-01-01';

-- Göndərmə veriləri
SELECT COUNT(*) as total_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_shipment_lines 
FROM trShipmentLine 
WHERE CreatedDate < '2025-01-01';

-- Ödəniş veriləri
SELECT COUNT(*) as total_payments 
FROM trPaymentHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_payment_lines 
FROM trPaymentLine 
WHERE CreatedDate < '2025-01-01';

-- Satış planı veriləri
SELECT COUNT(*) as total_sales_plans 
FROM trSalesPlan 
WHERE CreatedDate < '2025-01-01';

-- =============================================================================
-- ÜMUMI XÜLASƏ
-- =============================================================================

PRINT '=== ÜMUMI XÜLASƏ ===';

SELECT 
    'FAKTURA' as table_type,
    (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') as headers_to_delete,
    (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') as lines_to_delete
UNION ALL
SELECT 
    'SİFARİŞ',
    (SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trOrderLine WHERE CreatedDate < '2025-01-01')
UNION ALL
SELECT 
    'GÖNDƏRMƏ',
    (SELECT COUNT(*) FROM trShipmentHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trShipmentLine WHERE CreatedDate < '2025-01-01')
UNION ALL
SELECT 
    'ÖDƏNİŞ',
    (SELECT COUNT(*) FROM trPaymentHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trPaymentLine WHERE CreatedDate < '2025-01-01');

-- =============================================================================
-- TARİX ARALIQ ANALİZİ
-- =============================================================================

PRINT '=== TARİX ARALIQ ANALİZİ ===';

-- Ən köhnə və ən yeni fakturaları göstər
SELECT 
    MIN(CreatedDate) as oldest_invoice,
    MAX(CreatedDate) as newest_invoice,
    COUNT(*) as total_invoices
FROM trInvoiceHeader;

-- Aylıq paylanma (son 12 ay)
SELECT 
    YEAR(CreatedDate) as year,
    MONTH(CreatedDate) as month,
    COUNT(*) as invoice_count
FROM trInvoiceHeader
WHERE CreatedDate < '2025-01-01'
GROUP BY YEAR(CreatedDate), MONTH(CreatedDate)
ORDER BY YEAR(CreatedDate) DESC, MONTH(CreatedDate) DESC;

-- =============================================================================
-- XƏBƏRDARLIQLAR
-- =============================================================================

PRINT '=== XƏBƏRDARLIQLAR ===';

SELECT 
    'DİQQƏT!' as warning_type,
    'Bu əməliyyat 2025-01-01-ə qədər BÜTÜN satışları siləcək!' as warning_message
UNION ALL
SELECT 
    'BACKUP',
    'Mütləq backup yaradın!'
UNION ALL
SELECT 
    'TEST',
    'Əvvəl test environment-də sınayın!'
UNION ALL
SELECT 
    'VAXT',
    'Bu əməliyyat 2-6 saat çəkə bilər!'
UNION ALL
SELECT 
    'İSTİFADƏÇİLƏR',
    'İstifadəçiləri xəbərdar edin!';

PRINT 'YOXLAMA TAMAMLANDI - NƏTİCƏLƏRİ ANALİZ EDİN!';
