-- SATIŞ VERİLƏRİNİ SİLMƏK ÜÇÜN PROSEDURU İCRA ETMƏK

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [YourDatabaseName] 
-- TO DISK = 'C:\Backup\sales_backup_2025.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- Sİ<PERSON>Ə PROSEDURUNU İCRA ETMƏK
-- =============================================================================

-- Kiçik veri üçün (< 100,000 record):
EXEC DeleteSalesDataSQLServer @CutoffDate = '2025-01-01';

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT COUNT(*) as remaining_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

-- Orphan record yoxlaması:
SELECT COUNT(*) as orphan_invoice_lines
FROM trInvoiceLine il
LEFT JOIN trInvoiceHeader ih ON il.InvoiceHeaderID = ih.InvoiceHeaderID
WHERE ih.InvoiceHeaderID IS NULL;

SELECT COUNT(*) as orphan_order_lines
FROM trOrderLine ol
LEFT JOIN trOrderHeader oh ON ol.OrderHeaderID = oh.OrderHeaderID
WHERE oh.OrderHeaderID IS NULL;

PRINT 'SİLMƏ ƏMƏLIYYATI TAMAMLANDI - NƏTİCƏLƏRİ YOXLAYIN!';
