-- TƏHLÜKƏSİZ MASTER DATA SAXLAMAQLA TRANSACTION VERİLƏRİNİN SİLMƏSİ
-- FK constraint problemlərini həll edən versiya

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
BACKUP DATABASE [Test_DB] 
TO DISK = 'C:\Backup\safe_master_data_cleanup_2025.bak'
WITH FORMAT, INIT;

PRINT '✅ Backup yaradıldı!';

-- =============================================================================
-- TƏHLÜKƏSİZ SİLMƏ PROSEDURU (FK CONSTRAINT-LƏRİ DİSABLE ETMƏKLƏ)
-- =============================================================================

USE [Test_DB];

BEGIN TRANSACTION;

BEGIN TRY
    PRINT '';
    PRINT '🔄 TƏHLÜKƏSİZ TRANSACTION VERİLƏRİ SİLMƏ BAŞLAYIR...';
    PRINT '🔒 FK Constraint-lər disable edilir...';
    
    -- =============================================================================
    -- ADDIM 1: BÜTÜN FK CONSTRAINT-LƏRİ DISABLE ETMƏK
    -- =============================================================================
    
    -- Bütün FK constraint-ləri disable et
    DECLARE @DisableFK NVARCHAR(MAX) = '';
    
    SELECT @DisableFK = @DisableFK + 
        'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
        'NOCHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
    FROM sys.foreign_keys fk;
    
    IF LEN(@DisableFK) > 0
    BEGIN
        EXEC sp_executesql @DisableFK;
        PRINT '   ✓ FK Constraint-lər disable edildi';
    END
    
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 2: TRANSACTION VƏ TEMPORARY TABLE-LARI SİLMƏK
    -- =============================================================================
    
    PRINT '🗑️ ADDIM 2: TRANSACTION VƏ TEMPORARY VERİLƏR SİLİNİR...';
    
    -- Bütün tr* və tp* və au* table-larını tap və sil
    DECLARE @DeleteSQL NVARCHAR(MAX) = '';
    DECLARE @TableName NVARCHAR(128);
    DECLARE @RowCount INT;
    
    -- Transaction, Temporary və Audit table-ları üçün cursor
    DECLARE table_cursor CURSOR FOR
    SELECT TABLE_NAME 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_TYPE = 'BASE TABLE'
      AND (TABLE_NAME LIKE 'tr%' OR TABLE_NAME LIKE 'tp%' OR TABLE_NAME LIKE 'au%')
      AND TABLE_SCHEMA = 'dbo'
    ORDER BY 
        CASE 
            WHEN TABLE_NAME LIKE '%Line' OR TABLE_NAME LIKE '%Extension' THEN 1  -- Child tables əvvəl
            WHEN TABLE_NAME LIKE '%Header' THEN 2                                -- Header tables sonra
            ELSE 3                                                               -- Digərləri ən son
        END,
        TABLE_NAME;
    
    OPEN table_cursor;
    FETCH NEXT FROM table_cursor INTO @TableName;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @DeleteSQL = 'DELETE FROM [dbo].[' + @TableName + '];';
        
        BEGIN TRY
            EXEC sp_executesql @DeleteSQL;
            SET @RowCount = @@ROWCOUNT;
            PRINT '   ✓ ' + @TableName + ' silindi: ' + CAST(@RowCount AS VARCHAR(10)) + ' record';
        END TRY
        BEGIN CATCH
            PRINT '   ⚠️ ' + @TableName + ' silinərkən xəta: ' + ERROR_MESSAGE();
        END CATCH
        
        FETCH NEXT FROM table_cursor INTO @TableName;
    END
    
    CLOSE table_cursor;
    DEALLOCATE table_cursor;
    
    PRINT '   ✅ Transaction və temporary verilər silindi!';
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 3: IDENTITY SEED-LƏRİ RESET ETMƏK
    -- =============================================================================
    
    PRINT '🔄 ADDIM 3: IDENTITY SEED-LƏR RESET EDİLİR...';
    
    -- Identity column-u olan tr* table-ları üçün seed reset et
    DECLARE @ResetSQL NVARCHAR(MAX) = '';
    DECLARE @IdentityTable NVARCHAR(128);
    
    DECLARE identity_cursor CURSOR FOR
    SELECT DISTINCT t.name
    FROM sys.tables t
    INNER JOIN sys.identity_columns ic ON t.object_id = ic.object_id
    WHERE t.name LIKE 'tr%'
      AND t.is_ms_shipped = 0;
    
    OPEN identity_cursor;
    FETCH NEXT FROM identity_cursor INTO @IdentityTable;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        BEGIN TRY
            SET @ResetSQL = 'DBCC CHECKIDENT (''' + @IdentityTable + ''', RESEED, 0);';
            EXEC sp_executesql @ResetSQL;
            PRINT '   ✓ ' + @IdentityTable + ' identity seed reset edildi';
        END TRY
        BEGIN CATCH
            PRINT '   ⚠️ ' + @IdentityTable + ' identity reset xətası: ' + ERROR_MESSAGE();
        END CATCH
        
        FETCH NEXT FROM identity_cursor INTO @IdentityTable;
    END
    
    CLOSE identity_cursor;
    DEALLOCATE identity_cursor;
    
    PRINT '   ✅ Identity seed-lər reset edildi!';
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 4: FK CONSTRAINT-LƏRİ YENİDƏN ENABLE ETMƏK
    -- =============================================================================
    
    PRINT '🔓 ADDIM 4: FK Constraint-lər yenidən enable edilir...';
    
    -- Bütün FK constraint-ləri enable et
    DECLARE @EnableFK NVARCHAR(MAX) = '';
    
    SELECT @EnableFK = @EnableFK + 
        'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
        'WITH CHECK CHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
    FROM sys.foreign_keys fk;
    
    IF LEN(@EnableFK) > 0
    BEGIN
        EXEC sp_executesql @EnableFK;
        PRINT '   ✓ FK Constraint-lər enable edildi';
    END
    
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 5: NƏTİCƏ YOXLAMASI
    -- =============================================================================
    
    PRINT '📊 ADDIM 5: NƏTİCƏ YOXLANIR...';
    
    -- Master data sayı
    DECLARE @MasterCount INT = 0;
    SELECT @MasterCount = 
        ISNULL((SELECT COUNT(*) FROM cdCurrAcc), 0) +
        ISNULL((SELECT COUNT(*) FROM cdItem), 0) +
        ISNULL((SELECT COUNT(*) FROM cdGLAcc), 0) +
        ISNULL((SELECT COUNT(*) FROM bsCompany), 0);
    
    -- Transaction data sayı
    DECLARE @TransactionCount INT = 0;
    SELECT @TransactionCount = 
        ISNULL((SELECT COUNT(*) FROM trInvoiceHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trOrderHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trPaymentHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trBankHeader), 0);
    
    PRINT '   Master Data Records: ' + CAST(@MasterCount AS VARCHAR(10));
    PRINT '   Transaction Data Records: ' + CAST(@TransactionCount AS VARCHAR(10));
    PRINT '';
    
    COMMIT TRANSACTION;
    
    PRINT '🎉 ƏMƏLIYYAT UĞURLA TAMAMLANDI!';
    PRINT '';
    PRINT '✅ NƏTİCƏ:';
    PRINT '   - Master data saxlanıldı (' + CAST(@MasterCount AS VARCHAR(10)) + ' records)';
    PRINT '   - Transaction data silindi (' + CAST(@TransactionCount AS VARCHAR(10)) + ' qalan)';
    PRINT '   - FK constraint-lər restore edildi';
    PRINT '';
    PRINT '📋 SONRAKI ADDIMLAR:';
    PRINT '   1. 16_check_master_data_results.sql icra edin';
    PRINT '   2. Database performansını test edin';
    PRINT '   3. İstifadəçilərə məlumat verin';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    PRINT '';
    PRINT '❌ XƏTA BAŞ VERDİ - ƏMƏLIYYAT GERİ ALINDI!';
    PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
    PRINT 'Xəta sətiri: ' + CAST(ERROR_LINE() AS VARCHAR(10));
    PRINT '';
    
    -- FK constraint-ləri yenidən enable etməyə çalış
    BEGIN TRY
        PRINT '🔄 FK Constraint-lər restore edilməyə çalışılır...';
        DECLARE @RestoreFK NVARCHAR(MAX) = '';
        
        SELECT @RestoreFK = @RestoreFK + 
            'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
            'WITH CHECK CHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
        FROM sys.foreign_keys fk;
        
        IF LEN(@RestoreFK) > 0
        BEGIN
            EXEC sp_executesql @RestoreFK;
            PRINT '✅ FK Constraint-lər restore edildi';
        END
    END TRY
    BEGIN CATCH
        PRINT '⚠️ FK Constraint restore xətası: ' + ERROR_MESSAGE();
    END CATCH
    
    PRINT '';
    PRINT '🔄 Backup-dan restore etmək üçün:';
    PRINT 'RESTORE DATABASE [Test_DB] FROM DISK = ''C:\Backup\safe_master_data_cleanup_2025.bak'' WITH REPLACE;';
    
    THROW;
END CATCH;
