-- SQL SERVER üçün satış veriləri silmə skripti
-- 2025-01-01-ə qədər satış veriləri silir

-- =============================================================================
-- 1. ƏVVƏL YOXLAMA EDİN (SQL SERVER SİNTAKSI)
-- =============================================================================

-- Nə qədər veri silinəcəyini yoxlayın:
SELECT COUNT(*) as total_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as total_payments 
FROM trPaymentHeader 
WHERE CreatedDate < '2025-01-01';

-- =============================================================================
-- 2. SQL SERVER ÜÇÜN SİLMƏ PROSEDURU
-- =============================================================================

CREATE PROCEDURE DeleteSalesDataSQLServer
    @CutoffDate DATE = '2025-01-01'
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        PRINT 'Satış veriləri silmə başladı: ' + CAST(@CutoffDate AS VARCHAR(10));
        
        -- =============================================================================
        -- ADDIM 1: DETAIL VƏ EXTENSION TABLE-LAR (ƏN DƏRİN CHILD-LAR)
        -- =============================================================================
        
        -- Invoice detail table-ları
        DELETE FROM trInvoiceLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineReportedSales WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineReportedSales silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineSubsequentDeliveryOrders WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSubsequentDeliveryOrders silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order detail table-ları
        DELETE FROM trOrderLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderOpticalProductLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProductLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment detail table-ları
        DELETE FROM trShipmentLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment detail table-ları
        DELETE FROM trPaymentLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Sales plan detail table-ları
        DELETE FROM trSalesPlanProductQty WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProductQty silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== DETAIL TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 2: SUM TABLE-LAR
        -- =============================================================================
        
        DELETE FROM trInvoiceLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== SUM TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 3: LINE TABLE-LAR (CHILD TABLE-LAR)
        -- =============================================================================
        
        -- Invoice line table-ları
        DELETE FROM trInvoiceLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order line table-ları
        DELETE FROM trOrderLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment line table-ları
        DELETE FROM trShipmentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment line table-ları
        DELETE FROM trPaymentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order advance payments
        DELETE FROM trOrderAdvancePayments WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderAdvancePayments silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Sales plan products
        DELETE FROM trSalesPlanProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlanChannel WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanChannel silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order optical products
        DELETE FROM trOrderOpticalProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order payment plan
        DELETE FROM trOrderPaymentPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderPaymentPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== LINE TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 4: HEADER TABLE-LAR (PARENT TABLE-LAR)
        -- =============================================================================
        
        -- Invoice header
        DELETE FROM trInvoiceHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order header
        DELETE FROM trOrderHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment header
        DELETE FROM trShipmentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment header
        DELETE FROM trPaymentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Sales plan header
        DELETE FROM trSalesPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== HEADER TABLE-LAR SİLİNDİ ===';
        
        COMMIT TRANSACTION;
        PRINT '🎯 2025-01-01-Ə QƏDƏR BÜTÜN SATIŞ VERİLƏRİ UĞURLA SİLİNDİ!';
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        PRINT 'XƏTA BAŞ VERDİ - ƏMƏLIYYAT GERİ ALINDI!';
        PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;

-- =============================================================================
-- 3. BATCH SİLMƏ PROSEDURU (BÖYÜK VERİ ÜÇÜN)
-- =============================================================================

CREATE PROCEDURE DeleteSalesDataBatchSQLServer
    @CutoffDate DATE = '2025-01-01',
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsDeleted INT = 1;
    DECLARE @TotalDeleted INT = 0;
    
    PRINT 'Batch silmə başladı: ' + CAST(@CutoffDate AS VARCHAR(10)) + ', Batch size: ' + CAST(@BatchSize AS VARCHAR(10));
    
    -- trInvoiceLine batch silmə (ən böyük table)
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trInvoiceLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trInvoiceLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100'; -- 100ms fasilə
    END;
    
    -- trOrderLine batch silmə
    SET @RowsDeleted = 1;
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trOrderLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trOrderLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100';
    END;
    
    -- trShipmentLine batch silmə
    SET @RowsDeleted = 1;
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trShipmentLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trShipmentLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100';
    END;
    
    -- Qalan table-ları sadə şəkildə sil
    BEGIN TRANSACTION;
    
    DELETE FROM trInvoiceHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trOrderHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trShipmentHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trPaymentHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trSalesPlan WHERE CreatedDate < @CutoffDate;
    PRINT 'trSalesPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    COMMIT TRANSACTION;
    
    PRINT '🎯 BATCH SATIŞ SİLMƏ TAMAMLANDI!';
END;

-- =============================================================================
-- 4. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

/*
-- 1. Əvvəl yoxlama edin:
SELECT COUNT(*) as total_invoices FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';
SELECT COUNT(*) as total_orders FROM trOrderHeader WHERE CreatedDate < '2025-01-01';

-- 2. Backup yaradın (SQL Server):
-- BACKUP DATABASE [YourDatabaseName] TO DISK = 'C:\Backup\sales_backup_2025.bak'

-- 3. Kiçik veri üçün:
EXEC DeleteSalesDataSQLServer @CutoffDate = '2025-01-01';

-- 4. Böyük veri üçün:
EXEC DeleteSalesDataBatchSQLServer @CutoffDate = '2025-01-01', @BatchSize = 1000;

-- 5. Silmədən sonra yoxlama:
SELECT COUNT(*) as remaining_invoices FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';
*/
