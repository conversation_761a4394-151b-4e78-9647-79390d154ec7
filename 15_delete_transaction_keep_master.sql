-- MASTER DATA SAXLAMAQLA TRANSACTION VERİLƏRİNİN SİLMƏSİ
-- Transaction verilər silinir, Master data (cd*, bs*, df*, pr*) saxlanır

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
BACKUP DATABASE [YourDatabaseName] 
TO DISK = 'C:\Backup\before_master_data_cleanup_2025.bak'
WITH FORMAT, INIT;

PRINT '✅ Backup yaradıldı!';

-- =============================================================================
-- ƏSAS SİLMƏ PROSEDURU
-- =============================================================================

USE [YourDatabaseName];

BEGIN TRANSACTION;

BEGIN TRY
    PRINT '';
    PRINT '🔄 TRANSACTION VERİLƏRİ SİLİNİR (MASTER DATA SAXLANIR)...';
    PRINT '📊 Başlanğıc vəziyyət yoxlanılır...';
    
    -- Başlanğıc statistika
    DECLARE @InitialTransactionCount INT = 0;
    DECLARE @InitialMasterCount INT = 0;
    
    SELECT @InitialTransactionCount = 
        (SELECT COUNT(*) FROM trInvoiceHeader) +
        (SELECT COUNT(*) FROM trOrderHeader) +
        (SELECT COUNT(*) FROM trPaymentHeader) +
        (SELECT COUNT(*) FROM trBankHeader) +
        (SELECT COUNT(*) FROM trCashHeader);
        
    SELECT @InitialMasterCount = 
        (SELECT COUNT(*) FROM cdCurrAcc) +
        (SELECT COUNT(*) FROM cdItem) +
        (SELECT COUNT(*) FROM cdGLAcc);
    
    PRINT 'Başlanğıc Transaction records: ' + CAST(@InitialTransactionCount AS VARCHAR(10));
    PRINT 'Başlanğıc Master records: ' + CAST(@InitialMasterCount AS VARCHAR(10));
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 1: AUDIT VƏ LOG TABLE-LARI (au*)
    -- =============================================================================
    
    PRINT '🗂️ ADDIM 1: AUDIT VƏ LOG VERİLƏRİ SİLİNİR...';
    
    -- Audit line table-ları əvvəl (child tables)
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'auGettingDataTransferTraceLine')
    BEGIN
        DELETE FROM auGettingDataTransferTraceLine;
        PRINT '   ✓ auGettingDataTransferTraceLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Audit header table-ları (parent tables)
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'auGettingDataTransferTraceHeader')
    BEGIN
        DELETE FROM auGettingDataTransferTraceHeader;
        PRINT '   ✓ auGettingDataTransferTraceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    PRINT '   ✅ Audit verilər silindi!';
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 2: TRANSACTION LINE TABLE-LARI (tr*Line) - CHILD TABLES ƏVVƏL
    -- =============================================================================
    
    PRINT '📋 ADDIM 2: TRANSACTION LINE VERİLƏRİ SİLİNİR...';
    
    -- Invoice lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceLine')
    BEGIN
        DELETE FROM trInvoiceLine;
        PRINT '   ✓ trInvoiceLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Order lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderLine')
    BEGIN
        DELETE FROM trOrderLine;
        PRINT '   ✓ trOrderLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Shipment lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentLine')
    BEGIN
        DELETE FROM trShipmentLine;
        PRINT '   ✓ trShipmentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Payment lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentLine')
    BEGIN
        DELETE FROM trPaymentLine;
        PRINT '   ✓ trPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Bank lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankLine')
    BEGIN
        DELETE FROM trBankLine;
        PRINT '   ✓ trBankLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Cash lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashLine')
    BEGIN
        DELETE FROM trCashLine;
        PRINT '   ✓ trCashLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Cheque lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trChequeLine')
    BEGIN
        DELETE FROM trChequeLine;
        PRINT '   ✓ trChequeLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Journal lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalLine')
    BEGIN
        DELETE FROM trJournalLine;
        PRINT '   ✓ trJournalLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Credit card lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCreditCardPaymentLine')
    BEGIN
        DELETE FROM trCreditCardPaymentLine;
        PRINT '   ✓ trCreditCardPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Debit lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trDebitLine')
    BEGIN
        DELETE FROM trDebitLine;
        PRINT '   ✓ trDebitLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    PRINT '   ✅ Bütün transaction line verilər silindi!';
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 3: TRANSACTION HEADER TABLE-LARI (tr*Header) - PARENT TABLES SONRA
    -- =============================================================================
    
    PRINT '📄 ADDIM 3: TRANSACTION HEADER VERİLƏRİ SİLİNİR...';
    
    -- Invoice headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceHeader')
    BEGIN
        DELETE FROM trInvoiceHeader;
        PRINT '   ✓ trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Order headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderHeader')
    BEGIN
        DELETE FROM trOrderHeader;
        PRINT '   ✓ trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Shipment headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentHeader')
    BEGIN
        DELETE FROM trShipmentHeader;
        PRINT '   ✓ trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Payment headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentHeader')
    BEGIN
        DELETE FROM trPaymentHeader;
        PRINT '   ✓ trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Bank headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankHeader')
    BEGIN
        DELETE FROM trBankHeader;
        PRINT '   ✓ trBankHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Cash headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashHeader')
    BEGIN
        DELETE FROM trCashHeader;
        PRINT '   ✓ trCashHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Cheque headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trChequeHeader')
    BEGIN
        DELETE FROM trChequeHeader;
        PRINT '   ✓ trChequeHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Journal headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalHeader')
    BEGIN
        DELETE FROM trJournalHeader;
        PRINT '   ✓ trJournalHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Credit card headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCreditCardPaymentHeader')
    BEGIN
        DELETE FROM trCreditCardPaymentHeader;
        PRINT '   ✓ trCreditCardPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    -- Debit headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trDebitHeader')
    BEGIN
        DELETE FROM trDebitHeader;
        PRINT '   ✓ trDebitHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END
    
    PRINT '   ✅ Bütün transaction header verilər silindi!';
    PRINT '';

    -- =============================================================================
    -- ADDIM 4: DİGƏR TRANSACTION TABLE-LARI
    -- =============================================================================

    PRINT '📦 ADDIM 4: DİGƏR TRANSACTION VERİLƏRİ SİLİNİR...';

    -- Stock records
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trStock')
    BEGIN
        DELETE FROM trStock;
        PRINT '   ✓ trStock silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Price list lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPriceListLine')
    BEGIN
        DELETE FROM trPriceListLine;
        PRINT '   ✓ trPriceListLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Vendor price list lines
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trVendorPriceListLine')
    BEGIN
        DELETE FROM trVendorPriceListLine;
        PRINT '   ✓ trVendorPriceListLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Inner lines (scrap reasons)
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInnerLine')
    BEGIN
        DELETE FROM trInnerLine;
        PRINT '   ✓ trInnerLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Inner headers
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInnerHeader')
    BEGIN
        DELETE FROM trInnerHeader;
        PRINT '   ✓ trInnerHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Bad debt transaction results
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBadDebtTransLineResult')
    BEGIN
        DELETE FROM trBadDebtTransLineResult;
        PRINT '   ✓ trBadDebtTransLineResult silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    PRINT '   ✅ Digər transaction verilər silindi!';
    PRINT '';

    -- =============================================================================
    -- ADDIM 5: TEMPORARY/PROCESS TABLE-LARI (tp*)
    -- =============================================================================

    PRINT '⏳ ADDIM 5: TEMPORARY VƏ PROCESS VERİLƏRİ SİLİNİR...';

    -- Passport and boarding info
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpInvoicePassportAndBoardingInfo')
    BEGIN
        DELETE FROM tpInvoicePassportAndBoardingInfo;
        PRINT '   ✓ tpInvoicePassportAndBoardingInfo silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Proposal line confirmation status
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpProposalLineConfirmationStatus')
    BEGIN
        DELETE FROM tpProposalLineConfirmationStatus;
        PRINT '   ✓ tpProposalLineConfirmationStatus silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    -- Support resolve
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpSupportResolve')
    BEGIN
        DELETE FROM tpSupportResolve;
        PRINT '   ✓ tpSupportResolve silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    END

    PRINT '   ✅ Temporary verilər silindi!';
    PRINT '';

    -- =============================================================================
    -- ADDIM 6: IDENTITY SEED-LƏRİ RESET ETMƏK
    -- =============================================================================

    PRINT '🔄 ADDIM 6: IDENTITY SEED-LƏR RESET EDİLİR...';

    -- Transaction header tables identity reset
    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trInvoiceHeader')
        DBCC CHECKIDENT ('trInvoiceHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trOrderHeader')
        DBCC CHECKIDENT ('trOrderHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trShipmentHeader')
        DBCC CHECKIDENT ('trShipmentHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trPaymentHeader')
        DBCC CHECKIDENT ('trPaymentHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trBankHeader')
        DBCC CHECKIDENT ('trBankHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trCashHeader')
        DBCC CHECKIDENT ('trCashHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trChequeHeader')
        DBCC CHECKIDENT ('trChequeHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trJournalHeader')
        DBCC CHECKIDENT ('trJournalHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trCreditCardPaymentHeader')
        DBCC CHECKIDENT ('trCreditCardPaymentHeader', RESEED, 0);

    IF EXISTS (SELECT 1 FROM sys.identity_columns ic INNER JOIN sys.tables t ON ic.object_id = t.object_id WHERE t.name = 'trDebitHeader')
        DBCC CHECKIDENT ('trDebitHeader', RESEED, 0);

    PRINT '   ✅ Identity seed-lər reset edildi!';
    PRINT '';

    -- =============================================================================
    -- SON YOXLAMA VƏ NƏTİCƏ
    -- =============================================================================

    PRINT '📊 SON YOXLAMA VƏ NƏTİCƏ...';

    -- Son statistika
    DECLARE @FinalTransactionCount INT = 0;
    DECLARE @FinalMasterCount INT = 0;

    SELECT @FinalTransactionCount =
        ISNULL((SELECT COUNT(*) FROM trInvoiceHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trOrderHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trPaymentHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trBankHeader), 0) +
        ISNULL((SELECT COUNT(*) FROM trCashHeader), 0);

    SELECT @FinalMasterCount =
        ISNULL((SELECT COUNT(*) FROM cdCurrAcc), 0) +
        ISNULL((SELECT COUNT(*) FROM cdItem), 0) +
        ISNULL((SELECT COUNT(*) FROM cdGLAcc), 0);

    PRINT 'Son Transaction records: ' + CAST(@FinalTransactionCount AS VARCHAR(10));
    PRINT 'Son Master records: ' + CAST(@FinalMasterCount AS VARCHAR(10));
    PRINT '';

    COMMIT TRANSACTION;

    PRINT '🎉 ƏMƏLIYYAT UĞURLA TAMAMLANDI!';
    PRINT '';
    PRINT '✅ SAXLANILAN MASTER DATA:';
    PRINT '   - Müştəri məlumatları (cdCurrAcc)';
    PRINT '   - Məhsul məlumatları (cdItem)';
    PRINT '   - Hesab planı (cdGLAcc)';
    PRINT '   - Sistem konfiqurasiyası (bs*, df*)';
    PRINT '';
    PRINT '🗑️ SİLİNƏN TRANSACTION DATA:';
    PRINT '   - Bütün satış/alış tranzaksiyaları (tr*)';
    PRINT '   - Ödəniş məlumatları';
    PRINT '   - Log və audit məlumatları (au*)';
    PRINT '   - Temporary məlumatlar (tp*)';
    PRINT '';
    PRINT '⚠️ UNUTMAYIN:';
    PRINT '   - Backup faylını təhlükəsiz yerdə saxlayın';
    PRINT '   - Sistem performansını test edin';
    PRINT '   - İstifadəçilərə məlumat verin';

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT '';
    PRINT '❌ XƏTA BAŞ VERDİ - ƏMƏLIYYAT GERİ ALINDI!';
    PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
    PRINT 'Xəta sətiri: ' + CAST(ERROR_LINE() AS VARCHAR(10));
    PRINT '';
    PRINT '🔄 Backup-dan restore etmək üçün:';
    PRINT 'RESTORE DATABASE [YourDatabaseName] FROM DISK = ''C:\Backup\before_master_data_cleanup_2025.bak'' WITH REPLACE;';
    THROW;
END CATCH;
