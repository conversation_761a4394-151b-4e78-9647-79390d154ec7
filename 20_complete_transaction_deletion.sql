-- TRANSACTION SİLMƏ ƏMƏLİYYATINI TAMAMLAMAQ
-- Database köçürüldükdən sonra transaction silmə əməliyyatını tamamlayır

-- =============================================================================
-- DATABASE VƏZİYYƏTİNİ YOXLAMAQ
-- =============================================================================

USE [Test_DB];
GO

PRINT '🔍 Database vəziyyəti yoxlanır...';
PRINT '';

-- Mövcud transaction data sayı
SELECT 
    'MÖVCUD TRANSACTION DATA' as info,
    'trInvoiceHeader' as table_name,
    COUNT(*) as record_count
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceHeader')

UNION ALL

SELECT 
    'MÖVCUD TRANSACTION DATA',
    'trOrderHeader',
    COUNT(*)
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderHeader')

UNION ALL

SELECT 
    'MÖVCUD TRANSACTION DATA',
    'trPaymentHeader',
    COUNT(*)
FROM trPaymentHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentHeader')

UNION ALL

SELECT 
    'MÖVCUD TRANSACTION DATA',
    'trBankHeader',
    COUNT(*)
FROM trBankHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankHeader')

UNION ALL

SELECT 
    'MÖVCUD TRANSACTION DATA',
    'trCashHeader',
    COUNT(*)
FROM trCashHeader
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashHeader');

PRINT '';

-- Master data sayı (saxlanmalı)
SELECT 
    'MASTER DATA (SAXLANMALI)' as info,
    'cdCurrAcc' as table_name,
    COUNT(*) as record_count
FROM cdCurrAcc
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdCurrAcc')

UNION ALL

SELECT 
    'MASTER DATA (SAXLANMALI)',
    'cdItem',
    COUNT(*)
FROM cdItem
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdItem')

UNION ALL

SELECT 
    'MASTER DATA (SAXLANMALI)',
    'cdGLAcc',
    COUNT(*)
FROM cdGLAcc
WHERE EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'cdGLAcc');

PRINT '';
PRINT '=============================================================================';
PRINT '';

-- =============================================================================
-- TRANSACTION SİLMƏ ƏMƏLİYYATINI TAMAMLAMAQ
-- =============================================================================

PRINT '🗑️ TRANSACTION SİLMƏ ƏMƏLİYYATI BAŞLAYIR...';
PRINT '';

BEGIN TRANSACTION;

BEGIN TRY
    -- =============================================================================
    -- ADDIM 1: FK CONSTRAINT-LƏRİ DISABLE ETMƏK
    -- =============================================================================
    
    PRINT '🔒 FK Constraint-lər disable edilir...';
    
    DECLARE @DisableFK NVARCHAR(MAX) = '';
    
    SELECT @DisableFK = @DisableFK + 
        'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
        'NOCHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
    FROM sys.foreign_keys fk;
    
    IF LEN(@DisableFK) > 0
    BEGIN
        EXEC sp_executesql @DisableFK;
        PRINT '   ✓ FK Constraint-lər disable edildi';
    END
    
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 2: TRANSACTION TABLE-LARINI SİLMƏK
    -- =============================================================================
    
    PRINT '🗂️ TRANSACTION VERİLƏR SİLİNİR...';
    
    -- Extension və child table-ları əvvəl
    DECLARE @DeletedCount INT = 0;
    
    -- tpInvoiceLineExtension (ən child)
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpInvoiceLineExtension')
    BEGIN
        DELETE FROM tpInvoiceLineExtension;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ tpInvoiceLineExtension silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    -- Digər extension table-lar
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpOrderLineExtension')
    BEGIN
        DELETE FROM tpOrderLineExtension;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ tpOrderLineExtension silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    -- Transaction line table-ları
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceLine')
    BEGIN
        DELETE FROM trInvoiceLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trInvoiceLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderLine')
    BEGIN
        DELETE FROM trOrderLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trOrderLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentLine')
    BEGIN
        DELETE FROM trShipmentLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trShipmentLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentLine')
    BEGIN
        DELETE FROM trPaymentLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trPaymentLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankLine')
    BEGIN
        DELETE FROM trBankLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trBankLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashLine')
    BEGIN
        DELETE FROM trCashLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trCashLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trChequeLine')
    BEGIN
        DELETE FROM trChequeLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trChequeLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalLine')
    BEGIN
        DELETE FROM trJournalLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trJournalLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCreditCardPaymentLine')
    BEGIN
        DELETE FROM trCreditCardPaymentLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trCreditCardPaymentLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trDebitLine')
    BEGIN
        DELETE FROM trDebitLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trDebitLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    PRINT '';
    PRINT '📄 HEADER TABLE-LAR SİLİNİR...';
    
    -- Transaction header table-ları
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trInvoiceHeader')
    BEGIN
        DELETE FROM trInvoiceHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trInvoiceHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trOrderHeader')
    BEGIN
        DELETE FROM trOrderHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trOrderHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trShipmentHeader')
    BEGIN
        DELETE FROM trShipmentHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trShipmentHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trPaymentHeader')
    BEGIN
        DELETE FROM trPaymentHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trPaymentHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trBankHeader')
    BEGIN
        DELETE FROM trBankHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trBankHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCashHeader')
    BEGIN
        DELETE FROM trCashHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trCashHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trChequeHeader')
    BEGIN
        DELETE FROM trChequeHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trChequeHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trJournalHeader')
    BEGIN
        DELETE FROM trJournalHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trJournalHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trCreditCardPaymentHeader')
    BEGIN
        DELETE FROM trCreditCardPaymentHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trCreditCardPaymentHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trDebitHeader')
    BEGIN
        DELETE FROM trDebitHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trDebitHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    -- Digər transaction table-ları
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'trStock')
    BEGIN
        DELETE FROM trStock;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ trStock silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 3: AUDIT VƏ TEMPORARY TABLE-LAR
    -- =============================================================================
    
    PRINT '🗂️ AUDIT VƏ TEMPORARY VERİLƏR SİLİNİR...';
    
    -- Audit table-ları
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'auGettingDataTransferTraceLine')
    BEGIN
        DELETE FROM auGettingDataTransferTraceLine;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ auGettingDataTransferTraceLine silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'auGettingDataTransferTraceHeader')
    BEGIN
        DELETE FROM auGettingDataTransferTraceHeader;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ auGettingDataTransferTraceHeader silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    -- Temporary table-ları
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpInvoicePassportAndBoardingInfo')
    BEGIN
        DELETE FROM tpInvoicePassportAndBoardingInfo;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ tpInvoicePassportAndBoardingInfo silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpProposalLineConfirmationStatus')
    BEGIN
        DELETE FROM tpProposalLineConfirmationStatus;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ tpProposalLineConfirmationStatus silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'tpSupportResolve')
    BEGIN
        DELETE FROM tpSupportResolve;
        SET @DeletedCount = @@ROWCOUNT;
        PRINT '   ✓ tpSupportResolve silindi: ' + CAST(@DeletedCount AS VARCHAR(10)) + ' record';
    END
    
    PRINT '';
    
    -- =============================================================================
    -- ADDIM 4: FK CONSTRAINT-LƏRİ ENABLE ETMƏK
    -- =============================================================================
    
    PRINT '🔓 FK Constraint-lər enable edilir...';
    
    DECLARE @EnableFK NVARCHAR(MAX) = '';
    
    SELECT @EnableFK = @EnableFK + 
        'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
        'WITH CHECK CHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
    FROM sys.foreign_keys fk;
    
    IF LEN(@EnableFK) > 0
    BEGIN
        EXEC sp_executesql @EnableFK;
        PRINT '   ✓ FK Constraint-lər enable edildi';
    END
    
    PRINT '';
    
    COMMIT TRANSACTION;
    
    PRINT '🎉 TRANSACTION SİLMƏ ƏMƏLİYYATI TAMAMLANDI!';
    PRINT '';
    PRINT '📊 İndi 16_check_master_data_results.sql icra edin';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    PRINT '';
    PRINT '❌ XƏTA BAŞ VERDİ!';
    PRINT 'Xəta: ' + ERROR_MESSAGE();
    PRINT 'Sətir: ' + CAST(ERROR_LINE() AS VARCHAR(10));
    
    -- FK-ləri restore etməyə çalış
    BEGIN TRY
        DECLARE @RestoreFK NVARCHAR(MAX) = '';
        SELECT @RestoreFK = @RestoreFK + 
            'ALTER TABLE [' + SCHEMA_NAME(fk.schema_id) + '].[' + OBJECT_NAME(fk.parent_object_id) + '] ' +
            'WITH CHECK CHECK CONSTRAINT [' + fk.name + '];' + CHAR(13)
        FROM sys.foreign_keys fk;
        
        IF LEN(@RestoreFK) > 0
            EXEC sp_executesql @RestoreFK;
    END TRY
    BEGIN CATCH
        PRINT 'FK restore xətası: ' + ERROR_MESSAGE();
    END CATCH
    
    THROW;
END CATCH;
