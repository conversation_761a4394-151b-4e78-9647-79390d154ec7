-- SQL-də Parent-Child Table strukturunda tarixə görə veriləri silmək
-- Azərbaycan dilində şərh və nümunələr

-- =============================================================================
-- 1. ƏSAS PRINSIPLƏR
-- =============================================================================
-- Foreign key constraint-ləri olduqda, silmə ardıcıllığı vacibdir:
-- 1. Əvvəl CHILD table-dan sil
-- 2. Sonra PARENT table-dan sil

-- =============================================================================
-- 2. NÜMUNƏ TABLE STRUKTURU
-- =============================================================================

-- Parent table - Müştərilər
CREATE TABLE customers (
    customer_id INT PRIMARY KEY,
    customer_name VARCHAR(100),
    created_date DATE,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Child table - Sifarişlər
CREATE TABLE orders (
    order_id INT PRIMARY KEY,
    customer_id INT,
    order_date DATE,
    amount DECIMAL(10,2),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- Child table - Ödənişlər
CREATE TABLE payments (
    payment_id INT PRIMARY KEY,
    order_id INT,
    payment_date DATE,
    amount DECIMAL(10,2),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

-- =============================================================================
-- 3. METODLAR - TARIXƏ GÖRƏ SİLMƏK
-- =============================================================================

-- METOD 1: Sadə silmə (Foreign key constraint yoxdursa)
-- Bu metod yalnız constraint-lər olmadıqda işləyir
/*
DELETE FROM customers WHERE created_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
*/

-- METOD 2: Düzgün ardıcıllıqla silmə
-- Əvvəl child table-ları, sonra parent table-ı

-- Addım 1: Ən dərin child table-dan başla (payments)
DELETE FROM payments 
WHERE payment_date < '2025-01-01';

-- Addım 2: Orta səviyyə child table (orders)
DELETE FROM orders 
WHERE order_date < '2025-01-01';

-- Addım 3: Parent table (customers)
DELETE FROM customers 
WHERE created_date < '2025-01-01';

-- =============================================================================
-- 4. DAHA TƏHLÜKƏSIZ YANAŞMA - TRANSACTION İSTİFADƏSİ
-- =============================================================================

BEGIN TRANSACTION;

-- Əvvəl nə qədər record silinəcəyini yoxla
SELECT 'Payments to delete: ' as info, COUNT(*) as count 
FROM payments WHERE payment_date < '2025-01-01';

SELECT 'Orders to delete: ' as info, COUNT(*) as count 
FROM orders WHERE order_date < '2025-01-01';

SELECT 'Customers to delete: ' as info, COUNT(*) as count 
FROM customers WHERE created_date < '2025-01-01';

-- Silmə əməliyyatları
DELETE FROM payments WHERE payment_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM customers WHERE created_date < '2025-01-01';

-- Nəticəni yoxla və ya geri al
-- COMMIT; -- Əgər hər şey düzgündürsə
-- ROLLBACK; -- Əgər problem varsa

-- =============================================================================
-- 5. JOIN İSTİFADƏ EDƏRƏK SİLMƏK
-- =============================================================================

-- Child record-ları parent-ın tarixinə görə silmək
DELETE p FROM payments p
INNER JOIN orders o ON p.order_id = o.order_id
INNER JOIN customers c ON o.customer_id = c.customer_id
WHERE c.created_date < '2025-01-01';

DELETE o FROM orders o
INNER JOIN customers c ON o.customer_id = c.customer_id
WHERE c.created_date < '2025-01-01';

DELETE FROM customers 
WHERE created_date < '2025-01-01';

-- =============================================================================
-- 6. FOREIGN KEY CONSTRAINT-LƏRİ MÜVƏQQƏTI SÖNDÜRMƏK
-- =============================================================================

-- MySQL üçün:
SET FOREIGN_KEY_CHECKS = 0;

-- İstədiyiniz ardıcıllıqla silin
DELETE FROM customers WHERE created_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM payments WHERE payment_date < '2025-01-01';

-- Constraint-ləri yenidən aktiv edin
SET FOREIGN_KEY_CHECKS = 1;

-- PostgreSQL üçün:
-- ALTER TABLE orders DISABLE TRIGGER ALL;
-- ALTER TABLE payments DISABLE TRIGGER ALL;
-- ... silmə əməliyyatları ...
-- ALTER TABLE orders ENABLE TRIGGER ALL;
-- ALTER TABLE payments ENABLE TRIGGER ALL;

-- =============================================================================
-- 7. CASCADE DELETE İSTİFADƏSİ
-- =============================================================================

-- Əgər table yaradarkən CASCADE təyin etmisinizsə:
/*
CREATE TABLE orders (
    order_id INT PRIMARY KEY,
    customer_id INT,
    order_date DATE,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE
);
*/

-- Bu halda yalnız parent-dan silmək kifayətdir:
-- DELETE FROM customers WHERE created_date < '2025-01-01';
-- Bütün əlaqəli child record-lar avtomatik silinəcək

-- =============================================================================
-- 8. BACKUP VƏ TƏHLÜKƏSİZLİK
-- =============================================================================

-- Silməzdən əvvəl backup yaradın:
-- mysqldump -u username -p database_name > backup_before_delete.sql

-- Və ya müəyyən table-ları backup edin:
CREATE TABLE customers_backup AS SELECT * FROM customers WHERE created_date < '2025-01-01';
CREATE TABLE orders_backup AS SELECT * FROM orders WHERE order_date < '2025-01-01';
CREATE TABLE payments_backup AS SELECT * FROM payments WHERE payment_date < '2025-01-01';
