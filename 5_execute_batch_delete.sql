-- BÖYÜK VERİ ÜÇÜN BATCH SİLMƏ İCRA ETMƏK

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [YourDatabaseName] 
-- TO DISK = 'C:\Backup\sales_backup_2025.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- BATCH SİLMƏ PROSEDURUNU İCRA ETMƏK
-- =============================================================================

-- Böyük veri üçün (> 100,000 record):
EXEC DeleteSalesDataBatchSQLServer @CutoffDate = '2025-01-01', @BatchSize = 1000;

-- Çox böyük veri üçün (> 1,000,000 record):
-- EXEC DeleteSalesDataBatchSQLServer @CutoffDate = '2025-01-01', @BatchSize = 500;

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT COUNT(*) as remaining_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

PRINT 'BATCH SİLMƏ ƏMƏLIYYATI TAMAMLANDI - NƏTİCƏLƏRİ YOXLAYIN!';
