-- DÜ<PERSON>ƏLDİLMİŞ BATCH SİLMƏ PROSEDURU (Foreign Key Constraint-lər üçün)

CREATE PROCEDURE DeleteSalesDataBatchFixed
    @CutoffDate DATE = '2025-01-01',
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsDeleted INT = 1;
    DECLARE @TotalDeleted INT = 0;
    
    PRINT 'Düzəldilmiş batch silmə başladı: ' + CAST(@CutoffDate AS VARCHAR(10)) + ', Batch size: ' + CAST(@BatchSize AS VARCHAR(10));
    
    BEGIN TRY
        -- =============================================================================
        -- ADDIM 1: EXTENSION VƏ DETAIL TABLE-LAR (ƏN DƏRİN CHILD-LAR)
        -- =============================================================================
        
        -- Invoice extension table-ları
        DELETE FROM tpInvoiceLineExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceLineExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpInvoiceHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpInvoiceHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order extension table-ları
        DELETE FROM tpOrderLineExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderLineExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM tpOrderHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpOrderHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment extension table-ları
        DELETE FROM tpShipmentHeaderExtension WHERE CreatedDate < @CutoffDate;
        PRINT 'tpShipmentHeaderExtension silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Invoice detail table-ları
        DELETE FROM trInvoiceLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineReportedSales WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineReportedSales silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trInvoiceLineSubsequentDeliveryOrders WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSubsequentDeliveryOrders silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Order detail table-ları
        DELETE FROM trOrderLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineLinkedProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineLinkedProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderOpticalProductLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProductLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Shipment detail table-ları
        DELETE FROM trShipmentLineSumDetail WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSumDetail silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineBOM WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineBOM silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineGiftCard WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineGiftCard silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Payment detail table-ları
        DELETE FROM trPaymentLineCurrency WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLineCurrency silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        -- Sales plan detail table-ları
        DELETE FROM trSalesPlanProductQty WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProductQty silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== EXTENSION VƏ DETAIL TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 2: SUM TABLE-LAR
        -- =============================================================================
        
        DELETE FROM trInvoiceLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentLineSum WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentLineSum silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== SUM TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 3: LINE TABLE-LAR BATCH SİLMƏ
        -- =============================================================================
        
        -- trInvoiceLine batch silmə (ən böyük table)
        SET @TotalDeleted = 0;
        SET @RowsDeleted = 1;
        WHILE @RowsDeleted > 0
        BEGIN
            BEGIN TRANSACTION;
            DELETE TOP (@BatchSize) FROM trInvoiceLine WHERE CreatedDate < @CutoffDate;
            SET @RowsDeleted = @@ROWCOUNT;
            SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
            COMMIT TRANSACTION;
            
            PRINT 'trInvoiceLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
            
            IF @RowsDeleted > 0
                WAITFOR DELAY '00:00:00.100'; -- 100ms fasilə
        END;
        
        -- trOrderLine batch silmə
        SET @RowsDeleted = 1;
        SET @TotalDeleted = 0;
        WHILE @RowsDeleted > 0
        BEGIN
            BEGIN TRANSACTION;
            DELETE TOP (@BatchSize) FROM trOrderLine WHERE CreatedDate < @CutoffDate;
            SET @RowsDeleted = @@ROWCOUNT;
            SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
            COMMIT TRANSACTION;
            
            PRINT 'trOrderLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
            
            IF @RowsDeleted > 0
                WAITFOR DELAY '00:00:00.100';
        END;
        
        -- trShipmentLine batch silmə
        SET @RowsDeleted = 1;
        SET @TotalDeleted = 0;
        WHILE @RowsDeleted > 0
        BEGIN
            BEGIN TRANSACTION;
            DELETE TOP (@BatchSize) FROM trShipmentLine WHERE CreatedDate < @CutoffDate;
            SET @RowsDeleted = @@ROWCOUNT;
            SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
            COMMIT TRANSACTION;
            
            PRINT 'trShipmentLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
            
            IF @RowsDeleted > 0
                WAITFOR DELAY '00:00:00.100';
        END;
        
        -- Digər line table-ları
        DELETE FROM trPaymentLine WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentLine silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderAdvancePayments WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderAdvancePayments silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlanProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlanChannel WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlanChannel silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderOpticalProduct WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderOpticalProduct silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderPaymentPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderPaymentPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== LINE TABLE-LAR SİLİNDİ ===';
        
        -- =============================================================================
        -- ADDIM 4: HEADER TABLE-LAR
        -- =============================================================================
        
        DELETE FROM trInvoiceHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trOrderHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trShipmentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trPaymentHeader WHERE CreatedDate < @CutoffDate;
        PRINT 'trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        DELETE FROM trSalesPlan WHERE CreatedDate < @CutoffDate;
        PRINT 'trSalesPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
        
        PRINT '=== HEADER TABLE-LAR SİLİNDİ ===';
        
        PRINT '🎯 DÜZƏLDİLMİŞ BATCH SATIŞ SİLMƏ TAMAMLANDI!';
        
    END TRY
    BEGIN CATCH
        PRINT 'XƏTA BAŞ VERDİ!';
        PRINT 'Xəta mesajı: ' + ERROR_MESSAGE();
        PRINT 'Xəta sətiri: ' + CAST(ERROR_LINE() AS VARCHAR(10));
        THROW;
    END CATCH
END;
