# SQL-də Parent-Child Table-lardan <PERSON>ə <PERSON>örə Veriləri <PERSON>
## Checklist və Troubleshooting Guide

## 🔍 ƏVVƏL YOXLAYIN

### 1. Database Strukturunu Analiz Edin
```sql
-- Foreign key constraint-l<PERSON><PERSON> yox<PERSON>ın
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;
```

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON>
```sql
SELECT 'customers' as table_name, COUNT(*) as total_records, 
       COUNT(CASE WHEN created_date < '2025-01-01' THEN 1 END) as to_delete
FROM customers
UNION ALL
SELECT 'orders', COUNT(*), COUNT(CASE WHEN order_date < '2025-01-01' THEN 1 END)
FROM orders
UNION ALL
SELECT 'payments', COUNT(*), COUNT(CASE WHEN payment_date < '2025-01-01' THEN 1 END)
FROM payments;
```

### 3. Dependency Chain-i Müəyyən Edin
```sql
-- Hansı table-ların hansı table-lara bağlı olduğunu anlayın
-- payments -> orders -> customers (silmə ardıcıllığı)
```

## ✅ SİLMƏ PROSESI CHECKLIST

### Addım 1: Backup Yaradın
- [ ] Full database backup
- [ ] Spesifik table backup-ları
- [ ] Backup-ın düzgün olduğunu yoxlayın

### Addım 2: Test Environment-də Sınayın
- [ ] Eyni strukturu test DB-də yaradın
- [ ] Silmə script-ini test edin
- [ ] Performance-ı ölçün

### Addım 3: Production-da İcra Edin
- [ ] Maintenance window təyin edin
- [ ] Transaction istifadə edin
- [ ] Batch-batch silin (böyük veri üçün)
- [ ] Progress monitor edin

### Addım 4: Yoxlayın və Təsdiq Edin
- [ ] Silinən record sayını yoxlayın
- [ ] Foreign key constraint-lərin pozulmadığını yoxlayın
- [ ] Application-ın düzgün işlədiyini test edin

## 🚨 ÜMUMI XƏTALAR VƏ HƏLLLƏRİ

### Xəta 1: Foreign Key Constraint Violation
```
ERROR 1451: Cannot delete or update a parent row: a foreign key constraint fails
```

**Həll:**
```sql
-- Düzgün ardıcıllıqla silin (child -> parent)
DELETE FROM payments WHERE payment_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM customers WHERE created_date < '2025-01-01';
```

### Xəta 2: Lock Wait Timeout
```
ERROR 1205: Lock wait timeout exceeded
```

**Həll:**
```sql
-- Kiçik batch-larla silin
DELETE FROM payments WHERE payment_date < '2025-01-01' LIMIT 1000;
-- Fasilə verin
SELECT SLEEP(1);
-- Təkrarlayın
```

### Xəta 3: Out of Memory / Disk Space
**Həll:**
- Batch size-ı azaldın
- Temporary table space-i artırın
- Log file-ları təmizləyin

### Xəta 4: Deadlock
```
ERROR 1213: Deadlock found when trying to get lock
```

**Həll:**
```sql
-- Transaction-ı retry edin
-- Və ya table-ları müəyyən ardıcıllıqla lock edin
LOCK TABLES payments WRITE, orders WRITE, customers WRITE;
-- silmə əməliyyatları
UNLOCK TABLES;
```

## 📊 PERFORMANS OPTIMALLAŞDIRMASI

### Index-lər
```sql
-- Tarix sütunlarında index yaradın
CREATE INDEX idx_customers_created_date ON customers(created_date);
CREATE INDEX idx_orders_order_date ON orders(order_date);
CREATE INDEX idx_payments_payment_date ON payments(payment_date);
```

### Batch Silmə Strategiyası
```sql
-- Böyük table-lar üçün
WHILE (SELECT COUNT(*) FROM payments WHERE payment_date < '2025-01-01') > 0
DO
    DELETE FROM payments WHERE payment_date < '2025-01-01' LIMIT 1000;
    SELECT SLEEP(0.1); -- 100ms fasilə
END WHILE;
```

## 🔧 MÜXTƏLİF SSENARILƏR

### Ssenari 1: Yalnız Orphan Record-ları Silmək
```sql
-- Müştəriləri yalnız sifarişi olmayanda sil
DELETE c FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
WHERE c.created_date < '2025-01-01' 
AND o.customer_id IS NULL;
```

### Ssenari 2: Cascade Delete Aktivdirsə
```sql
-- Yalnız parent-dan silmək kifayətdir
DELETE FROM customers WHERE created_date < '2025-01-01';
-- Child record-lar avtomatik silinəcək
```

### Ssenari 3: Soft Delete
```sql
-- Fiziki silmək əvəzinə flag qoymaq
UPDATE customers SET is_deleted = 1, deleted_date = NOW() 
WHERE created_date < '2025-01-01';

UPDATE orders SET is_deleted = 1, deleted_date = NOW()
WHERE order_date < '2025-01-01';

UPDATE payments SET is_deleted = 1, deleted_date = NOW()
WHERE payment_date < '2025-01-01';
```

## 🛡️ TƏHLÜKƏSİZLİK TƏDBİRLƏRİ

### 1. Backup Strategiyası
```bash
# Full backup
mysqldump -u user -p database > full_backup_$(date +%Y%m%d).sql

# Table-specific backup
mysqldump -u user -p database customers orders payments > tables_backup.sql
```

### 2. Rollback Planı
```sql
-- Əgər səhv olarsa, backup-dan bərpa edin
-- mysql -u user -p database < backup_file.sql
```

### 3. Monitoring
```sql
-- Silmə prosesini monitor edin
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('customers', 'orders', 'payments');
```

## 📝 NÜMUNƏ SCRIPT - HAZIR İSTİFADƏ ÜÇÜN

```sql
-- Tam avtomatik silmə proseduru
DELIMITER //

CREATE PROCEDURE SafeDeleteByDate(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'Xəta baş verdi, əməliyyat geri alındı' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Backup table-ları yarat (əgər yoxdursa)
    CREATE TABLE IF NOT EXISTS customers_deleted_backup LIKE customers;
    CREATE TABLE IF NOT EXISTS orders_deleted_backup LIKE orders;
    CREATE TABLE IF NOT EXISTS payments_deleted_backup LIKE payments;
    
    -- Silinəcək veriləri backup-a köçür
    INSERT INTO payments_deleted_backup 
    SELECT * FROM payments WHERE payment_date < cutoff_date;
    
    INSERT INTO orders_deleted_backup 
    SELECT * FROM orders WHERE order_date < cutoff_date;
    
    INSERT INTO customers_deleted_backup 
    SELECT * FROM customers WHERE created_date < cutoff_date;
    
    -- Silmə əməliyyatları
    DELETE FROM payments WHERE payment_date < cutoff_date;
    DELETE FROM orders WHERE order_date < cutoff_date;
    DELETE FROM customers WHERE created_date < cutoff_date;
    
    -- Nəticəni göstər
    SELECT 
        'Silmə əməliyyatı uğurla tamamlandı' as status,
        ROW_COUNT() as last_affected_rows;
    
    COMMIT;
END //

DELIMITER ;

-- İstifadə:
-- CALL SafeDeleteByDate('2025-01-01');
```

## 🎯 SON TÖVSİYƏLƏR

1. **Həmişə test edin** - Production-da heç vaxt birbaşa icra etməyin
2. **Backup yaradın** - Hər zaman tam backup olsun
3. **Batch-batch işləyin** - Böyük veriləri bir anda silməyin
4. **Monitor edin** - Prosesin gedişatını izləyin
5. **Rollback planı hazırlayın** - Nə edəcəyinizi əvvəlcədən bilin
6. **Off-peak saatlarda edin** - İstifadəçilərə minimum təsir
7. **Log tutun** - Nə etdiyinizi qeyd edin
8. **Team-ə məlumat verin** - Digər developer-ləri xəbərdar edin
