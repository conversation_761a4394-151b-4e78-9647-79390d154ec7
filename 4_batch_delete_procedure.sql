-- <PERSON><PERSON><PERSON><PERSON><PERSON> VERİ ÜÇÜN BATCH SİLMƏ PROSEDURU

CREATE PROCEDURE DeleteSalesDataBatchSQLServer
    @CutoffDate DATE = '2025-01-01',
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RowsDeleted INT = 1;
    DECLARE @TotalDeleted INT = 0;
    
    PRINT 'Batch silmə başladı: ' + CAST(@CutoffDate AS VARCHAR(10)) + ', Batch size: ' + CAST(@BatchSize AS VARCHAR(10));
    
    -- trInvoiceLine batch silmə (ən böyük table)
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trInvoiceLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trInvoiceLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100'; -- 100ms fasilə
    END;
    
    -- trOrderLine batch silmə
    SET @RowsDeleted = 1;
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trOrderLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trOrderLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100';
    END;
    
    -- trShipmentLine batch silmə
    SET @RowsDeleted = 1;
    SET @TotalDeleted = 0;
    WHILE @RowsDeleted > 0
    BEGIN
        BEGIN TRANSACTION;
        DELETE TOP (@BatchSize) FROM trShipmentLine WHERE CreatedDate < @CutoffDate;
        SET @RowsDeleted = @@ROWCOUNT;
        SET @TotalDeleted = @TotalDeleted + @RowsDeleted;
        COMMIT TRANSACTION;
        
        PRINT 'trShipmentLine - Bu batch: ' + CAST(@RowsDeleted AS VARCHAR(10)) + ', Cəmi: ' + CAST(@TotalDeleted AS VARCHAR(10));
        
        WAITFOR DELAY '00:00:00.100';
    END;
    
    -- Qalan table-ları sadə şəkildə sil
    BEGIN TRANSACTION;
    
    DELETE FROM trInvoiceHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trInvoiceHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trOrderHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trOrderHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trShipmentHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trShipmentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trPaymentHeader WHERE CreatedDate < @CutoffDate;
    PRINT 'trPaymentHeader silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    DELETE FROM trSalesPlan WHERE CreatedDate < @CutoffDate;
    PRINT 'trSalesPlan silindi: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' record';
    
    COMMIT TRANSACTION;
    
    PRINT '🎯 BATCH SATIŞ SİLMƏ TAMAMLANDI!';
END;
