-- Sizin ERP sisteminizdə tarixə görə veriləri silmək üçün spesifik skript
-- Parent-Child əlaqələrinə və data tiplərinə uyğun hazırlanmışdır

-- =============================================================================
-- 1. SİSTEM ANALİZİ
-- =============================================================================
-- Sizin sisteminizdə əsas tarix sütunları:
-- - CreatedDate (datetime) - yaradılma tarixi
-- - LastUpdatedDate (datetime) - son yenilənmə tarixi  
-- - DocumentDate (date) - sənəd tarixi
-- - OperationDate (date) - əməliyyat tarixi
-- - TransactionDate (date) - tranzaksiya tarixi

-- =============================================================================
-- 2. ƏSAS PARENT-CHILD QRUPLAR
-- =============================================================================

-- A) AUDIT TABLES (au*) - Ən çox child table-ları olan
-- B) CODE DEFINITION TABLES (cd*) - Master data
-- C) TRANSACTION TABLES (tr*) - Əsas tranzaksiya veriləri
-- D) BUSINESS SETUP TABLES (bs*) - Konfiqurasiya veriləri

-- =============================================================================
-- 3. TƏHLÜKƏSİZ SİLMƏ PROSEDURU
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteDataByDateForYourERP(
    IN cutoff_date DATE,
    IN delete_audit_data BOOLEAN DEFAULT TRUE,
    IN delete_transaction_data BOOLEAN DEFAULT FALSE,
    IN batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'XƏTA: Əməliyyat geri alındı!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Statistika göstər
    SELECT 'SİLMƏ ƏVVƏLİ STATİSTİKA' as info;
    
    -- =============================================================================
    -- ADDIM 1: AUDIT TABLE-LARI SİLMƏK (ən təhlükəsiz)
    -- =============================================================================
    
    IF delete_audit_data THEN
        SELECT 'AUDIT TABLE-LARI SİLİNİR...' as status;
        
        -- Audit trace table-ları (ən dərin child-lar)
        DELETE FROM auGettingDataTransferTraceLine 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auAllocationTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auChequeDenyTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auCurrAccBadDebtStatusTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auCustomerCompanyBrandAttributeTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auDataTransferMissingRecordsTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auDataTransferTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auEuromsgSentAccountTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auGlobalDefaultTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auInnerTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auInteractiveSMSTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auInvoiceReprintTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auInvoiceTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auItemCopyTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auMarketPlaceServiceTrace 
        WHERE OperationStartDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auMergeRetailCustomerTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auMernisQueryTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auOpenDrawerWithNoSaleTrace 
        WHERE OpenDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auOptInOptOutTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPaymentReprintTrace 
        WHERE DocumentDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPosTerminalLoginTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auProcessFlowDenyTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auProposalLineRevisionTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPurchaseRequisitionLineRevisionTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPurchaseRequisitionProposalRevisionTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auRetailInvoiceLineChangeTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auShipmentReprintTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auShipmentTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auTransferPlanTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auUpdateItemCodeTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        -- Audit header table-ları
        DELETE FROM auGettingDataTransferTraceHeader 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auSendingDataTransferTrace 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        -- Service log table-ları
        DELETE FROM auAirConnTransactionInfo 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auAngolaSVCIntegrationInfo 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auBasefyServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auCustomizedDiscountEngineServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auCustomsServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auFastPayServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auIyzicoServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auMacellanSuperappServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auMobilDevServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auN2AnimaServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPaynetServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auPlanetPaymentServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auSeaBoxServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auTuratelServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auUmicoServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auUnifreeServiceLog 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        -- Diğer audit table-ları
        DELETE FROM auCancelRetailTransactions 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auDataTransferLog 
        WHERE EventDateTime < cutoff_date LIMIT batch_size;
        
        DELETE FROM auFixEInvoiceStatusCodes 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auFixPaymentTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auProgramUseTrace 
        WHERE StartDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auReconciliationReportNotificationTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auRetailCustomerIdentitySharingSystemTrace 
        WHERE CreatedDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auTransactionCheckInOutTrace 
        WHERE CheckInDate < cutoff_date LIMIT batch_size;
        
        DELETE FROM auVerifoneProcessTrace 
        WHERE OperationDate < cutoff_date LIMIT batch_size;
        
        SELECT 'AUDIT TABLE-LARI SİLİNDİ' as status;
    END IF;
    
    -- =============================================================================
    -- ADDIM 2: TRANSACTION TABLE-LARI (DİQQƏTLƏ!)
    -- =============================================================================
    
    IF delete_transaction_data THEN
        SELECT 'DİQQƏT: TRANSACTION VERİLƏRİ SİLİNİR!' as warning;
        
        -- Bu hissə çox diqqətli olmalıdır!
        -- Yalnız test environment-də istifadə edin!
        
        -- Transaction line table-ları əvvəl
        -- DELETE FROM trInvoiceLine WHERE ... (əgər lazımdırsa)
        -- DELETE FROM trOrderLine WHERE ... (əgər lazımdırsa)
        
        -- Transaction header table-ları sonra
        -- DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
        -- DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
        
        SELECT 'TRANSACTION VERİLƏRİ SİLİNDİ' as status;
    END IF;
    
    -- Nəticəni göstər
    SELECT 'SİLMƏ ƏMƏLİYYATI TAMAMLANDI' as final_status;
    
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 4. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

-- Yalnız audit veriləri silmək (təhlükəsiz)
-- CALL DeleteDataByDateForYourERP('2025-01-01', TRUE, FALSE, 1000);

-- Audit və transaction veriləri silmək (DİQQƏTLƏ!)
-- CALL DeleteDataByDateForYourERP('2025-01-01', TRUE, TRUE, 500);

-- =============================================================================
-- 5. MANUAL SİLMƏ (ADDIM-ADDIM)
-- =============================================================================

-- Əgər prosedur istifadə etmək istəmirsinizsə, manual olaraq:

-- 1. Əvvəl audit trace table-larını silin
/*
DELETE FROM auAllocationTrace WHERE OperationDate < '2025-01-01';
DELETE FROM auInvoiceTrace WHERE OperationDate < '2025-01-01';
DELETE FROM auShipmentTrace WHERE OperationDate < '2025-01-01';
-- ... digər trace table-ları
*/

-- 2. Sonra audit service log-larını silin  
/*
DELETE FROM auBasefyServiceLog WHERE CreatedDate < '2025-01-01';
DELETE FROM auCustomsServiceLog WHERE CreatedDate < '2025-01-01';
DELETE FROM auFastPayServiceLog WHERE CreatedDate < '2025-01-01';
-- ... digər service log-ları
*/

-- 3. Ən son audit permit table-larını silin
/*
DELETE FROM auBankPermit WHERE CreatedDate < '2025-01-01';
DELETE FROM auCashPermit WHERE CreatedDate < '2025-01-01';
DELETE FROM auChequePermit WHERE CreatedDate < '2025-01-01';
-- ... digər permit table-ları
*/

-- =============================================================================
-- 6. BACKUP VƏ BƏRPA
-- =============================================================================

-- Backup yaratmaq:
-- mysqldump -u username -p database_name > backup_before_delete_$(date +%Y%m%d).sql

-- Spesifik table-ları backup etmək:
-- mysqldump -u username -p database_name auAllocationTrace auInvoiceTrace > audit_backup.sql

-- =============================================================================
-- 7. MONITORING QUERY-LƏRİ
-- =============================================================================

-- Silinəcək record sayını yoxlamaq:
SELECT 
    'auAllocationTrace' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN OperationDate < '2025-01-01' THEN 1 END) as to_delete
FROM auAllocationTrace
UNION ALL
SELECT 
    'auInvoiceTrace',
    COUNT(*),
    COUNT(CASE WHEN OperationDate < '2025-01-01' THEN 1 END)
FROM auInvoiceTrace
UNION ALL
SELECT 
    'auBasefyServiceLog',
    COUNT(*),
    COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END)
FROM auBasefyServiceLog;

-- Database ölçüsünü yoxlamaq:
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND table_name LIKE 'au%'
ORDER BY (data_length + index_length) DESC;

-- =============================================================================
-- 8. ƏSAS TRANSACTION TABLE-LARI ÜÇÜN SPESİFİK SKRIPT
-- =============================================================================

-- DİQQƏT: Bu hissə yalnız test environment-də istifadə edin!

DELIMITER //

CREATE PROCEDURE DeleteTransactionDataByDate(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'TRANSACTION SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Transaction extension table-ları əvvəl
    DELETE FROM tpInvoiceHeaderExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpOrderHeaderExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpShipmentHeaderExtension WHERE CreatedDate < cutoff_date;
    DELETE FROM tpInnerHeaderExtension WHERE CreatedDate < cutoff_date;

    -- Transaction line table-ları
    DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trProposalLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trBankLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trCashLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trChequeLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trJournalLine WHERE CreatedDate < cutoff_date;
    DELETE FROM trPaymentLine WHERE CreatedDate < cutoff_date;

    -- Transaction header table-ları
    DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trProposalHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trBankHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trCashHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trChequeHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trJournalHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date;
    DELETE FROM trStock WHERE CreatedDate < cutoff_date;

    SELECT 'TRANSACTION VERİLƏRİ SİLİNDİ' as status;
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 9. CODE DEFINITION TABLE-LARI ÜÇÜN SKRIPT
-- =============================================================================

-- Bu table-lar master data olduğu üçün çox diqqətli olun!

DELIMITER //

CREATE PROCEDURE DeleteCodeDefinitionDataByDate(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'CODE DEFINITION SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Description table-ları əvvəl (child)
    DELETE FROM cdAccountantDesc WHERE CreatedDate < cutoff_date;
    DELETE FROM cdBankDesc WHERE CreatedDate < cutoff_date;
    DELETE FROM cdCurrAccDesc WHERE CreatedDate < cutoff_date;
    DELETE FROM cdItemDesc WHERE CreatedDate < cutoff_date;
    DELETE FROM cdGLAccDesc WHERE CreatedDate < cutoff_date;

    -- Attribute table-ları
    DELETE FROM cdCurrAccAttribute WHERE CreatedDate < cutoff_date;
    DELETE FROM cdItemAttribute WHERE CreatedDate < cutoff_date;
    DELETE FROM cdGLAccAttribute WHERE CreatedDate < cutoff_date;

    -- Main code table-ları (parent) - ÇOX DİQQƏTLƏ!
    -- DELETE FROM cdAccountant WHERE CreatedDate < cutoff_date;
    -- DELETE FROM cdBank WHERE CreatedDate < cutoff_date;
    -- DELETE FROM cdCurrAcc WHERE CreatedDate < cutoff_date;
    -- DELETE FROM cdItem WHERE CreatedDate < cutoff_date;
    -- DELETE FROM cdGLAcc WHERE CreatedDate < cutoff_date;

    SELECT 'CODE DEFINITION VERİLƏRİ SİLİNDİ' as status;
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- 10. TÖVSİYƏLƏR VƏ BEST PRACTICES
-- =============================================================================

-- 1. Əvvəl test environment-də sınayın
-- 2. Kiçik batch-larla başlayın (1000 record)
-- 3. Audit table-larından başlayın (daha təhlükəsiz)
-- 4. Transaction table-larını silməzdən əvvəl business team-lə danışın
-- 5. Həmişə backup yaradın
-- 6. Off-peak saatlarda icra edin
-- 7. Progress monitor edin

-- İSTİFADƏ ARDICIĞI:
-- 1. CALL DeleteDataByDateForYourERP('2025-01-01', TRUE, FALSE, 1000); -- Audit only
-- 2. CALL DeleteTransactionDataByDate('2025-01-01'); -- Transactions (DİQQƏTLƏ!)
-- 3. CALL DeleteCodeDefinitionDataByDate('2025-01-01'); -- Master data (ÇOX DİQQƏTLƏ!)
