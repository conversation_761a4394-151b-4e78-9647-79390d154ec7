-- DÜZƏLDİLMİŞ BATCH SİLMƏ İCRA ETMƏK

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [Test_DB] 
-- TO DISK = 'C:\Backup\sales_backup_2025_fixed.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- DÜZƏLDİLMİŞ BATCH SİLMƏ PROSEDURUNU İCRA ETMƏK
-- =============================================================================

-- Düzəldilmiş proseduru icra edin:
EXEC DeleteSalesDataBatchFixed @CutoffDate = '2025-01-01', @BatchSize = 1000;

-- Əgər yenə də xəta olarsa, daha kiçik batch ilə sınayın:
-- EXEC DeleteSalesDataBatchFixed @CutoffDate = '2025-01-01', @BatchSize = 500;

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT COUNT(*) as remaining_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

-- Extension table-ları yoxlayın:
SELECT COUNT(*) as remaining_invoice_extensions
FROM tpInvoiceLineExtension 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_order_extensions
FROM tpOrderHeaderExtension 
WHERE CreatedDate < '2025-01-01';

PRINT 'DÜZƏLDİLMİŞ BATCH SİLMƏ ƏMƏLIYYATI TAMAMLANDI - NƏTİCƏLƏRİ YOXLAYIN!';
