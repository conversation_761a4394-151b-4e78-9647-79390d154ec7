-- Praktiki nümunələr - Tarixə görə silmək
-- Real həyat ssenarilərində istifadə üçün

-- =============================================================================
-- 1. ASAN VƏ TƏHLÜKƏSİZ METOD - STORED PROCEDURE
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteDataByDate(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- <PERSON>ati<PERSON><PERSON> göstər
    SELECT 
        'Silinəcək payments' as table_name, 
        COUNT(*) as record_count 
    FROM payments 
    WHERE payment_date < cutoff_date;
    
    SELECT 
        'Silinəcək orders' as table_name, 
        COUNT(*) as record_count 
    FROM orders 
    WHERE order_date < cutoff_date;
    
    SELECT 
        'Silinəcək customers' as table_name, 
        COUNT(*) as record_count 
    FROM customers 
    WHERE created_date < cutoff_date;
    
    -- Silmə əməliyyatları (düzgün ardıcıllıqla)
    DELETE FROM payments WHERE payment_date < cutoff_date;
    DELETE FROM orders WHERE order_date < cutoff_date;
    DELETE FROM customers WHERE created_date < cutoff_date;
    
    -- Nəticəni göstər
    SELECT ROW_COUNT() as 'Son silmədə təsirlənən sətrlər';
    
    COMMIT;
END //

DELIMITER ;

-- İstifadə:
-- CALL DeleteDataByDate('2025-01-01');

-- =============================================================================
-- 2. BATCH SİLMƏ - BÖYÜK VERİLƏR ÜÇÜN
-- =============================================================================

-- Böyük table-larda performans üçün batch-batch silmək
DELIMITER //

CREATE PROCEDURE DeleteDataInBatches(
    IN cutoff_date DATE, 
    IN batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    
    -- Payments table-ı üçün batch silmə
    batch_loop: LOOP
        DELETE FROM payments 
        WHERE payment_date < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop;
        END IF;
        
        -- Kiçik fasilə (server yükünü azaltmaq üçün)
        SELECT SLEEP(0.1);
    END LOOP;
    
    SELECT CONCAT('Payments table-dan silindi: ', total_deleted) as result;
    
    -- Orders table üçün eyni proses
    SET total_deleted = 0;
    batch_loop2: LOOP
        DELETE FROM orders 
        WHERE order_date < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop2;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    SELECT CONCAT('Orders table-dan silindi: ', total_deleted) as result;
    
    -- Customers table üçün
    SET total_deleted = 0;
    batch_loop3: LOOP
        DELETE FROM customers 
        WHERE created_date < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop3;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    SELECT CONCAT('Customers table-dan silindi: ', total_deleted) as result;
END //

DELIMITER ;

-- İstifadə:
-- CALL DeleteDataInBatches('2025-01-01', 500);

-- =============================================================================
-- 3. ŞƏRTLI SİLMƏ - YALNIZ ORPHAN RECORD-LARI
-- =============================================================================

-- Yalnız əlaqəsi olmayan (orphan) record-ları silmək
DELETE c FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
WHERE c.created_date < '2025-01-01' 
AND o.customer_id IS NULL;

-- Və ya əksinə - yalnız əlaqəsi olan record-ları silmək
DELETE c FROM customers c
INNER JOIN orders o ON c.customer_id = o.customer_id
WHERE c.created_date < '2025-01-01';

-- =============================================================================
-- 4. ARXIV ETMƏK VƏ SONRA SİLMƏK
-- =============================================================================

-- Arxiv table-ları yaratmaq
CREATE TABLE customers_archive LIKE customers;
CREATE TABLE orders_archive LIKE orders;
CREATE TABLE payments_archive LIKE payments;

-- Veriləri arxivə köçürmək
INSERT INTO payments_archive 
SELECT * FROM payments WHERE payment_date < '2025-01-01';

INSERT INTO orders_archive 
SELECT * FROM orders WHERE order_date < '2025-01-01';

INSERT INTO customers_archive 
SELECT * FROM customers WHERE created_date < '2025-01-01';

-- Arxivə köçürüldükdən sonra silmək
DELETE FROM payments WHERE payment_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM customers WHERE created_date < '2025-01-01';

-- =============================================================================
-- 5. FOREIGN KEY CONSTRAINT-LƏRİNİ YOXLAMAQ
-- =============================================================================

-- MySQL-də constraint-ləri görmək
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- PostgreSQL-də constraint-ləri görmək
/*
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY';
*/

-- =============================================================================
-- 6. PERFORMANS OPTIMALLAŞDIRMASI
-- =============================================================================

-- Index-lər yaratmaq (əgər yoxdursa)
CREATE INDEX idx_customers_created_date ON customers(created_date);
CREATE INDEX idx_orders_order_date ON orders(order_date);
CREATE INDEX idx_payments_payment_date ON payments(payment_date);

-- Silmədən əvvəl statistika yoxlamaq
ANALYZE TABLE customers, orders, payments;

-- =============================================================================
-- 7. LOG VƏ MONITORING
-- =============================================================================

-- Silmə əməliyyatını log etmək üçün table
CREATE TABLE deletion_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50),
    deleted_count INT,
    cutoff_date DATE,
    deletion_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Log ilə silmə proseduru
DELIMITER //

CREATE PROCEDURE DeleteWithLogging(IN cutoff_date DATE)
BEGIN
    DECLARE payments_count INT DEFAULT 0;
    DECLARE orders_count INT DEFAULT 0;
    DECLARE customers_count INT DEFAULT 0;
    
    -- Silinəcək record sayını hesabla
    SELECT COUNT(*) INTO payments_count FROM payments WHERE payment_date < cutoff_date;
    SELECT COUNT(*) INTO orders_count FROM orders WHERE order_date < cutoff_date;
    SELECT COUNT(*) INTO customers_count FROM customers WHERE created_date < cutoff_date;
    
    -- Silmə əməliyyatları
    DELETE FROM payments WHERE payment_date < cutoff_date;
    DELETE FROM orders WHERE order_date < cutoff_date;
    DELETE FROM customers WHERE created_date < cutoff_date;
    
    -- Log-a yaz
    INSERT INTO deletion_log (table_name, deleted_count, cutoff_date) 
    VALUES 
        ('payments', payments_count, cutoff_date),
        ('orders', orders_count, cutoff_date),
        ('customers', customers_count, cutoff_date);
        
    SELECT 'Silmə əməliyyatı tamamlandı və log edildi' as status;
END //

DELIMITER ;

-- İstifadə:
-- CALL DeleteWithLogging('2025-01-01');
