-- TAM DÜZƏLDİLMİŞ BATCH SİLMƏ İCRA ETMƏK

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [Test_DB] 
-- TO DISK = 'C:\Backup\sales_backup_2025_complete.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- TAM DÜZƏLDİLMİŞ BATCH SİLMƏ PROSEDURUNU İCRA ETMƏK
-- =============================================================================

-- Tam düzəldilmiş proseduru icra edin:
EXEC DeleteSalesDataCompleteFixed @CutoffDate = '2025-01-01', @BatchSize = 1000;

-- Əgər yenə də xəta olarsa, daha kiçik batch ilə sınayın:
-- EXEC DeleteSalesDataCompleteFixed @CutoffDate = '2025-01-01', @BatchSize = 500;

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT COUNT(*) as remaining_invoices 
FROM trInvoiceHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_invoice_lines 
FROM trInvoiceLine 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_orders 
FROM trOrderHeader 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_shipments 
FROM trShipmentHeader 
WHERE CreatedDate < '2025-01-01';

-- Extension table-ları yoxlayın:
SELECT COUNT(*) as remaining_invoice_extensions
FROM tpInvoiceLineExtension 
WHERE CreatedDate < '2025-01-01';

SELECT COUNT(*) as remaining_discount_offers
FROM tpInvoiceDiscountOffer 
WHERE CreatedDate < '2025-01-01';

-- Ümumi xülasə:
SELECT 
    'FAKTURA' as table_type,
    (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') as remaining_headers,
    (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') as remaining_lines
UNION ALL
SELECT 
    'SİFARİŞ',
    (SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trOrderLine WHERE CreatedDate < '2025-01-01')
UNION ALL
SELECT 
    'GÖNDƏRMƏ',
    (SELECT COUNT(*) FROM trShipmentHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trShipmentLine WHERE CreatedDate < '2025-01-01')
UNION ALL
SELECT 
    'ÖDƏNİŞ',
    (SELECT COUNT(*) FROM trPaymentHeader WHERE CreatedDate < '2025-01-01'),
    (SELECT COUNT(*) FROM trPaymentLine WHERE CreatedDate < '2025-01-01');

PRINT 'TAM DÜZƏLDİLMİŞ BATCH SİLMƏ ƏMƏLIYYATI TAMAMLANDI - NƏTİCƏLƏRİ YOXLAYIN!';
