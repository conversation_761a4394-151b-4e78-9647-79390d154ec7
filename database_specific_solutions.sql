-- Müxtəlif Database sistemləri üçün spesifik həllər
-- MySQL, PostgreSQL, SQL Server, Oracle

-- =============================================================================
-- 1. MYSQL SPESİFİK HƏLLƏR
-- =============================================================================

-- MySQL-də multi-table DELETE
DELETE p, o, c 
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
LEFT JOIN payments p ON o.order_id = p.order_id
WHERE c.created_date < '2025-01-01';

-- MySQL-də FOREIGN_KEY_CHECKS istifadəsi
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM customers WHERE created_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM payments WHERE payment_date < '2025-01-01';
SET FOREIGN_KEY_CHECKS = 1;

-- MySQL-də TRUNCATE (bütün table-ı təmizləmək üçün)
-- TRUNCATE TABLE payments; -- Bütün veriləri silir, AUTO_INCREMENT sıfırlanır

-- =============================================================================
-- 2. POSTGRESQL SPESİFİK HƏLLƏR
-- =============================================================================

-- PostgreSQL-də USING clause ilə DELETE
DELETE FROM payments 
USING orders, customers
WHERE payments.order_id = orders.order_id
AND orders.customer_id = customers.customer_id
AND customers.created_date < '2025-01-01';

-- PostgreSQL-də trigger-ləri müvəqqəti söndürmək
ALTER TABLE orders DISABLE TRIGGER ALL;
ALTER TABLE payments DISABLE TRIGGER ALL;

-- Silmə əməliyyatları
DELETE FROM customers WHERE created_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM payments WHERE payment_date < '2025-01-01';

-- Trigger-ləri yenidən aktiv etmək
ALTER TABLE orders ENABLE TRIGGER ALL;
ALTER TABLE payments ENABLE TRIGGER ALL;

-- PostgreSQL-də RETURNING clause
DELETE FROM customers 
WHERE created_date < '2025-01-01'
RETURNING customer_id, customer_name, created_date;

-- PostgreSQL-də CTE (Common Table Expression) istifadəsi
WITH customers_to_delete AS (
    SELECT customer_id FROM customers WHERE created_date < '2025-01-01'
),
orders_to_delete AS (
    SELECT order_id FROM orders 
    WHERE customer_id IN (SELECT customer_id FROM customers_to_delete)
)
DELETE FROM payments 
WHERE order_id IN (SELECT order_id FROM orders_to_delete);

-- =============================================================================
-- 3. SQL SERVER SPESİFİK HƏLLƏR
-- =============================================================================

-- SQL Server-də OUTPUT clause
DELETE FROM customers 
OUTPUT DELETED.customer_id, DELETED.customer_name, DELETED.created_date
WHERE created_date < '2025-01-01';

-- SQL Server-də MERGE statement (kompleks şərtlər üçün)
MERGE customers AS target
USING (SELECT customer_id FROM customers WHERE created_date < '2025-01-01') AS source
ON target.customer_id = source.customer_id
WHEN MATCHED THEN DELETE;

-- SQL Server-də constraint-ləri müvəqqəti söndürmək
ALTER TABLE orders NOCHECK CONSTRAINT ALL;
ALTER TABLE payments NOCHECK CONSTRAINT ALL;

-- Silmə əməliyyatları
DELETE FROM customers WHERE created_date < '2025-01-01';
DELETE FROM orders WHERE order_date < '2025-01-01';
DELETE FROM payments WHERE payment_date < '2025-01-01';

-- Constraint-ləri yenidən aktiv etmək
ALTER TABLE orders CHECK CONSTRAINT ALL;
ALTER TABLE payments CHECK CONSTRAINT ALL;

-- SQL Server-də TOP clause ilə batch silmə
WHILE @@ROWCOUNT > 0
BEGIN
    DELETE TOP (1000) FROM payments WHERE payment_date < '2025-01-01';
    WAITFOR DELAY '00:00:01'; -- 1 saniyə gözlə
END;

-- =============================================================================
-- 4. ORACLE SPESİFİK HƏLLƏR
-- =============================================================================

-- Oracle-də ROWNUM istifadəsi (batch silmə üçün)
DELETE FROM payments 
WHERE payment_date < DATE '2025-01-01'
AND ROWNUM <= 1000;

-- Oracle-də MERGE statement
MERGE INTO customers c
USING (SELECT customer_id FROM customers WHERE created_date < DATE '2025-01-01') old_customers
ON (c.customer_id = old_customers.customer_id)
WHEN MATCHED THEN DELETE;

-- Oracle-də constraint-ləri söndürmək
ALTER TABLE orders DISABLE CONSTRAINT fk_orders_customer_id;
ALTER TABLE payments DISABLE CONSTRAINT fk_payments_order_id;

-- Silmə əməliyyatları
DELETE FROM customers WHERE created_date < DATE '2025-01-01';
DELETE FROM orders WHERE order_date < DATE '2025-01-01';
DELETE FROM payments WHERE payment_date < DATE '2025-01-01';

-- Constraint-ləri yenidən aktiv etmək
ALTER TABLE orders ENABLE CONSTRAINT fk_orders_customer_id;
ALTER TABLE payments ENABLE CONSTRAINT fk_payments_order_id;

-- Oracle-də FORALL statement (PL/SQL)
DECLARE
    TYPE customer_id_array IS TABLE OF customers.customer_id%TYPE;
    l_customer_ids customer_id_array;
BEGIN
    SELECT customer_id 
    BULK COLLECT INTO l_customer_ids
    FROM customers 
    WHERE created_date < DATE '2025-01-01';
    
    FORALL i IN 1..l_customer_ids.COUNT
        DELETE FROM customers WHERE customer_id = l_customer_ids(i);
        
    COMMIT;
END;
/

-- =============================================================================
-- 5. ÜMUMI TƏHLÜKƏSİZLİK PRİNSİPLƏRİ
-- =============================================================================

-- 1. Həmişə backup yaradın
-- mysqldump -u user -p database > backup_$(date +%Y%m%d_%H%M%S).sql

-- 2. Əvvəl SELECT ilə yoxlayın
SELECT COUNT(*) as 'Silinəcək customers' FROM customers WHERE created_date < '2025-01-01';
SELECT COUNT(*) as 'Silinəcək orders' FROM orders WHERE order_date < '2025-01-01';
SELECT COUNT(*) as 'Silinəcək payments' FROM payments WHERE payment_date < '2025-01-01';

-- 3. Transaction istifadə edin
START TRANSACTION;
-- silmə əməliyyatları
-- COMMIT; və ya ROLLBACK;

-- 4. Test environment-də əvvəl sınayın

-- 5. Production-da kiçik batch-larla işləyin

-- =============================================================================
-- 6. PERFORMANS OPTIMALLAŞDIRMASI
-- =============================================================================

-- Index-lər yaradın
CREATE INDEX idx_customers_created_date ON customers(created_date);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_payments_date ON payments(payment_date);

-- Statistikaları yeniləyin
-- MySQL: ANALYZE TABLE customers, orders, payments;
-- PostgreSQL: ANALYZE customers, orders, payments;
-- SQL Server: UPDATE STATISTICS customers, orders, payments;
-- Oracle: EXEC DBMS_STATS.GATHER_TABLE_STATS('schema', 'customers');

-- =============================================================================
-- 7. MONITORING VƏ LOG
-- =============================================================================

-- Silmə prosesini monitor etmək üçün
SELECT 
    'BEFORE DELETE' as status,
    (SELECT COUNT(*) FROM customers) as customers_count,
    (SELECT COUNT(*) FROM orders) as orders_count,
    (SELECT COUNT(*) FROM payments) as payments_count,
    NOW() as timestamp;

-- Silmə əməliyyatları burada...

SELECT 
    'AFTER DELETE' as status,
    (SELECT COUNT(*) FROM customers) as customers_count,
    (SELECT COUNT(*) FROM orders) as orders_count,
    (SELECT COUNT(*) FROM payments) as payments_count,
    NOW() as timestamp;

-- =============================================================================
-- 8. ROLLBACK PLANI
-- =============================================================================

-- Əgər nəsə səhv olarsa, backup-dan bərpa etmək:
-- mysql -u user -p database < backup_file.sql

-- Və ya spesifik table-ları bərpa etmək:
-- mysql -u user -p database -e "SOURCE backup_customers.sql"

-- =============================================================================
-- 9. AUTOMATION SCRIPT (Bash nümunəsi)
-- =============================================================================

/*
#!/bin/bash
# delete_old_data.sh

DB_HOST="localhost"
DB_USER="username"
DB_PASS="password"
DB_NAME="database"
CUTOFF_DATE="2025-01-01"

# Backup yaradın
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql

# Silmə əməliyyatını icra edin
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME << EOF
START TRANSACTION;
DELETE FROM payments WHERE payment_date < '$CUTOFF_DATE';
DELETE FROM orders WHERE order_date < '$CUTOFF_DATE';
DELETE FROM customers WHERE created_date < '$CUTOFF_DATE';
COMMIT;
EOF

echo "Silmə əməliyyatı tamamlandı: $CUTOFF_DATE"
*/
