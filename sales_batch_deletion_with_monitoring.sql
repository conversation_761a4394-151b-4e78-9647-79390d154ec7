-- Sa<PERSON><PERSON><PERSON> veriləri üçün batch silmə və real-time monitoring
-- Performance optimallaşdırılmış və təhlükəsiz

-- =============================================================================
-- 1. SATIŞ VERİLƏRİ ÜÇÜN BATCH SİLMƏ PROSEDURU
-- =============================================================================

DELIMITER //

CREATE PROCEDURE BatchDeleteSalesData(
    IN cutoff_date DATE,
    IN batch_size INT DEFAULT 1000,
    IN sleep_seconds DECIMAL(3,1) DEFAULT 0.2,
    IN max_batches INT DEFAULT 100
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    DECLARE batch_count INT DEFAULT 0;
    DECLARE table_name VARCHAR(100);
    DECLARE start_time DATETIME DEFAULT NOW();
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'BATCH SATIŞ SİLMƏ XƏTASI!' as error_message, table_name as failed_table;
        RESIGNAL;
    END;
    
    -- Monitoring table yaradın (əgər yoxdursa)
    CREATE TABLE IF NOT EXISTS sales_deletion_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        table_name VARCHAR(100),
        batch_number INT,
        records_deleted INT,
        total_deleted INT,
        deletion_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        cutoff_date DATE
    );
    
    SELECT 
        'BATCH SATIŞ SİLMƏ BAŞLADI' as status,
        cutoff_date as cutoff_date,
        batch_size as batch_size,
        start_time as start_time;
    
    -- =============================================================================
    -- INVOICE LINE TABLE-LARI (ƏN BÖYÜK TABLE-LAR)
    -- =============================================================================
    
    SET table_name = 'trInvoiceLine';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    invoice_line_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trInvoiceLine 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        -- Log-a yaz
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE invoice_line_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- =============================================================================
    -- ORDER LINE TABLE-LARI
    -- =============================================================================
    
    SET table_name = 'trOrderLine';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    order_line_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trOrderLine 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE order_line_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- =============================================================================
    -- SHIPMENT LINE TABLE-LARI
    -- =============================================================================
    
    SET table_name = 'trShipmentLine';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    shipment_line_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trShipmentLine 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE shipment_line_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- =============================================================================
    -- HEADER TABLE-LARI (KIÇIK BATCH-LARLA)
    -- =============================================================================
    
    SET batch_size = batch_size / 2; -- Header table-lar üçün kiçik batch
    
    -- Invoice Header
    SET table_name = 'trInvoiceHeader';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    invoice_header_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trInvoiceHeader 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE invoice_header_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- Order Header
    SET table_name = 'trOrderHeader';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    order_header_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trOrderHeader 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE order_header_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    -- Shipment Header
    SET table_name = 'trShipmentHeader';
    SET total_deleted = 0;
    SET batch_count = 0;
    
    shipment_header_loop: LOOP
        START TRANSACTION;
        
        DELETE FROM trShipmentHeader 
        WHERE CreatedDate < cutoff_date 
        LIMIT batch_size;
        
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        SET batch_count = batch_count + 1;
        
        INSERT INTO sales_deletion_log (table_name, batch_number, records_deleted, total_deleted, cutoff_date)
        VALUES (table_name, batch_count, affected_rows, total_deleted, cutoff_date);
        
        COMMIT;
        
        SELECT CONCAT('Silindi: ', table_name, ' - Batch: ', batch_count, ', Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 OR batch_count >= max_batches THEN
            LEAVE shipment_header_loop;
        END IF;
        
        SELECT SLEEP(sleep_seconds);
    END LOOP;
    
    SELECT 
        'BATCH SATIŞ SİLMƏ TAMAMLANDI' as final_status,
        TIMEDIFF(NOW(), start_time) as total_duration,
        NOW() as end_time;
        
END //

DELIMITER ;

-- =============================================================================
-- 2. REAL-TIME MONITORING QUERY-LƏRİ
-- =============================================================================

-- Prosesin gedişatını izləmək üçün:
SELECT 
    'CURRENT DELETION PROGRESS' as info,
    table_name,
    MAX(batch_number) as last_batch,
    MAX(total_deleted) as total_deleted,
    MAX(deletion_time) as last_update
FROM sales_deletion_log 
WHERE DATE(deletion_time) = CURDATE()
GROUP BY table_name
ORDER BY last_update DESC;

-- Server yükünü yoxlamaq:
SELECT 
    'SERVER STATUS' as info,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN COMMAND != 'Sleep' THEN 1 END) as active_queries,
    COUNT(CASE WHEN STATE LIKE '%delete%' THEN 1 END) as delete_operations,
    COUNT(CASE WHEN INFO LIKE '%trInvoice%' OR INFO LIKE '%trOrder%' OR INFO LIKE '%trShipment%' THEN 1 END) as sales_operations
FROM INFORMATION_SCHEMA.PROCESSLIST;

-- Table ölçülərini yoxlamaq:
SELECT 
    'TABLE SIZES' as info,
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    ROUND((data_free / 1024 / 1024), 2) AS 'Free (MB)'
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND (table_name LIKE 'trInvoice%' OR table_name LIKE 'trOrder%' OR table_name LIKE 'trShipment%')
ORDER BY (data_length + index_length) DESC;

-- =============================================================================
-- 3. İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

-- Kiçik test batch ilə başlayın:
-- CALL BatchDeleteSalesData('2025-01-01', 100, 1.0, 10);

-- Normal batch ölçüsü:
-- CALL BatchDeleteSalesData('2025-01-01', 1000, 0.2, 100);

-- Böyük table-lar üçün:
-- CALL BatchDeleteSalesData('2025-01-01', 2000, 0.1, 200);

-- =============================================================================
-- 4. PERFORMANCE OPTIMALLAŞDIRMASI
-- =============================================================================

-- Bu index-ləri yaradın (əgər yoxdursa):
-- CREATE INDEX idx_trInvoiceLine_CreatedDate ON trInvoiceLine(CreatedDate);
-- CREATE INDEX idx_trInvoiceHeader_CreatedDate ON trInvoiceHeader(CreatedDate);
-- CREATE INDEX idx_trOrderLine_CreatedDate ON trOrderLine(CreatedDate);
-- CREATE INDEX idx_trOrderHeader_CreatedDate ON trOrderHeader(CreatedDate);
-- CREATE INDEX idx_trShipmentLine_CreatedDate ON trShipmentLine(CreatedDate);
-- CREATE INDEX idx_trShipmentHeader_CreatedDate ON trShipmentHeader(CreatedDate);

-- Statistikaları yeniləyin:
-- ANALYZE TABLE trInvoiceHeader, trInvoiceLine, trOrderHeader, trOrderLine, trShipmentHeader, trShipmentLine;

-- =============================================================================
-- 5. EMERGENCY STOP PROSEDURU
-- =============================================================================

DELIMITER //

CREATE PROCEDURE StopSalesDeletion()
BEGIN
    -- Aktiv silmə proseslərini dayandırmaq üçün
    DECLARE done INT DEFAULT FALSE;
    DECLARE process_id INT;
    DECLARE cur CURSOR FOR 
        SELECT ID FROM INFORMATION_SCHEMA.PROCESSLIST 
        WHERE DB = DATABASE() 
        AND (INFO LIKE '%DELETE FROM tr%' OR INFO LIKE '%BatchDeleteSalesData%');
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    kill_loop: LOOP
        FETCH cur INTO process_id;
        IF done THEN
            LEAVE kill_loop;
        END IF;
        
        SET @sql = CONCAT('KILL ', process_id);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SELECT CONCAT('Dayandırıldı: Process ID ', process_id) as stopped_process;
    END LOOP;
    
    CLOSE cur;
    
    SELECT 'SATIŞ SİLMƏ PROSESLƏRİ DAYANDI' as status;
END //

DELIMITER ;

-- Emergency istifadə:
-- CALL StopSalesDeletion();
