-- TAM DÜZƏLDİLMİŞ MALIYYƏ VERİLƏRİ SİLMƏ İCRA ETMƏK

-- =============================================================================
-- BACKUP YARATMAQ (MÜTLƏQ!)
-- =============================================================================

-- Database backup yaradın (database adını dəyişdirin):
-- BACKUP DATABASE [Test_DB] 
-- TO DISK = 'C:\Backup\financial_complete_backup_2025.bak'
-- WITH FORMAT, INIT;

-- =============================================================================
-- TAM DÜZƏLDİLMİŞ MALIYYƏ SİLMƏ PROSEDURUNU İCRA ETMƏK
-- =============================================================================

-- Tam düzəldilmiş proseduru icra edin:
EXEC DeleteFinancialDataComplete @CutoffDate = '2025-01-01', @BatchSize = 500;

-- Əgər yenə də xəta olarsa, daha kiçik batch ilə sınayın:
-- EXEC DeleteFinancialDataComplete @CutoffDate = '2025-01-01', @BatchSize = 200;

-- =============================================================================
-- SİLMƏDƏN SONRA YOXLAMA
-- =============================================================================

-- Qalan veriləri yoxlayın:
SELECT 'BANK VERİLƏRİ' as category,
       COUNT(*) as remaining_records
FROM trBankHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'BANK LINE VERİLƏRİ',
       COUNT(*)
FROM trBankLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'NAĞD PUL VERİLƏRİ',
       COUNT(*)
FROM trCashHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'NAĞD PUL LINE VERİLƏRİ',
       COUNT(*)
FROM trCashLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'ÇEK VERİLƏRİ',
       COUNT(*)
FROM trChequeHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'ÇEK LINE VERİLƏRİ',
       COUNT(*)
FROM trChequeLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'KREDİT KARTI VERİLƏRİ',
       COUNT(*)
FROM trCreditCardPaymentHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'KREDİT KARTI LINE VERİLƏRİ',
       COUNT(*)
FROM trCreditCardPaymentLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'BORC VERİLƏRİ',
       COUNT(*)
FROM trDebitHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'BORC LINE VERİLƏRİ',
       COUNT(*)
FROM trDebitLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'JOURNAL VERİLƏRİ',
       COUNT(*)
FROM trJournalHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'JOURNAL LINE VERİLƏRİ',
       COUNT(*)
FROM trJournalLine 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'PAYMENT VERİLƏRİ',
       COUNT(*)
FROM trPaymentHeader 
WHERE CreatedDate < '2025-01-01'
UNION ALL
SELECT 'PAYMENT LINE VERİLƏRİ',
       COUNT(*)
FROM trPaymentLine 
WHERE CreatedDate < '2025-01-01';

-- Ümumi xülasə:
SELECT 
    'ÜMUMI NƏTİCƏ' as info,
    COUNT(*) as total_remaining_financial_records
FROM (
    SELECT 1 as dummy FROM trBankHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trBankLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCashHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCashLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trChequeHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trChequeLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCreditCardPaymentHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trCreditCardPaymentLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trDebitHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trDebitLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trJournalHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trJournalLine WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trPaymentHeader WHERE CreatedDate < '2025-01-01'
    UNION ALL SELECT 1 FROM trPaymentLine WHERE CreatedDate < '2025-01-01'
) t;

PRINT '';
PRINT '🏦 TAM DÜZƏLDİLMİŞ MALIYYƏ VERİLƏRİNİN SİLMƏSİ TAMAMLANDI!';
PRINT '';
PRINT '🔍 NƏTİCƏLƏRİ YOXLAYIN:';
PRINT '   - Qalan record sayını yoxlayın';
PRINT '   - Mühasibat balansını yoxlayın';
PRINT '   - Sistem performansını test edin';
PRINT '';
PRINT '⚠️  UNUTMAYIN:';
PRINT '   - Mühasibat departamentinə məlumat verin';
PRINT '   - Audit log-larını yoxlayın';
PRINT '   - Backup-ın düzgün olduğunu təsdiq edin';
