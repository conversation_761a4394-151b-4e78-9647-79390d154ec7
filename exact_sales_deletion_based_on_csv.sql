-- Sizin CSV fayllarına əsasən DƏQIQ satış veriləri silmə skripti
-- 2025.01.01-ə qədər satış veriləri silir
-- Parent-Child əlaqələrinə uyğun düzgün ardıcıllıq

-- =============================================================================
-- SİZİN SİSTEMİNİZDƏ SATIŞ TABLE-LARI (CSV-dən müəyyən edilib)
-- =============================================================================

/*
ƏSAS SATIŞ TABLE-LARI:
1. trInvoiceHeader/trInvoiceLine - Faktura (əsas satış sənədi)
2. trOrderHeader/trOrderLine - Sifariş 
3. trShipmentHeader/trShipmentLine - Göndərmə
4. trPaymentHeader/trPaymentLine - Ödəniş
5. trSalesPlan/trSalesPlanChannel/trSalesPlanProduct - Satış planı
6. trOrderAdvancePayments - Sifariş avans ödənişləri

ƏLAQƏLI TABLE-LAR:
- trInvoiceLineBOM, trInvoiceLineSum, trInvoiceLineSumDetail
- trOrderLineBOM, trOrderLineSum, trOrderLineSumDetail  
- trShipmentLineBOM, trShipmentLineSum, trShipmentLineSumDetail
- trInvoiceLineCurrency, trOrderLineCurrency, trPaymentLineCurrency
- trInvoiceLineGiftCard, trShipmentLineGiftCard
- trInvoiceLineLinkedProduct, trOrderLineLinkedProduct
*/

-- =============================================================================
-- SADƏ VƏ EFFEKTIV SİLMƏ PROSEDURU
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteSalesDataExact(IN cutoff_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'XƏTA BAŞ VERDİ - ƏMƏLIYYAT GERİ ALINDI!' as error_message;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    SELECT CONCAT('2025.01.01-ə qədər satış veriləri silmə başladı: ', cutoff_date) as status;
    
    -- =============================================================================
    -- ADDIM 1: ƏN DƏRİN CHILD TABLE-LAR (Detail və Extension table-lar)
    -- =============================================================================
    
    -- Invoice detail table-ları
    DELETE FROM trInvoiceLineSumDetail WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineSumDetail silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineBOM WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineBOM silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineCurrency WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineCurrency silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineGiftCard WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineGiftCard silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineLinkedProduct WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineLinkedProduct silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineReportedSales WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineReportedSales silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trInvoiceLineSubsequentDeliveryOrders WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineSubsequentDeliveryOrders silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order detail table-ları
    DELETE FROM trOrderLineSumDetail WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLineSumDetail silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderLineBOM WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLineBOM silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderLineCurrency WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLineCurrency silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderLineLinkedProduct WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLineLinkedProduct silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderOpticalProductLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderOpticalProductLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Shipment detail table-ları
    DELETE FROM trShipmentLineSumDetail WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLineSumDetail silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentLineBOM WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLineBOM silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentLineGiftCard WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLineGiftCard silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Payment detail table-ları
    DELETE FROM trPaymentLineCurrency WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentLineCurrency silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Sales plan detail table-ları
    DELETE FROM trSalesPlanProductQty WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trSalesPlanProductQty silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT '=== DETAIL TABLE-LAR SİLİNDİ ===' as progress;
    
    -- =============================================================================
    -- ADDIM 2: SUM TABLE-LAR
    -- =============================================================================
    
    DELETE FROM trInvoiceLineSum WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLineSum silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderLineSum WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLineSum silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentLineSum WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLineSum silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT '=== SUM TABLE-LAR SİLİNDİ ===' as progress;
    
    -- =============================================================================
    -- ADDIM 3: LINE TABLE-LAR (CHILD TABLE-LAR)
    -- =============================================================================
    
    -- Invoice line table-ları
    DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order line table-ları
    DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Shipment line table-ları
    DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Payment line table-ları
    DELETE FROM trPaymentLine WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentLine silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order advance payments
    DELETE FROM trOrderAdvancePayments WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderAdvancePayments silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Sales plan products
    DELETE FROM trSalesPlanProduct WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trSalesPlanProduct silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trSalesPlanChannel WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trSalesPlanChannel silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order optical products
    DELETE FROM trOrderOpticalProduct WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderOpticalProduct silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order payment plan
    DELETE FROM trOrderPaymentPlan WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderPaymentPlan silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT '=== LINE TABLE-LAR SİLİNDİ ===' as progress;
    
    -- =============================================================================
    -- ADDIM 4: HEADER TABLE-LAR (PARENT TABLE-LAR)
    -- =============================================================================
    
    -- Invoice header
    DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Order header
    DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Shipment header
    DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Payment header
    DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    -- Sales plan header
    DELETE FROM trSalesPlan WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trSalesPlan silindi: ', ROW_COUNT(), ' record') as progress;
    
    SELECT '=== HEADER TABLE-LAR SİLİNDİ ===' as progress;
    
    SELECT '🎯 2025.01.01-Ə QƏDƏR BÜTÜN SATIŞ VERİLƏRİ UĞURLA SİLİNDİ!' as final_status;
    
    COMMIT;
END //

DELIMITER ;

-- =============================================================================
-- BATCH SİLMƏ VERSİYASI (BÖYÜK VERİ ÜÇÜN)
-- =============================================================================

DELIMITER //

CREATE PROCEDURE DeleteSalesDataBatch(
    IN cutoff_date DATE,
    IN batch_size INT DEFAULT 1000
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    DECLARE total_deleted INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT 'BATCH SİLMƏ XƏTASI!' as error_message;
        RESIGNAL;
    END;
    
    SELECT CONCAT('Batch silmə başladı: ', cutoff_date, ', Batch size: ', batch_size) as status;
    
    -- trInvoiceLine batch silmə (ən böyük table)
    SET total_deleted = 0;
    batch_loop_invoice_line: LOOP
        START TRANSACTION;
        DELETE FROM trInvoiceLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trInvoiceLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_invoice_line;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    -- trOrderLine batch silmə
    SET total_deleted = 0;
    batch_loop_order_line: LOOP
        START TRANSACTION;
        DELETE FROM trOrderLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trOrderLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_order_line;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    -- trShipmentLine batch silmə
    SET total_deleted = 0;
    batch_loop_shipment_line: LOOP
        START TRANSACTION;
        DELETE FROM trShipmentLine WHERE CreatedDate < cutoff_date LIMIT batch_size;
        SET affected_rows = ROW_COUNT();
        SET total_deleted = total_deleted + affected_rows;
        COMMIT;
        
        SELECT CONCAT('trShipmentLine - Bu batch: ', affected_rows, ', Cəmi: ', total_deleted) as progress;
        
        IF affected_rows = 0 THEN
            LEAVE batch_loop_shipment_line;
        END IF;
        
        SELECT SLEEP(0.1);
    END LOOP;
    
    -- Qalan table-ları sadə şəkildə sil
    START TRANSACTION;
    
    DELETE FROM trInvoiceHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trInvoiceHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trOrderHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trOrderHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trShipmentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trShipmentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trPaymentHeader WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trPaymentHeader silindi: ', ROW_COUNT(), ' record') as progress;
    
    DELETE FROM trSalesPlan WHERE CreatedDate < cutoff_date;
    SELECT CONCAT('trSalesPlan silindi: ', ROW_COUNT(), ' record') as progress;
    
    COMMIT;
    
    SELECT '🎯 BATCH SATIŞ SİLMƏ TAMAMLANDI!' as final_status;
END //

DELIMITER ;

-- =============================================================================
-- İSTİFADƏ NÜMUNƏLƏRİ
-- =============================================================================

/*
-- 1. Əvvəl yoxlama edin:
SELECT COUNT(*) as total_invoices FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';
SELECT COUNT(*) as total_orders FROM trOrderHeader WHERE CreatedDate < '2025-01-01';
SELECT COUNT(*) as total_shipments FROM trShipmentHeader WHERE CreatedDate < '2025-01-01';

-- 2. Backup yaradın:
mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine trShipmentHeader trShipmentLine trPaymentHeader trPaymentLine trSalesPlan > sales_backup_2025.sql

-- 3. Kiçik veri üçün (< 100,000 record):
CALL DeleteSalesDataExact('2025-01-01');

-- 4. Böyük veri üçün (> 100,000 record):
CALL DeleteSalesDataBatch('2025-01-01', 1000);

-- 5. Çox böyük veri üçün (> 1,000,000 record):
CALL DeleteSalesDataBatch('2025-01-01', 500);

-- 6. Silmədən sonra yoxlama:
SELECT COUNT(*) as remaining_invoices FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';
SELECT COUNT(*) as remaining_orders FROM trOrderHeader WHERE CreatedDate < '2025-01-01';
*/

-- =============================================================================
-- HAZIR KOMANDALAR
-- =============================================================================

-- Bu əmrləri ardıcıl olaraq icra edin:

-- 1. Yoxlama:
-- SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';

-- 2. Backup:
-- mysqldump -u username -p database_name trInvoiceHeader trInvoiceLine trOrderHeader trOrderLine trShipmentHeader trShipmentLine > backup.sql

-- 3. Silmə:
-- CALL DeleteSalesDataExact('2025-01-01');

-- 4. Yoxlama:
-- SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01';
