-- 2025.01.01-ə qədər satış veriləri silindikdən SONRA yoxlama skripti
-- Bu skript silmə əməliyyatından sonra icra edilməlidir

-- =============================================================================
-- 1. SİLMƏ NƏTİCƏSİ STATİSTİKASI
-- =============================================================================

SELECT 
    '✅ SİLMƏ NƏTİCƏSİ STATİSTİKASI' as section,
    '' as table_name,
    '' as remaining_records,
    '' as size_mb,
    '' as status;

-- Qalan satış veriləri
SELECT 
    '📊 QALAN VERİLƏR' as section,
    'trInvoiceHeader' as table_name,
    COUNT(*) as remaining_records,
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trInvoiceHeader')), 2) as size_mb,
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END as status
FROM trInvoiceHeader
WHERE EXISTS (SELECT 1 FROM trInvoiceHeader LIMIT 1)

UNION ALL

SELECT 
    '📊 QALAN VERİLƏR',
    'trInvoiceLine',
    COUNT(*),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trInvoiceLine')), 2),
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END
FROM trInvoiceLine
WHERE EXISTS (SELECT 1 FROM trInvoiceLine LIMIT 1)

UNION ALL

SELECT 
    '📊 QALAN VERİLƏR',
    'trOrderHeader',
    COUNT(*),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trOrderHeader')), 2),
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END
FROM trOrderHeader
WHERE EXISTS (SELECT 1 FROM trOrderHeader LIMIT 1)

UNION ALL

SELECT 
    '📊 QALAN VERİLƏR',
    'trOrderLine',
    COUNT(*),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trOrderLine')), 2),
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END
FROM trOrderLine
WHERE EXISTS (SELECT 1 FROM trOrderLine LIMIT 1)

UNION ALL

SELECT 
    '📊 QALAN VERİLƏR',
    'trShipmentHeader',
    COUNT(*),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trShipmentHeader')), 2),
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END
FROM trShipmentHeader
WHERE EXISTS (SELECT 1 FROM trShipmentHeader LIMIT 1)

UNION ALL

SELECT 
    '📊 QALAN VERİLƏR',
    'trShipmentLine',
    COUNT(*),
    ROUND(((SELECT (data_length + index_length) / 1024 / 1024 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = 'trShipmentLine')), 2),
    CASE 
        WHEN COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END) = 0 THEN '✅ TƏMİZ'
        ELSE CONCAT('⚠️ ', COUNT(CASE WHEN CreatedDate < '2025-01-01' THEN 1 END), ' köhnə record qaldı')
    END
FROM trShipmentLine
WHERE EXISTS (SELECT 1 FROM trShipmentLine LIMIT 1);

-- =============================================================================
-- 2. ORPHAN RECORD YOXLAMASI
-- =============================================================================

SELECT 
    '🔍 ORPHAN RECORD YOXLAMASI' as section,
    '' as check_type,
    '' as orphan_count,
    '' as status;

-- Invoice line-lar üçün orphan yoxlaması
SELECT 
    '🔍 ORPHAN YOXLAMA' as section,
    'trInvoiceLine -> trInvoiceHeader' as check_type,
    COUNT(*) as orphan_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ ORPHAN YOX'
        ELSE '❌ ORPHAN VAR - TƏMİZLƏMƏK LAZIM'
    END as status
FROM trInvoiceLine il
LEFT JOIN trInvoiceHeader ih ON il.InvoiceID = ih.InvoiceID
WHERE ih.InvoiceID IS NULL
AND EXISTS (SELECT 1 FROM trInvoiceLine LIMIT 1)

UNION ALL

-- Order line-lar üçün orphan yoxlaması
SELECT 
    '🔍 ORPHAN YOXLAMA',
    'trOrderLine -> trOrderHeader',
    COUNT(*),
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ ORPHAN YOX'
        ELSE '❌ ORPHAN VAR - TƏMİZLƏMƏK LAZIM'
    END
FROM trOrderLine ol
LEFT JOIN trOrderHeader oh ON ol.OrderID = oh.OrderID
WHERE oh.OrderID IS NULL
AND EXISTS (SELECT 1 FROM trOrderLine LIMIT 1)

UNION ALL

-- Shipment line-lar üçün orphan yoxlaması
SELECT 
    '🔍 ORPHAN YOXLAMA',
    'trShipmentLine -> trShipmentHeader',
    COUNT(*),
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ ORPHAN YOX'
        ELSE '❌ ORPHAN VAR - TƏMİZLƏMƏK LAZIM'
    END
FROM trShipmentLine sl
LEFT JOIN trShipmentHeader sh ON sl.ShipmentID = sh.ShipmentID
WHERE sh.ShipmentID IS NULL
AND EXISTS (SELECT 1 FROM trShipmentLine LIMIT 1);

-- =============================================================================
-- 3. DATABASE ÖLÇÜSÜ AZALMASI
-- =============================================================================

SELECT 
    '📉 DATABASE ÖLÇÜSÜ AZALMASI' as section,
    '' as info,
    '' as current_size,
    '' as note;

-- Ümumi database ölçüsü
SELECT 
    '📉 DATABASE ÖLÇÜSÜ' as section,
    'Ümumi satış table-ları' as info,
    CONCAT(
        ROUND(SUM((data_length + index_length) / 1024 / 1024), 2), ' MB'
    ) as current_size,
    'Silmədən sonrakı ölçü' as note
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND (table_name LIKE 'trInvoice%' OR table_name LIKE 'trOrder%' OR 
     table_name LIKE 'trShipment%' OR table_name LIKE 'trPayment%');

-- Free space
SELECT 
    '📉 FREE SPACE' as section,
    'Boşaldılmış yer' as info,
    CONCAT(
        ROUND(SUM(data_free / 1024 / 1024), 2), ' MB'
    ) as current_size,
    'OPTIMIZE TABLE icra edin' as note
FROM information_schema.tables 
WHERE table_schema = DATABASE()
AND (table_name LIKE 'trInvoice%' OR table_name LIKE 'trOrder%' OR 
     table_name LIKE 'trShipment%' OR table_name LIKE 'trPayment%')
AND data_free > 0;

-- =============================================================================
-- 4. SİLMƏ LOG ANALİZİ
-- =============================================================================

SELECT 
    '📋 SİLMƏ LOG ANALİZİ' as section,
    '' as table_name,
    '' as total_batches,
    '' as total_deleted,
    '' as last_update;

-- Silmə log-undan statistika
SELECT 
    '📋 SİLMƏ LOG' as section,
    table_name,
    MAX(batch_number) as total_batches,
    MAX(total_deleted) as total_deleted,
    MAX(deletion_time) as last_update
FROM sales_deletion_log 
WHERE cutoff_date = '2025-01-01'
GROUP BY table_name
ORDER BY MAX(total_deleted) DESC;

-- =============================================================================
-- 5. FOREIGN KEY CONSTRAINT YOXLAMASI
-- =============================================================================

SELECT 
    '🔗 FOREIGN KEY CONSTRAINT YOXLAMASI' as section,
    '' as constraint_name,
    '' as status,
    '' as note;

-- Constraint-lərin pozulmadığını yoxlayın
SELECT 
    '🔗 FK CONSTRAINT' as section,
    'Bütün constraint-lər' as constraint_name,
    '✅ SAĞLAM' as status,
    'Əgər bu query işləyirsə, constraint-lər sağlamdır' as note;

-- =============================================================================
-- 6. PERFORMANCE YOXLAMASI
-- =============================================================================

SELECT 
    '⚡ PERFORMANCE YOXLAMASI' as section,
    '' as metric,
    '' as value,
    '' as recommendation;

-- Index istifadəsi
SELECT 
    '⚡ PERFORMANCE' as section,
    'Index fragmentasiyası' as metric,
    'Yoxlanmalıdır' as value,
    'ANALYZE TABLE icra edin' as recommendation

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Table fragmentasiyası',
    'Yoxlanmalıdır',
    'OPTIMIZE TABLE icra edin'

UNION ALL

SELECT 
    '⚡ PERFORMANCE',
    'Query cache',
    'Təmizlənməlidir',
    'RESET QUERY CACHE icra edin';

-- =============================================================================
-- 7. APPLİCATİON TEST TÖVSİYƏLƏRİ
-- =============================================================================

SELECT 
    '🧪 APPLİCATİON TEST TÖVSİYƏLƏRİ' as section,
    '' as test_area,
    '' as action,
    '' as priority;

SELECT 
    '🧪 TEST TÖVSİYƏLƏRİ' as section,
    'Faktura modulları' as test_area,
    'Yeni faktura yaradın və test edin' as action,
    'YÜKSƏK' as priority

UNION ALL

SELECT 
    '🧪 TEST TÖVSİYƏLƏRİ',
    'Sifariş modulları',
    'Yeni sifariş yaradın və test edin',
    'YÜKSƏK'

UNION ALL

SELECT 
    '🧪 TEST TÖVSİYƏLƏRİ',
    'Hesabat modulları',
    'Satış hesabatlarını yoxlayın',
    'ORTA'

UNION ALL

SELECT 
    '🧪 TEST TÖVSİYƏLƏRİ',
    'İstifadəçi interfeysi',
    'Satış ekranlarını test edin',
    'ORTA';

-- =============================================================================
-- 8. OPTIMALLAŞDIRMA KOMANDLARI
-- =============================================================================

SELECT 
    '🔧 OPTIMALLAŞDIRMA KOMANDLARI' as section,
    '' as command,
    '' as purpose,
    '' as execution_time;

SELECT 
    '🔧 OPTIMALLAŞDIRMA' as section,
    'OPTIMIZE TABLE trInvoiceHeader, trInvoiceLine;' as command,
    'Table fragmentasiyasını aradan qaldırır' as purpose,
    '10-30 dəqiqə' as execution_time

UNION ALL

SELECT 
    '🔧 OPTIMALLAŞDIRMA',
    'ANALYZE TABLE trInvoiceHeader, trInvoiceLine;',
    'Query optimizer statistikalarını yeniləyir',
    '1-5 dəqiqə'

UNION ALL

SELECT 
    '🔧 OPTIMALLAŞDIRMA',
    'RESET QUERY CACHE;',
    'Query cache-i təmizləyir',
    '1 saniyə'

UNION ALL

SELECT 
    '🔧 OPTIMALLAŞDIRMA',
    'FLUSH TABLES;',
    'Table cache-i təmizləyir',
    '1 saniyə';

-- =============================================================================
-- 9. SON NƏTİCƏ XÜLASƏSI
-- =============================================================================

SELECT 
    '🎯 SON NƏTİCƏ XÜLASƏSI' as section,
    '' as result,
    '' as status,
    '' as next_action;

SELECT 
    '🎯 NƏTİCƏ' as section,
    '2025.01.01-ə qədər satış veriləri' as result,
    CASE 
        WHEN (
            (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trOrderLine WHERE CreatedDate < '2025-01-01') = 0
        ) THEN '✅ UĞURLA SİLİNDİ'
        ELSE '⚠️ QISMƏN SİLİNDİ - YOXLAYIN'
    END as status,
    CASE 
        WHEN (
            (SELECT COUNT(*) FROM trInvoiceHeader WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trInvoiceLine WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trOrderHeader WHERE CreatedDate < '2025-01-01') = 0 AND
            (SELECT COUNT(*) FROM trOrderLine WHERE CreatedDate < '2025-01-01') = 0
        ) THEN 'OPTIMIZE TABLE icra edin'
        ELSE 'Qalan veriləri yoxlayın və təmizləyin'
    END as next_action;

-- =============================================================================
-- 10. ORPHAN RECORD-LARI TƏMİZLƏMƏK ÜÇÜN KOMANDLAR
-- =============================================================================

/*
-- Əgər orphan record-lar varsa, bunları icra edin:

-- Invoice line orphan-larını təmizləmək:
DELETE il FROM trInvoiceLine il
LEFT JOIN trInvoiceHeader ih ON il.InvoiceID = ih.InvoiceID
WHERE ih.InvoiceID IS NULL;

-- Order line orphan-larını təmizləmək:
DELETE ol FROM trOrderLine ol
LEFT JOIN trOrderHeader oh ON ol.OrderID = oh.OrderID
WHERE oh.OrderID IS NULL;

-- Shipment line orphan-larını təmizləmək:
DELETE sl FROM trShipmentLine sl
LEFT JOIN trShipmentHeader sh ON sl.ShipmentID = sh.ShipmentID
WHERE sh.ShipmentID IS NULL;
*/
